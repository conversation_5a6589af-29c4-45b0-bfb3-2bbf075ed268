<?php

namespace App\Services;

use App\Models\Task\TaskPointConfig;
use App\Models\Task\UserTaskRecord;
use App\Models\User\User;
use App\Models\Membership\MembershipPointRecord;
use App\Models\Enums\Membership\MembershipPointSourceEnum;
use App\Models\Enums\User\WalletChangeTypeEnum;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TaskPointService
{
    /**
     * 执行任务并获得积分
     */
    public function executeTask(
        int $userId,
        string $taskType,
        ?string $channel = null,
        ?array $taskData = null,
        ?string $externalId = null
    ): ?UserTaskRecord {
        $user = User::find($userId);
        if (!$user) {
            return null;
        }

        // 查找任务配置
        $taskConfig = TaskPointConfig::active()
            ->byTaskType($taskType)
            ->byChannel($channel)
            ->first();

        if (!$taskConfig) {
            Log::warning('Task config not found', [
                'task_type' => $taskType,
                'channel' => $channel,
            ]);
            return null;
        }

        // 检查用户是否可以执行此任务
        if (!$taskConfig->canUserExecute($userId)) {
            Log::info('User cannot execute task', [
                'user_id' => $userId,
                'task_type' => $taskType,
                'channel' => $channel,
                'reason' => 'limit_exceeded_or_inactive',
            ]);
            return null;
        }

        DB::beginTransaction();
        try {
            // 创建任务记录
            $taskRecord = UserTaskRecord::createRecord(
                $userId,
                $taskConfig->id,
                $taskType,
                $channel,
                $taskConfig->points_per_action,
                $taskData,
                $externalId
            );

            // 创建积分记录
            $pointRecord = MembershipPointRecord::create([
                'user_id' => $userId,
                'earned_points' => $taskConfig->points_per_action,
                'source_type' => $this->getPointSourceType($taskType),
                'source_id' => $taskRecord->id,
                'description' => $this->getTaskDescription($taskType, $channel),
            ]);

            // 更新用户钱包积分
            userWalletService()->walletChangePoint(
                $user->wallet,
                WalletChangeTypeEnum::TaskReward,
                $taskConfig->points_per_action,
                $this->getTaskDescription($taskType, $channel),
                $pointRecord
            );

            DB::commit();

            Log::info('Task executed successfully', [
                'user_id' => $userId,
                'task_type' => $taskType,
                'channel' => $channel,
                'points' => $taskConfig->points_per_action,
            ]);

            return $taskRecord;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to execute task', [
                'user_id' => $userId,
                'task_type' => $taskType,
                'channel' => $channel,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * 获取用户可执行的任务列表
     */
    public function getUserAvailableTasks(int $userId): array
    {
        $tasks = TaskPointConfig::active()->ordered()->get();
        $availableTasks = [];

        foreach ($tasks as $task) {
            if ($task->canUserExecute($userId)) {
                $availableTasks[] = [
                    'id' => $task->id,
                    'task_type' => $task->task_type,
                    'task_name' => $task->task_name,
                    'channel' => $task->channel,
                    'points_per_action' => $task->points_per_action,
                    'daily_limit' => $task->daily_limit,
                    'total_limit' => $task->total_limit,
                    'today_count' => $task->getUserTodayCount($userId),
                    'total_count' => $task->getUserTotalCount($userId),
                    'description' => $task->description,
                    'extra_config' => $task->extra_config,
                ];
            }
        }

        return $availableTasks;
    }

    /**
     * 获取用户任务统计
     */
    public function getUserTaskStats(int $userId): array
    {
        $today = today();
        $thisMonth = now()->startOfMonth();

        return [
            'today_tasks' => UserTaskRecord::byUser($userId)->today()->count(),
            'today_points' => UserTaskRecord::byUser($userId)->today()->sum('earned_points'),
            'month_tasks' => UserTaskRecord::byUser($userId)->thisMonth()->count(),
            'month_points' => UserTaskRecord::byUser($userId)->thisMonth()->sum('earned_points'),
            'total_tasks' => UserTaskRecord::byUser($userId)->count(),
            'total_points' => UserTaskRecord::byUser($userId)->sum('earned_points'),
        ];
    }

    /**
     * 获取积分来源类型
     */
    private function getPointSourceType(string $taskType): string
    {
        return match ($taskType) {
            'registration' => MembershipPointSourceEnum::Registration->value,
            'birthday' => MembershipPointSourceEnum::Birthday->value,
            default => MembershipPointSourceEnum::Task->value,
        };
    }

    /**
     * 获取任务描述
     */
    private function getTaskDescription(string $taskType, ?string $channel): string
    {
        $taskTypes = TaskPointConfig::getTaskTypeOptions();
        $channels = TaskPointConfig::getChannelOptions();
        
        $taskName = $taskTypes[$taskType] ?? $taskType;
        $channelName = $channel ? ($channels[$channel] ?? $channel) : '';
        
        return $channelName ? "{$channelName}{$taskName}获得积分" : "{$taskName}获得积分";
    }
}
