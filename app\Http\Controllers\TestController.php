<?php

namespace App\Http\Controllers;


use App\Models\User\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Mail;

class TestController extends Controller
{
    //

    public function index(Request $request)
    {
        Mail::raw('测试邮件', function ($message) {
            $message->to('<EMAIL>')
                ->replyTo('<EMAIL>','张三')
                ->subject('测试邮件111111111');
        });
        return response()->json(['res'=>'ok']);
//        User::query()->where('test', 1)->first();
//        return User::query()->first();
//        throw new DataException('您未登录111');
//        return response()->noContent();
//        return JsonResource::collection(User::query()->get());
//
//        abort(Response::HTTP_NOT_IMPLEMENTED, 'cuowu');
//        $request->validate(['name' => 'required']);
        // return JsonResource::make(User::query()->first());
    }

    public function heartbeat(): JsonResponse
    {
        return response()->json();
    }
    public function promotions()
    {

    }
    public function promotionContent($promotion)
    {
        if ($promotion->{'collection_id'}){
            $promotion->{'contents'} = [];
        }
    }

    /**
     * 发送邮件
     * @return void
     */
    public function sendMail()
    {
        
        Mail::raw('测试邮件', function ($message) {
            $message->to('<EMAIL>')
                ->replyTo('<EMAIL>')
                ->subject('测试邮件');
        });
   
    }
}
