<?php

namespace App\Models\Order;

use App\Models\Coupon\Coupon;
use App\Models\User\UserCoupon;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\MiddleModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * @property OrderItemProduct $product
 * @property int $num
 * @property float $coupon_discount_price
 * @property float $price
 */
class OrderItem extends MiddleModel
{
    use HasFactory;

    protected $guarded = [];


    protected $casts = [
        'price' => 'decimal:4',
        'original_price' => 'decimal:4',
        'coupon_discount_price' => 'decimal:4',
    ];

    protected $appends = [
        'total_price',
        'total_price_agent',
        'price_agent',
        'original_price_agent',
        'coupon_discount_price_agent',
        'sell_price',
        'sell_price_agent'
    ];

    public function totalPrice(): Attribute
    {
        return Attribute::get(
            fn() => $this->num * $this->price
        );
    }
    public function sellPrice(): Attribute
    {
        return Attribute::get(
            fn() => round(($this->price * $this->num) - $this->coupon_discount_price, 2)
        );
    }

    /**************** 金额币种转换 ****************/
    public function sellPriceAgent(): Attribute
    {
        return Attribute::get(
            fn() => convertPrice(($this->price * $this->num) - $this->coupon_discount_price, currentCurrency())
        );
    }

    /**************** 金额币种转换 ****************/
    public function totalPriceAgent(): Attribute
    {
        return Attribute::get(
            fn() => convertPrice($this->num * $this->price, $this->order->currency)
        );
    }

    public function priceAgent(): Attribute
    {
        return Attribute::get(
            fn() => convertPrice($this->price, $this->order->currency)
        );
    }

    public function originalPriceAgent(): Attribute
    {
        return Attribute::get(
            fn() => convertPrice($this->original_price * $this->num, $this->order->currency)
        );
    }

    public function couponDiscountPriceAgent(): Attribute
    {
        return Attribute::get(
            fn() => convertPrice($this->coupon_discount_price, $this->order->currency)
        );
    }

    /**************** 关联模型 ****************/
    public function product(): MorphTo
    {
        return $this->morphTo();
    }

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    public function coupon(): BelongsTo
    {
        return $this->belongsTo(Coupon::class);
    }

    public function userCoupon(): BelongsTo
    {
        return $this->belongsTo(UserCoupon::class);
    }
}
