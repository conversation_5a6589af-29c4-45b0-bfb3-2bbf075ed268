<?php

namespace Tests\Feature;

use App\Models\Task\TaskPointConfig;
use App\Models\Task\UserTaskRecord;
use App\Models\User\User;
use App\Services\TaskPointService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class TaskPointSystemTest extends TestCase
{
    use RefreshDatabase;

    private TaskPointService $taskPointService;
    private User $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->taskPointService = app(TaskPointService::class);
        $this->user = User::factory()->create();
        
        // 创建测试任务配置
        $this->createTestTaskConfigs();
    }

    private function createTestTaskConfigs(): void
    {
        TaskPointConfig::create([
            'task_type' => 'check_in',
            'task_name' => '签到',
            'channel' => null,
            'points_per_action' => 5,
            'daily_limit' => 1,
            'total_limit' => 0,
            'is_active' => true,
            'description' => '每日签到奖励',
            'sort_order' => 1,
        ]);

        TaskPointConfig::create([
            'task_type' => 'wechat_follow',
            'task_name' => '关注微信',
            'channel' => 'wechat',
            'points_per_action' => 50,
            'daily_limit' => 0,
            'total_limit' => 1,
            'is_active' => true,
            'description' => '关注官方微信公众号',
            'sort_order' => 2,
        ]);

        TaskPointConfig::create([
            'task_type' => 'fb_share',
            'task_name' => '在FB上分享',
            'channel' => 'facebook',
            'points_per_action' => 10,
            'daily_limit' => 3,
            'total_limit' => 0,
            'is_active' => true,
            'description' => '在Facebook上分享内容',
            'sort_order' => 3,
        ]);
    }

    /** @test */
    public function it_can_execute_task_successfully()
    {
        $taskRecord = $this->taskPointService->executeTask(
            $this->user->id,
            'check_in'
        );

        $this->assertNotNull($taskRecord);
        $this->assertEquals($this->user->id, $taskRecord->user_id);
        $this->assertEquals('check_in', $taskRecord->task_type);
        $this->assertEquals(5, $taskRecord->earned_points);
    }

    /** @test */
    public function it_respects_daily_limit()
    {
        // 第一次签到成功
        $firstRecord = $this->taskPointService->executeTask(
            $this->user->id,
            'check_in'
        );
        $this->assertNotNull($firstRecord);

        // 第二次签到失败（达到每日限制）
        $secondRecord = $this->taskPointService->executeTask(
            $this->user->id,
            'check_in'
        );
        $this->assertNull($secondRecord);
    }

    /** @test */
    public function it_respects_total_limit()
    {
        // 第一次关注成功
        $firstRecord = $this->taskPointService->executeTask(
            $this->user->id,
            'wechat_follow',
            'wechat'
        );
        $this->assertNotNull($firstRecord);
        $this->assertEquals(50, $firstRecord->earned_points);

        // 第二次关注失败（达到总限制）
        $secondRecord = $this->taskPointService->executeTask(
            $this->user->id,
            'wechat_follow',
            'wechat'
        );
        $this->assertNull($secondRecord);
    }

    /** @test */
    public function it_can_execute_multiple_times_within_limit()
    {
        // 第一次分享
        $firstRecord = $this->taskPointService->executeTask(
            $this->user->id,
            'fb_share',
            'facebook'
        );
        $this->assertNotNull($firstRecord);

        // 第二次分享
        $secondRecord = $this->taskPointService->executeTask(
            $this->user->id,
            'fb_share',
            'facebook'
        );
        $this->assertNotNull($secondRecord);

        // 第三次分享
        $thirdRecord = $this->taskPointService->executeTask(
            $this->user->id,
            'fb_share',
            'facebook'
        );
        $this->assertNotNull($thirdRecord);

        // 第四次分享失败（达到每日限制3次）
        $fourthRecord = $this->taskPointService->executeTask(
            $this->user->id,
            'fb_share',
            'facebook'
        );
        $this->assertNull($fourthRecord);
    }

    /** @test */
    public function it_returns_null_for_non_existent_task()
    {
        $taskRecord = $this->taskPointService->executeTask(
            $this->user->id,
            'non_existent_task'
        );

        $this->assertNull($taskRecord);
    }

    /** @test */
    public function it_can_get_user_available_tasks()
    {
        $availableTasks = $this->taskPointService->getUserAvailableTasks($this->user->id);

        $this->assertCount(3, $availableTasks);
        
        // 检查任务信息
        $checkInTask = collect($availableTasks)->firstWhere('task_type', 'check_in');
        $this->assertNotNull($checkInTask);
        $this->assertEquals('签到', $checkInTask['task_name']);
        $this->assertEquals(5, $checkInTask['points_per_action']);
        $this->assertEquals(1, $checkInTask['daily_limit']);
        $this->assertEquals(0, $checkInTask['today_count']);
    }

    /** @test */
    public function it_can_get_user_task_stats()
    {
        // 执行一些任务
        $this->taskPointService->executeTask($this->user->id, 'check_in');
        $this->taskPointService->executeTask($this->user->id, 'wechat_follow', 'wechat');

        $stats = $this->taskPointService->getUserTaskStats($this->user->id);

        $this->assertEquals(2, $stats['today_tasks']);
        $this->assertEquals(55, $stats['today_points']); // 5 + 50
        $this->assertEquals(2, $stats['total_tasks']);
        $this->assertEquals(55, $stats['total_points']);
    }

    /** @test */
    public function task_config_can_check_user_limits()
    {
        $checkInConfig = TaskPointConfig::where('task_type', 'check_in')->first();
        
        // 初始状态可以执行
        $this->assertTrue($checkInConfig->canUserExecute($this->user->id));
        $this->assertTrue($checkInConfig->canUserExecuteToday($this->user->id));
        $this->assertTrue($checkInConfig->canUserExecuteTotal($this->user->id));

        // 执行一次后
        $this->taskPointService->executeTask($this->user->id, 'check_in');
        
        // 不能再执行（达到每日限制）
        $this->assertFalse($checkInConfig->canUserExecute($this->user->id));
        $this->assertFalse($checkInConfig->canUserExecuteToday($this->user->id));
        $this->assertTrue($checkInConfig->canUserExecuteTotal($this->user->id)); // 总限制为0（无限制）
    }

    /** @test */
    public function it_can_get_user_task_counts()
    {
        $checkInConfig = TaskPointConfig::where('task_type', 'check_in')->first();
        
        // 初始计数为0
        $this->assertEquals(0, $checkInConfig->getUserTodayCount($this->user->id));
        $this->assertEquals(0, $checkInConfig->getUserTotalCount($this->user->id));

        // 执行任务后
        $this->taskPointService->executeTask($this->user->id, 'check_in');
        
        $this->assertEquals(1, $checkInConfig->getUserTodayCount($this->user->id));
        $this->assertEquals(1, $checkInConfig->getUserTotalCount($this->user->id));
    }
}
