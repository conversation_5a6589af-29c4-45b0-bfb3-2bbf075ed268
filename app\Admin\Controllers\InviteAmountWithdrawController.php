<?php

namespace App\Admin\Controllers;

use App\Constants\Permissions;
use App\Http\Controllers\Controller;
use App\Models\InviteAmountWithdraw;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

class InviteAmountWithdrawController extends Controller
{
    public function __construct()
    {
        $this->hasPermissionOr(Permissions::WithdrawsIndex)->only(['index', 'show']);
    }

    /**
     * 提现记录列表
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $builder = QueryBuilder::for(InviteAmountWithdraw::class, $request)
            ->with([
                'user:id,first_name,last_name,email,inviters_rule_id',
                'user.invitersRule:id,title'
            ])
            ->allowedFilters([
                AllowedFilter::exact('status'),
                AllowedFilter::partial('user.email'),
            ])
            ->allowedSorts(['id', 'created_at'])
            ->defaultSort('-created_at');

        // 创建时间筛选
        if ($request->has('start_date') && $request->start_date) {
            $builder->whereDate('created_at', '>=', $request->start_date);
        }
        if ($request->has('end_date') && $request->end_date) {
            $builder->whereDate('created_at', '<=', $request->end_date);
        }

        $res = $builder->paginate($this->getPerPage());

        return JsonResource::collection($res);
    }
} 