<?php

namespace App\Services;

use App\Models\Attachment;
use App\Models\Enums\AttachmentModuleEnum;
use App\Models\Product\Product;
use App\Models\Product\ProductKeyword;
use App\Models\User\User;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\HigherOrderWhenProxy;
use Illuminate\Support\Str;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;
use Throwable;
use Illuminate\Support\Facades\Cache;
use App\Constants\CacheKey;

class ProductService
{
    /**
     * 商品列表builder
     * @param Request $request
     * @param User|null $user
     * @return HigherOrderWhenProxy|QueryBuilder
     */
    public function getIndexQueryBuilder(Request $request, ?User $user = null): QueryBuilder|HigherOrderWhenProxy
    {
        return QueryBuilder::for(Product::class, $request)
            ->with([
                'image:id,path,disk,module',
                'variants' => function ($query) {
                    $query->select(['id', 'product_id', 'price', 'image_id', 'original_price', 'color_attribute_value_id', 'size_attribute_value_id', 'stock'])
                        ->where('is_publish', true)
                        ->orderBy('sort', 'desc'); // 根据 sort 字段降序排序
                },
                'variants.colorAttribute',
                'variants.sizeAttribute',
                'variants.image:id,path,disk,module',
                'variants.images:id,path,disk,module',
                'colors.color:id,value,extra',
                'colors.images:id,path,disk,module'
            ])
            ->where(function ($query) {
                $query->where('is_publish', true)
                    ->orWhere(function ($q) {
                        $q->where('is_publish', false)
                            ->whereNotNull('publish_at')
                            ->where('publish_at', '<=', now());
                    });
            })
            ->when($user, function (Builder $query, User $user) {
                // 是否收藏
                $query->withExists([
                    'collectUserIds' => function ($builder) use ($user) {
                        $builder->where('user_id', $user->id);
                    }
                ]);
            })
            ->allowedIncludes([
                'commentStatic'
            ])
            ->allowedFilters([
                AllowedFilter::exact('color_attribute_value_id', 'variants.color_attribute_value_id'),
                AllowedFilter::exact('size_attribute_value_id', 'variants.size_attribute_value_id'),
                AllowedFilter::exact('collection_ids', 'collections.id'),
                AllowedFilter::exact('category_id'),
                AllowedFilter::exact('style_id'),
                AllowedFilter::exact('brand_id'),
                AllowedFilter::exact('material_id'),
                AllowedFilter::exact('ids', 'id'),
                AllowedFilter::partial('title'),
                AllowedFilter::callback('collected', function (Builder $q, $value) use ($user) {
                    if ($value && $user) {
                        $q->whereHas('collectUserIds', function (Builder $builder) use ($user, $value) {
                            $builder->where('user_id', $user->id);
                        });
                    }
                }),
                AllowedFilter::exact('spu'),
                AllowedFilter::exact('is_new'),
                AllowedFilter::exact('is_featured'),
                AllowedFilter::callback('keyword', function (Builder $q, $value) {
                    if (!$value) {
                        return;
                    }
                    // 关键词搜搜 颜色匹配命中要看靠生成机制
                    $value = explode(' ', strtolower($value));
                    $baseMatch = implode(' ', array_map(fn($v) => "{$v}*", $value));
                    $colorMatch = implode(' ', array_map(fn($v): string => "color{$v}", $value));
                    $sizeMatch = implode(' ', array_map(fn($v) => "size{$v}", $value));
                    $sourceColumn =
                        /** @lang text */
                        <<<SQL
                        MATCH(title) AGAINST ('{$baseMatch}' IN BOOLEAN MODE)  +
                        MATCH(slug_title) AGAINST ('{$baseMatch}' IN BOOLEAN MODE)  +
                        MATCH(category_name) AGAINST ('{$baseMatch}' IN BOOLEAN MODE) +
                        MATCH(material_name) AGAINST ('{$baseMatch}' IN BOOLEAN MODE)  +
                        MATCH(sku) AGAINST ('{$baseMatch}' IN BOOLEAN MODE)  +
                        MATCH(collection_names) AGAINST ('{$baseMatch}' IN BOOLEAN MODE)  +
                        MATCH(colors) AGAINST ('{$colorMatch}' IN BOOLEAN MODE)   +
                        MATCH(sizes) AGAINST ('{$sizeMatch}' IN BOOLEAN MODE)
SQL;
                    $res = ProductKeyword::query()
                        ->select([
                            'product_id',
                            DB::raw($sourceColumn . '  as like_score')
                        ])
                        ->whereRaw("{$sourceColumn} > 0")
                        ->orderByDesc(DB::raw($sourceColumn))
                        ->limit(2000)
                        ->get();
                    $productIds = $res->pluck('product_id')->toArray();
                    $q->whereIn('id', $productIds);
                    if ($productIds) {
                        $sortString = implode(",", $productIds);
                        $q->orderByRaw("FIELD (id,{$sortString})");
                    }
                }),
                AllowedFilter::callback('price', function (Builder $q, $value) {
                    $values = array_filter(Arr::wrap($value));
                    if (!empty($values)) {
                        $q->whereHas('variants', function (Builder $query) use ($values) {
                            $min = convertPrice(Arr::get($values, 0), fromCurrency: currentCurrency());
                            if (is_numeric($min)) {
                                $query->where('price', '>=', $min);
                            }
                            $max = convertPrice(Arr::get($values, 1), fromCurrency: currentCurrency());
                            if (is_numeric($max)) {
                                $query->where('price', '<=', $max);
                            }
                        });
                    }
                }),
                AllowedFilter::callback('productIds', function (Builder $q, $value) {
                    if (!empty($value)) {
                        $productIds = array_filter(Arr::wrap($value));
                        $q->whereIn('id', $productIds);
                    }
                }),
            ])
            ->allowedSorts([
                'min_price',
                'max_price',
                'first_publish_at',
                'new_publish_at',
                'title',
                'id',
                'sale_num',
                'is_featured'
            ])
            ->defaultSort(['-is_featured', '-new_publish_at']);
    }

    /**
     * 根据URL上传文件
     * @param string $imageUrl
     * @return Attachment|bool|Builder<Attachment>|object|\Illuminate\Database\Eloquent\Model
     */
    public function uploadFileFromUrl(string $imageUrl): Builder|bool|Attachment
    {
        $response = Http::get($imageUrl);
        // 检查请求是否成功
        if ($response->failed()) {
            return false;
        }
        // 获取文件内容
        $fileContent = $response->body();
        // 提取文件名和扩展名
        $fileName = basename(parse_url($imageUrl, PHP_URL_PATH));
        $extension = pathinfo($fileName, PATHINFO_EXTENSION);
        // 创建临时文件路径
        $directory = 'temp/uploads/';
        // 使用 Laravel Storage 类创建文件夹（如果不存在）
        if (!Storage::disk('local')->exists($directory)) {
            Storage::disk('local')->makeDirectory($directory);
        }
        $tempFilePath = Storage::disk('local')->path($directory . uniqid() . '.' . $extension);

        // 将文件内容写入临时文件
        file_put_contents($tempFilePath, $fileContent);
        try {
            $file = new UploadedFile(
                $tempFilePath,
                $fileName,
                mime_content_type($tempFilePath),
                null,
                true // 表示文件已移动 (无需再移动)
            );
            $imagePath = AttachmentModuleEnum::Product->getPath();
            $fileHash = md5_file($file->getRealPath());  // 获取文件的哈希值
            $disk = config('filesystems.default');
            $existingFile = Attachment::query()
                ->where('file_hash', $fileHash)
                ->where('module', AttachmentModuleEnum::Product)
                ->where('disk', $disk)
                ->first();
            if ($existingFile) {
                return $existingFile;
            }
            $src = $file->store($imagePath, $disk);
            $attachment = new Attachment();
            $attachment->fill([
                'file_name' => $fileName,
                'file_type' => $file->getMimeType(),
                'file_size' => $file->getSize(),
                'upload_time' => now(),
                'file_hash' => $fileHash,
                'module' => AttachmentModuleEnum::Product,
                'disk' => $disk,
                'path' => Str::replace($imagePath, '', $src),
                'user_id' => null,
            ])->save();
            return $attachment;
        } catch (Throwable $exception) {
        } finally {
            unlink($tempFilePath);
        }
        return false;
    }

    /**
     * 根据本地路径上传文件
     */
    public function uploadFileFromLocalPath(string $localPath, string $disk = 'public')
    {
        try {
            // 获取文件名
            $path = Storage::disk($disk)->path($localPath);
            $fileName = basename($path);
            // 创建 UploadedFile 对象
            $file = new UploadedFile(
                $path,
                $fileName,
                mime_content_type($path),
                null,
                true // 表示文件已移动 (无需再移动)
            );
            // 目的路径
            $imagePath = AttachmentModuleEnum::Product->getPath();
            $fileHash = md5_file($file->getRealPath());  // 获取文件的哈希值
            $disk = config('filesystems.default');
            // 是否已存在当前图片
            $existingFile = Attachment::query()
                ->where('file_hash', $fileHash)
                ->where('module', AttachmentModuleEnum::Product)
                ->where('disk', $disk)
                ->first();
            // 存在返回图片对象
            if ($existingFile) {
                return $existingFile;
            }
            // 不存在则保存图片
            $src = $file->store($imagePath, $disk);
            // 创建图片对象
            $attachment = new Attachment();
            $attachment->fill([
                'file_name' => $fileName,
                'file_type' => $file->getMimeType(),
                'file_size' => $file->getSize(),
                'upload_time' => now(),
                'file_hash' => $fileHash,
                'module' => AttachmentModuleEnum::Product,
                'disk' => $disk,
                'path' => Str::replace($imagePath, '', $src),
                'user_id' => null,
            ])->save();

            return $attachment;
        } catch (Throwable $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * 根据目录上传文件
     * @param string $localDir
     * @param string $disk
     * @throws \Exception
     * @return array|Attachment|Builder<Attachment>|object|\Illuminate\Database\Eloquent\Model
     */
    public function uploadFileFromLocalDir(string $localDir, string $disk = 'public')
    {
        try {
            $files = Storage::disk($disk)->allFiles($localDir);
            $images = [];
            foreach ($files as $file) {
                $images[] = $this->uploadFileFromLocalPath($file, $disk);
            }

            return $images;
        } catch (Throwable $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * 清除商品相关缓存
     * @param Product $product
     * @return void
     */
    public function clearProductCache(Product $product): void
    {
        // 清除商品列表缓存
        cachePartialDelete(CacheKey::ProductSearchList->getKey('*'));
        // 清除商品详情缓存
        Cache::forget(CacheKey::Productnfo->getKey(md5(json_encode(['id' => $product->id]))));
        Cache::forget(CacheKey::Productnfo->getKey(md5(json_encode(['spu' => $product->spu]))));
        Cache::forget(CacheKey::Productnfo->getKey(md5(json_encode(['slug_title' => $product->slug_title]))));
    }
}
