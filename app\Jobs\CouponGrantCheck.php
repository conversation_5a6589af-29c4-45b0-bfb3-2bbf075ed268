<?php

namespace App\Jobs;

use App\Models\CouponGrantRule;
use App\Models\Enums\CouponGrantType;
use App\Services\CouponGrant\CouponGrantService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class CouponGrantCheck implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 检查是否满足优惠券发放规则
     */
    public function __construct(public CouponGrantType $type, public Model $model)
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {   
        $now = now();
        // 获取正常使用的规则
        CouponGrantRule::query()
            ->where('type', $this->type)
            ->with(['coupon'])
            ->where('enabled', true)
            ->where(function($query) use ($now) {
                $query->where(function($q) use ($now) {
                    $q->where('effective_start_at', '<=', $now);
                    $q->where('effective_end_at', '>=', $now);
                });
            })
            ->where(function($query) {
                $query->whereNull('total_count')
                      ->orWhereRaw('total_count > grant_count');
            })
            ->chunkMap(function (CouponGrantRule $rule) {
                // 获取发放优惠券工厂
                $couponGrantService = CouponGrantService::getInstance($rule, $this->model);
                // 发放
                $couponGrantService->grant();
            });
    }

}
