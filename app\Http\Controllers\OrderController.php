<?php

namespace App\Http\Controllers;


use App\Constants\ErrorCode;
use App\Exceptions\DataException;
use App\Models\Cart;
use App\Models\CartItem;
use App\Models\Country;
use App\Models\Enums\Order\OrderAddressTypeEnum;
use App\Models\Enums\Order\OrderPaidStatusEnum;
use App\Models\Enums\Order\OrderPaidTypeEnum;
use App\Models\Enums\Order\OrderStatusEnum;
use App\Models\Enums\User\UserEmailPreferencesEnum;
use App\Models\Order\FastPaypalOrder;
use App\Models\Order\Order;
use App\Models\Order\OrderAddress;
use App\Models\Order\OrderItem;
use App\Models\Order\Payment\AirwallexOrder;
use App\Models\Order\Payment\PaypalOrder;
use App\Models\Product\ProductVariant;
use App\Models\UserExperienceActivity;
use App\Models\User\User;
use App\Models\CouponGrantRule;
use App\Models\Enums\CouponGrantType;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Enum;
use Illuminate\Validation\Rules\Exists;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;
use App\Models\User\UserCoupon;
use App\Jobs\EmailRuleNotice;
use App\Constants\QueueKey;
use App\Models\Enums\EmailRuleEventEnum;
use Throwable;

class OrderController extends Controller
{

    /**
     * 订单列表
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        /**
         * @var $user User|null
         */
        $user = Auth::user();
        $builder = orderService()->getIndexQueryBuilder($request)
            ->where('user_id', $user->id)
            ->with(['items.coupon']);

        $res = $builder->paginate($this->getPerPage());
        return JsonResource::collection($res);
    }

    public function show(Order $order): JsonResource
    {
        /**
         * @var $user User|null
         */
        $user = Auth::user();
        if ($order->user_id) {
            // 非本人订单
            if (!$user || $user->id !== $order->user_id) {
                throw new DataException('Non personal orders cannot be viewed.', ErrorCode::HttpNotFound);
            }
        }
        $order->loadMissing([
            'items.product' => function (MorphTo $query) {
                $query->morphWith([
                    ProductVariant::class => ['colorAttribute', 'sizeAttribute'],
                ]);
            },
            'items.userCoupon:id,used_at,code,coupon_id',
            'items.userCoupon.coupon',
        ]);
        return JsonResource::make($order);
    }

    /**
     * 通过已支付订单快速创建用户
     * @param Order $order
     * @param Request $request
     * @return JsonResource
     * @throws DataException
     */
    public function fastRegisterUser(Order $order, Request $request): JsonResource
    {
        // 被注册过不行
        if ($order->user_id || !$order->email) {
            throw new DataException('Operation error.');
        }
        // 需要订单支付完成的
        if ($order->paid_status != OrderPaidStatusEnum::Paid) {
            throw new DataException('Order unpaid.');
        }
        // 创建用户
        $user = userService()->fastCreateUserByEmail($order->email);
        // 更新订阅
        if ($order->subscribed) {
            $user->update([
                'email_preferences' => UserEmailPreferencesEnum::FewTimesWeek
            ]);
        }

        // 订单绑定用户
        $order->user()->associate($user)->save();
        $order->loadMissing(['billingAddress', 'shippingAddress']);
        if ($order->billingAddress->is_save) {
            $base = $order->billingAddress->toArray();
            $user->addresses()->create([
                ...Arr::only($base, ['first_name', 'last_name', 'country_id', 'address', 'address1', 'state', 'city', 'zip', 'phone']),
                'is_billing_default' => true
            ]);
        }
        if ($order->shippingAddress->is_save) {
            $base = $order->shippingAddress->toArray();
            $user->addresses()->create([
                ...Arr::only($base, ['first_name', 'last_name', 'country_id', 'address', 'address1', 'state', 'city', 'zip', 'phone']),
                'is_shipping_default' => true
            ]);
        }

        $expireAt = now()->addDays(30);
        $token = $user->createToken('admin', ['*'], $expireAt);
        return JsonResource::make($user)->additional([
            'order' => $order,
            'token' => $token->plainTextToken,
            'expire_at' => $expireAt
        ]);
    }

    /**
     * 从支付页提交订单并支付
     * @param Request $request
     * @return JsonResource
     * @throws DataException
     * @throws Throwable
     */
    public function paymentByCart(Request $request): JsonResource
    {
        /**
         * @var $user User|null
         */
        $user = Auth::user();
        $validated = $request->validate([
            'session_uuid' => 'uuid',
            // 未登录不是paypal支付必须要邮件
            'email' => [
                Rule::requiredIf(function () use ($user, $request) {
                    return !$user && $request->input('paid_type') !== OrderPaidTypeEnum::PayPal->value;
                }),
                'email',
                // new Unique(User::class, 'email')
                Rule::requiredIf(function () use ($user, $request) {
                    return !$user && User::where('email', $request->input('email'))->exists();
                }),
            ],
            'subscribed' => [
                Rule::requiredIf(function () use ($user, $request) {
                    return !$user && $request->input('paid_type') !== OrderPaidTypeEnum::PayPal->value;
                }),
                'bool'
            ],
            'paid_type' => ['required', new Enum(OrderPaidTypeEnum::class)],
            // 配送地址
            'shipping_address' => [
                'array',
                Rule::requiredIf(function () use ($request) {
                    return $request->input('paid_type') !== OrderPaidTypeEnum::PayPal->value;
                })
            ],
            'shipping_address.first_name' => 'required_with:shipping_address|string|max:64',
            'shipping_address.last_name' => 'required_with:shipping_address|string|max:64',
            'shipping_address.country_id' => ['required_with:shipping_address', new Exists(Country::class, 'id')],
            'shipping_address.address' => 'string|max:512',
            'shipping_address.address1' => 'nullable|string|max:512',
            'shipping_address.state' => 'string|max:512',
            'shipping_address.is_save' => 'bool',
            'shipping_address.city' => 'required_with:shipping_address|max:512',
            'shipping_address.zip' => 'required_with:shipping_address|max:512',
            'shipping_address.phone' => 'required_with:shipping_address|string|max:32',
            // 账单地址
            'billing_address' => [
                'array',
                Rule::requiredIf(function () use ($request) {
                    return $request->input('paid_type') !== OrderPaidTypeEnum::PayPal->value;
                })
            ],
            'billing_address.first_name' => 'required_with:billing_address|string|max:64',
            'billing_address.last_name' => 'required_with:billing_address|string|max:64',
            'billing_address.country_id' => ['required_with:billing_address', new Exists(Country::class, 'id')],
            'billing_address.address' => 'string|max:512',
            'billing_address.address1' => 'nullable|string|max:512',
            'billing_address.state' => 'string|max:512',
            'billing_address.is_save' => 'bool',
            'billing_address.city' => 'required_with:billing_address|max:512',
            'billing_address.zip' => 'required_with:billing_address|max:512',
            'billing_address.phone' => 'required_with:billing_address|string|max:32',
        ]);
        $session_uuid = Arr::get($validated, 'session_uuid');
        $cart = userService()->getOrCreateCart($user, $session_uuid);
        if (!$cart instanceof Cart) {
            throw new DataException("You have not added any items to your shopping cart yet.");
        }
        //检查购物车运费
        $country = Country::find(Arr::get($validated, 'shipping_address.country_id'))->iso_code;
        cartService()->refreshCartShipping($cart, $country);
        // 获取购物车列表
        $cartItems = $cart->items()->with(['productVariant.product',])->get();
        if ($cartItems->isEmpty()) {
            throw new DataException("There is no product in your shopping cart yet.");
        }

        // 保存结账信息到购物车
        $shipping_address = Arr::get($validated, 'shipping_address', []);
        $shipping_address['country_code'] = Country::find($shipping_address['country_id'])->iso_code;
        $billing_address = Arr::get($validated, 'billing_address', []);
        $billing_address['country_code'] = Country::find($billing_address['country_id'])->iso_code;
        $checkoutData = [
            'email' => Arr::get($validated, 'email', null),
            'paid_type' => Arr::get($validated, 'paid_type'),
            'shipping_address' => $billing_address,
            'billing_address' => $billing_address,
            'user_id' => $user ? $user->id : null,
            'subscribed' => Arr::get($validated, 'subscribed', false),
            'timestamp' => now()->timestamp,
        ];
        $cart->update([
            'checkout_data' => $checkoutData,
            'is_checkout_processing' => true
        ]);

        $order = new Order();
        DB::beginTransaction();
        try {
            if ($user) {
                $order->user()->associate($user);
            }
            $orderItems = [];
            $discount = $coupon_discount = $totalPrice = $subtotalPrice = 0;
            foreach ($cartItems as $item) {
                /**
                 * @var ProductVariant $product
                 * @var CartItem $item
                 */
                $product = $item->productVariant;
                $orderItem = new OrderItem([
                    'num' => $item->num,
                    'name' => $product->getName(),
                    'price' => $item->price,  // 购物车中售价
                    'coupon_discount_price' => $item->discount, // 购物车中优惠(购物车优惠部分)
                    'coupon_id' => $item->coupon_id, // 购物车中优惠(购物车优惠部分)
                    'user_coupon_id' => null, // 默认为null，下面会检查并设置
                    'original_price' => $product->getOriginalPrice(), // 商品原价
                    'image_url' => $product->getImageUrl(),
                    'product_info' => json_encode($product->getProductInfo()), // 商品信息
                    'is_activity' => $item->is_activity,
                    'user_experience_activity_id' => $item->user_experience_activity_id,
                ]);
                // 如果有优惠券ID且用户已登录，检查并关联用户优惠券
                if ($item->coupon_id && $user) {
                    $userCoupon = UserCoupon::where('coupon_id', $item->coupon_id)
                        ->where('user_id', $user->id)
                        ->whereNull('used_at')
                        ->first();
                    if ($userCoupon) {
                        $orderItem->user_coupon_id = $userCoupon->id;
                    }
                }

                if ($item->is_activity && !$user) {
                    $user_experience_activity_id = $item->user_experience_activity_id;
                    //查看当前活动是否需要登录
                    $user_experience_activity = UserExperienceActivity::find($user_experience_activity_id);
                    if ($user_experience_activity->is_register == 1) {
                        throw new DataException('The current discounted price requires login.');
                    }
                }
                $subtotalPrice += $orderItem->num * $orderItem->original_price;
                $totalPrice += $orderItem->num * $orderItem->price - $item->discount;
                $discount += $item->original_price - $item->price + $item->discount;
                $coupon_discount += $item->discount;
                $orderItem->product()->associate($product);
                $orderItems[] = $orderItem;
            }
            // 保存订单
            $order->fill([
                'email' => Arr::get($validated, 'email', null),
                'paid_type' => Arr::get($validated, 'paid_type'),
                'subtotal_price' => $subtotalPrice,
                'total_price' => $totalPrice,
                'status' => OrderStatusEnum::Unpaid,
                'discount_price' => $discount, // 总折扣
                'coupon_discount_price' => $coupon_discount, // 优惠券折扣
                'total' => $totalPrice + $cart->delivery_fee - $cart->delivery_fee_discount, // 订单总金额
                'shipping_id' => $cart->shipping_id,  // 购物车中运费ID
                'shipping_type' => $cart->shipping_type,  // 购物车中运费类型
                'shipping_fee' => $cart->delivery_fee,  // 购物车中运费
                'shipping_fee_discount' => $cart->delivery_fee_discount,
                'shipping_fee_discount_id' => $cart->delivery_fee_discount_id,
                'currency_id' => currentCurrency()->id // 支付货币ID
            ])->save();
            // 保存订单内容
            $order->items()->saveMany($orderItems);
            // 绑定账单地址
            $billingAddress = Arr::pull($validated, 'billing_address');
            if ($billingAddress) {
                $billingAddress = (new OrderAddress($billingAddress))->fill(['type' => OrderAddressTypeEnum::Billing]);
                $order->billingAddress()->save($billingAddress);
                // 同步账单地址到用户
                if ($billingAddress->is_save) {
                    $order->syncAddressToUser($order->billingAddress, true);
                }
            }
            // 绑定物流地址
            $shippingAddress = Arr::pull($validated, 'shipping_address');
            if ($shippingAddress) {
                $shippingAddress = (new OrderAddress($shippingAddress))->fill(['type' => OrderAddressTypeEnum::Shipping]);
                $order->shippingAddress()->save($shippingAddress);
                // 同步物流地址到用户
                if ($shippingAddress->is_save) {
                    $order->syncAddressToUser($order->shippingAddress, false);
                }
            }
            // 创建支付
            $res = paymentService()->payByAirwallexBank($order, $request);

            // 变更购物车标识状态
            $cart->update([
                'order_id' => $order->id,
                'is_checkout' => true
            ]);
            DB::commit();
        } catch (Throwable $exception) {
            DB::rollBack();
            throw $exception;
        }
        return JsonResource::make($order)->additional(['pay_data' => $res]);
    }

    /**
     * 从支付页提交订单并支付
     * @param Request $request
     * @return JsonResource
     * @throws DataException
     * @throws Throwable
     */
    public function paymentByCartForPayPal(Request $request): JsonResource
    {
        /**
         * @var $user User|null
         */
        $user = Auth::user();
        $validated = $request->validate([
            'session_uuid' => 'uuid',
            // 未登录不是paypal支付必须要邮件
            'email' => [
                Rule::requiredIf(function () use ($user, $request) {
                    return !$user && $request->input('paid_type') !== OrderPaidTypeEnum::PayPal->value;
                }),
                'email',
                // new Unique(User::class, 'email')
                Rule::requiredIf(function () use ($user, $request) {
                    return !$user && User::where('email', $request->input('email'))->exists();
                }),
            ],
            'subscribed' => [
                Rule::requiredIf(function () use ($user, $request) {
                    return !$user && $request->input('paid_type') !== OrderPaidTypeEnum::PayPal->value;
                }),
                'bool'
            ],
            'paid_type' => ['required', new Enum(OrderPaidTypeEnum::class)],
            // 配送地址
            'shipping_address' => [
                'array',
                Rule::requiredIf(function () use ($request) {
                    return $request->input('paid_type') !== OrderPaidTypeEnum::PayPal->value;
                })
            ],
            'shipping_address.first_name' => 'required_with:shipping_address|string|max:64',
            'shipping_address.last_name' => 'required_with:shipping_address|string|max:64',
            'shipping_address.country_id' => ['required_with:shipping_address', new Exists(Country::class, 'id')],
            'shipping_address.address' => 'string|max:512',
            'shipping_address.address1' => 'nullable|string|max:512',
            'shipping_address.state' => 'string|max:512',
            'shipping_address.is_save' => 'bool',
            'shipping_address.city' => 'required_with:shipping_address|max:512',
            'shipping_address.zip' => 'required_with:shipping_address|max:512',
            'shipping_address.phone' => 'required_with:shipping_address|string|max:32',
            // 账单地址
            'billing_address' => [
                'array',
                Rule::requiredIf(function () use ($request) {
                    return $request->input('paid_type') !== OrderPaidTypeEnum::PayPal->value;
                })
            ],
            'billing_address.first_name' => 'required_with:billing_address|string|max:64',
            'billing_address.last_name' => 'required_with:billing_address|string|max:64',
            'billing_address.country_id' => ['required_with:billing_address', new Exists(Country::class, 'id')],
            'billing_address.address' => 'string|max:512',
            'billing_address.address1' => 'nullable|string|max:512',
            'billing_address.state' => 'string|max:512',
            'billing_address.is_save' => 'bool',
            'billing_address.city' => 'required_with:billing_address|max:512',
            'billing_address.zip' => 'required_with:billing_address|max:512',
            'billing_address.phone' => 'required_with:billing_address|string|max:32',
        ]);
        $session_uuid = Arr::get($validated, 'session_uuid');
        $cart = userService()->getOrCreateCart($user, $session_uuid);

        if (!$cart instanceof Cart) {
            throw new DataException("You have not added any items to your shopping cart yet.");
        }

        //检查购物车运费
        $country = Country::find(Arr::get($validated, 'shipping_address.country_id'))->iso_code;
        cartService()->refreshCartShipping($cart, $country);

        // 获取购物车列表
        $cartItems = $cart->items()->with(['productVariant.product'])->get();
        if ($cartItems->isEmpty()) {
            throw new DataException("There is no product in your shopping cart yet.");
        }

        // 验证库存
        foreach ($cartItems as $item) {
            if ($item->is_activity && !$user) {
                $user_experience_activity_id = $item->user_experience_activity_id;
                //查看当前活动是否需要登录
                $user_experience_activity = UserExperienceActivity::find($user_experience_activity_id);
                if ($user_experience_activity->is_register == 1) {
                    throw new DataException('The current discounted price requires login.');
                }
            }
            $product = $item->productVariant;
            if ($product->stock < $item->num) {
                throw new DataException("Product {$product->getName()} is out of stock.");
            }
        }

        // 保存结账信息到购物车
        $shipping_address = Arr::get($validated, 'shipping_address', []);
        $shipping_address['country_code'] = Country::find($shipping_address['country_id'])->iso_code;
        $billing_address = Arr::get($validated, 'billing_address', []);
        $billing_address['country_code'] = Country::find($billing_address['country_id'])->iso_code;
        $checkoutData = [
            'email' => Arr::get($validated, 'email', null),
            'paid_type' => Arr::get($validated, 'paid_type'),
            'shipping_address' => $billing_address,
            'billing_address' => $billing_address,
            'user_id' => $user ? $user->id : null,
            'subscribed' => Arr::get($validated, 'subscribed', false),
            'timestamp' => now()->timestamp,
        ];
        $cart->update([
            'checkout_data' => $checkoutData,
            'is_checkout_processing' => true
        ]);

        DB::beginTransaction();
        // 发起支付
        $res = [];
        $paidType = OrderPaidTypeEnum::from(Arr::get($validated, 'paid_type'));

        try {
            switch ($paidType) {
                case OrderPaidTypeEnum::PayPal:
                    // 创建PayPal支付意向
                    $res = paymentService()->createPayPalOrder($cart);
                    break;
                default:
                    throw new DataException("Unsupported payment type.");
            }
            DB::commit();
        } catch (Throwable $exception) {
            // 恢复购物车状态
            $cart->update(['is_checkout_processing' => false]);
            DB::rollBack();
            throw $exception;
        }

        // 返回支付意向信息，前端将使用这些信息完成支付
        return JsonResource::make([
            'cart_id' => $cart->id,
            'session_uuid' => $session_uuid,
            'total' => $cart->total,
            'currency' => currentCurrency()->currency_code,
        ])->additional(['pay_data' => $res]);
    }


    /**
     * 提交订单,从商品,直接提交订单支付
     * @param Request $request
     * @return JsonResource
     * @throws DataException
     * @throws \Throwable
     */
    public function paymentByProduct(Request $request): JsonResource
    {
        $validated = $request->validate([
            'num' => 'required|integer|min:1',
            'product_variant_id' => ['required', new Exists(ProductVariant::class, 'id')],
            'country' => 'required|string',
        ]);
        $productVariant = ProductVariant::query()->with(['product'])->find(Arr::get($validated, 'product_variant_id'));
        if (!$productVariant instanceof ProductVariant) {
            throw new DataException("商品信息不存在(翻译英文)");
        }
        // 判断商品状态是否上架的
        if ($productVariant->product->status) {
            throw new DataException("商品信息不存在");
        }

        // 创建一个新的匿名购物车
        /** @var Cart $cart  */
        $cart = userService()->getOrCreateCart();
        // 添加购物车
        cartService()->addCartItem($cart, [...$validated, 'product_id' => $productVariant->product_id]);
        // 刷新购物车
        cartService()->refreshCart($cart, null, true, $validated['country']);
        $cart->refresh();

        // 生成快闪订单
        $fastPaypalOrder = new FastPaypalOrder([]);
        $fastPaypalOrder->cart()->associate($cart)->save();
        $info = paymentService()->fastPayByPaypal($fastPaypalOrder);

        return JsonResource::make($fastPaypalOrder)->additional(['pay_data' => $info]);
    }

    /**
     * 从购物车直接快闪支付
     * @param \Illuminate\Http\Request $request
     * @return JsonResource
     */
    public function fastPaymentByCart(Request $request): JsonResource
    {
        $validated = $request->validate([
            'session_uuid' => 'nullable|uuid',
            'country' => 'required|string',
        ]);

        $user = Auth::user();
        $session_uuid = Arr::get($validated, 'session_uuid');
        $cart = userService()->getOrCreateCart($user, $session_uuid);
        if ($cart->items->isEmpty()) {
            throw new DataException("The shopping cart is empty");
        }
        // 刷新购物车
        // $cart = cartService()->refreshCart($cart, null, true, $validated['country']);

        // 生成快闪订单
        $fastPaypalOrder = new FastPaypalOrder([]);
        $fastPaypalOrder->cart()->associate($cart)->save();
        $info = paymentService()->fastPayByPaypal($fastPaypalOrder);

        return JsonResource::make($fastPaypalOrder)->additional(['pay_data' => $info]);
    }

    /**
     *  更新
     * @param Request $request
     * @return JsonResource
     */
    public function syncOrdersShippingNumber(Request $request): JsonResource
    {
        $validated = $request->validate([
            'no' => ['required', 'string'],
            'shipping_number' => ['required', 'string'],
        ]);
        $order = Order::query()->where('no', $validated['no'])->first();
        if ($order) {
            $order->update(['shipping_number' => $validated['shipping_number']]);
        }
        return JsonResource::make([]);
    }


    public function syncOrdersStatus(Request $request): JsonResource
    {
        $validated = $request->validate([
            'no' => ['required', 'string'],
            'status' => ['required', new Enum(OrderStatusEnum::class)],
        ]);
        $order = Order::query()->where('no', $validated['no'])->first();
        if ($order) {
            $order->update(['status' => $validated['status']]);
        }
        return JsonResource::make([]);
    }

    /**
     * 订单列表(外部调用)
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function orders(Request $request): AnonymousResourceCollection
    {
        $builder = QueryBuilder::for(Order::class, $request)
            ->where('paid_status', OrderPaidStatusEnum::Paid)
            ->with([
                'items.product' => function ($builder) {
                    $builder->morphWith([
                        ProductVariant::class => [
                            'product:id,title,slug_title,spu,desc',
                            // 'product.variants:id,product_id,color_attribute_value_id,size_attribute_value_id',
                            // 'product.variants.colorAttribute:id,value',
                            // 'product.variants.sizeAttribute:id,value',
                            'image:path,id,module',
                            'images:path,id,module',
                            'colorAttribute',
                            'sizeAttribute'
                        ],
                    ]);
                },
                'items.coupon:id,name',
                'user:id,email',
                'payment',
                'billingAddress.country:id,iso_code,name',
                'shippingAddress.country:id,iso_code,name',
                'currency',
                'shipping'
            ])
            ->allowedFilters([
                AllowedFilter::partial('product_name', 'items.name'),
                AllowedFilter::exact('status'),
                AllowedFilter::exact('paid_status'),
                AllowedFilter::exact('payment_type'),
                AllowedFilter::callback('date_start', function (Builder $query, $value) {
                    $query->where('created_at', '>=', $value);
                }),
                AllowedFilter::callback('date_end', function (Builder $query, $value) {
                    $query->where('created_at', '<=', $value);
                }),
                AllowedFilter::partial('no'),
            ])
            ->defaultSort('-created_at');

        $res = $builder->paginate($this->getPerPage());

        // 状态转译
        $res->each(function ($item) {
            if ($item->payment instanceof AirwallexOrder) {
                $item->payment_name = 'airwallex';
                $item->tracking_number = $item->payment->intent_id;
            } elseif ($item->payment instanceof PaypalOrder) {
                $item->payment_name = 'paypal';
                $item->tracking_number = $item->payment->paypal_order_id;
            }
            $item->status_name = $item->status->en_desc();
        });

        return JsonResource::collection($res);
    }

    /**
     * 重新下单
     * @param \Illuminate\Http\Request $request
     * @return JsonResource
     */
    public function reorder(Request $request): JsonResource
    {
        $validated = $request->validate([
            'session_uuid' => 'nullable|uuid',
            'items' => 'array|required',
            'items.*.product_id' => ['required', 'int', 'exists:products,id'],
            'items.*.product_variant_id' => ['required', 'int', 'exists:product_variants,id'],
            'items.*.num' => 'numeric|required|min:1',
            'items.*.id' => 'required|numeric'
        ]);
        $newItems = Arr::get($validated, 'items', []);
        /** @var User|null $user */
        $user = Auth::user();
        /** @var Cart $cart  */
        $session_uuid = Arr::get($validated, 'session_uuid');
        DB::beginTransaction();
        try {
            //先清空购物车
            Cart::query()->where('user_id', $user->id)->where('order_id', null)->where('is_checkout', 0)->delete();
            /** @var Cart $userCart  */
            $cart = userService()->getOrCreateCart($user);
            foreach ($newItems as $item) {
                $is_activity = OrderItem::query()->where('id', $item['id'])->value('is_activity');
                cartService()->addCartItem($cart, $item, $user, $session_uuid, $is_activity);
            }

            // 刷新购物车数据
            $cart = cartService()->refreshCart($cart);
            $cart->load([
                'items',
                'items.product:id,spu,title',
                'items.productVariant:id,sku,stock,price,original_price,image_id,color_attribute_value_id,size_attribute_value_id',
                'items.productVariant.image:id,path,disk,module',
                'items.productVariant.sizeAttribute:id,value',
                'items.productVariant.colorAttribute:id,value',
                'items.coupon',
            ]);
            DB::commit();
        } catch (Throwable $exception) {
            DB::rollBack();
        }

        return JsonResource::make($cart);
    }

    // 获取物流单号
    public function shippingNumber(Request $request): JsonResource
    {
        $validated = $request->validate([
            'no' => 'required|string',
            'email' => 'required|string|email',
        ]);

        $res = Order::query()
            ->where('no', $validated['no'])
            ->where('email', $validated['email'])
            ->select('shipping_number')
            ->first();
        // 如果订单不存在
        if (!$res) {
            throw new DataException('Order not found.');
        }
        // 如果订单没有物流单号
        if (!$res->shipping_number) {
            throw new DataException('Order not shipped.');
        }

        return JsonResource::make($res);
    }

    /**
     * 订单确认页数据
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Order\Order $order
     * @return JsonResource
     */
    public function confirm(Request $request, Order $order): JsonResource
    {
        //如果订单状态是未支付，都改成支付失败
        if ($order->paid_status == OrderPaidStatusEnum::Unpaid && $order->status == OrderStatusEnum::Unpaid) {
            $order->update(['status' => OrderStatusEnum::PaidFail]);
            $user = auth()->user();
            EmailRuleNotice::dispatch(EmailRuleEventEnum::OrderPaidFail, user: $user, order: $order)->onQueue(QueueKey::Default->value);
        }

        // 优惠卷动效
        list($coupon_grant_rule, $coupon) = CouponGrantRule::grantCouponByRuleType(CouponGrantType::FirstOrder, $order, true);

        // 订单信息
        return JsonResource::make($order->loadMissing([
            'payment',
            'billingAddress',
            'billingAddress.country',
            'shippingAddress',
            'shippingAddress.country',
            'currency',
            'items',
            'items.coupon',
            'items.product' => function ($builder) {
                $builder->morphWith([
                    ProductVariant::class => [
                        'product:id,title,slug_title,spu,desc',
                        'product.variants:id,product_id,color_attribute_value_id,size_attribute_value_id',
                        'product.variants.colorAttribute:id,value',
                        'product.variants.sizeAttribute:id,value',
                        'image:path,id,module',
                        'images:path,id,module',
                        'colorAttribute',
                        'sizeAttribute'
                    ]
                ]);
            },
        ]))->additional([
            'coupon_frames' => [[
                'coupon_grant_rule' => $coupon_grant_rule,
                'coupon' => $coupon
            ]]
        ]);
    }
}
