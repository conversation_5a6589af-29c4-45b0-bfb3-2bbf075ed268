<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_items', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('order_id')->index();
            // 商品信息
            $table->string('product_type', 128)->comment('商品类型');
            $table->bigInteger('product_id')->index()->comment('商品id (第一版默认只有商品变种, 小心这里product 并非表示products)');
            $table->string('name', 512)->comment('商品名称');
            $table->decimal('price', 10, 4)->comment('价格');
            $table->decimal('original_price', 10, 4)->comment('原价');
            $table->bigInteger('user_coupon_id')->nullable()->comment('用户使用的优惠券');
            $table->bigInteger('coupon_id')->nullable()->comment('优惠券id');
            $table->decimal('coupon_discount_price', 10, 4)->default(0)->comment('优惠券抵扣金额/抵扣部分价格');
            $table->string('image_url', 512)->comment('商品主图');
            $table->integer('num')->comment('商品数量');
            $table->text('product_info')->comment('颜色值');
            $table->bigInteger('user_experience_activity_id')->nullable()->comment('用户体验活动id');
            $table->boolean('is_activity')->default(0)->comment('是否是活动产品');

            $table->timestamps();
            $table->engine('InnoDB');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_items');
    }
};
