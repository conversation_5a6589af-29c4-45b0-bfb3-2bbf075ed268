<?php

namespace App\Http\Controllers;

use App\Constants\CacheKey;
use App\Filters\EquipmentTypeFilter;
use App\Http\Resources\PromotionListResource;
use App\Models\Enums\Promotion\PromotionStatusEnum;
use App\Models\Enums\Promotion\PromotionTypeEnum;
use App\Models\Promotion;
use App\Models\User\User;
use App\Models\User\UserProductCollect;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

class PromotionController extends Controller
{
    /**
     * 列表
     * @param \Illuminate\Http\Request $request
     */
    public function index(Request $request): mixed
    {
        /**
         * @var $user User|null
         */
        $user = Auth::user();
        $uniqueKey = md5(json_encode($request->all()));
        $content = Cache::remember(CacheKey::PromotionBuilderList->getKey($uniqueKey), now()->addDays(), function () use ($request, $user) {
            $builder = QueryBuilder::for(Promotion::class, $request)
                ->allowedFilters([
                    AllowedFilter::partial('position'), // 模糊查询位置
                    AllowedFilter::exact('type'), // 精确查询类型
                    AllowedFilter::custom('equipment_type', new EquipmentTypeFilter()), // 自定义过滤器
                ])
                ->where('status', PromotionStatusEnum::StatusEnable)
                ->allowedSorts(['sort', 'id'])
                ->defaultSort('sort')
                ->with([
                    'contents',
                    'contents.collection:id,title,slug_title',
                    'contents.attachment:id,path,disk,module',
                    'contents.collection.products.commentStatic',
                    'productContents'
                ])
                ->get();
            return PromotionListResource::collection($builder)->response()->getContent();
        });
        // append user collected
        if ($user instanceof User) {
            $list = json_decode($content, true);
            foreach ($list['data'] as &$item) {
                if ($item['type'] == PromotionTypeEnum::ProductType->value) {
                    $productIds = data_get($item['contents'],'*.product.id');
                    $collectIds = UserProductCollect::query()->whereIn('product_id', $productIds)
                        ->where('user_id', $user->id)
                        ->pluck('product_id')
                        ->toArray();
                    foreach ($item['contents'] as &$row) {
                        $row['product']['collected'] = in_array($row['product']['id'], $collectIds);
                    }
                }
            }
            return response()->json($list);
        } else {
            return response($content, 200, ['content-type' => 'application/json']);
        }

    }
}
