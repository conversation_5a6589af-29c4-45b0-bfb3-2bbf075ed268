<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('task_point_configs', function (Blueprint $table) {
            $table->id();
            $table->string('task_type', 50)->comment('任务类型');
            $table->string('task_name', 100)->comment('任务名称');
            $table->string('channel', 50)->nullable()->comment('渠道(如微信、微博等)');
            $table->decimal('points_per_action', 10, 2)->default(0)->comment('每次操作获得积分');
            $table->integer('daily_limit')->default(0)->comment('每日限制次数(0为无限制)');
            $table->integer('total_limit')->default(0)->comment('总限制次数(0为无限制)');
            $table->boolean('is_active')->default(true)->comment('是否启用');
            $table->json('extra_config')->nullable()->comment('额外配置(JSON格式)');
            $table->text('description')->nullable()->comment('任务描述');
            $table->integer('sort_order')->default(0)->comment('排序');
            $table->timestamps();

            $table->index(['task_type', 'channel']);
            $table->index('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('task_point_configs');
    }
};
