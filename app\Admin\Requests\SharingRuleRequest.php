<?php

namespace App\Admin\Requests;

use App\Models\Email\EmailTemplate;
use App\Models\Enums\Reward\RewardFansTypeEnum;
use Illuminate\Validation\Rules\Enum;
use Illuminate\Foundation\Http\FormRequest;
use App\Models\Enums\Reward\RewardTypeEnum;
use Illuminate\Support\Arr;
use Illuminate\Validation\Rules\Exists;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Validator;

class SharingRuleRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true; // 根据需要进行授权检查
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'title' => ['required', 'string', 'max:32'],
            'description' => ['required', 'string', 'max:255'],
            'is_global' => ['bool'],
            'is_publish' => ['bool'],
            'effective_start_at' => 'date|nullable',
            'effective_end_at' => 'date|nullable',
            'reward_type' => ['required', new Enum(RewardTypeEnum::class)], // 奖励类型
            'rules' => ['required', 'array'], // 规则
            'rules.exclusive' => ['required', 'array'], // 专属粉丝规则
            'rules.ordinary' => ['nullable', 'array'], // 普通粉丝规则
            'condition' => ['required', 'array'],
            'condition.enabled' => ['bool'],
            'condition.days' => ['nullable', 'integer'],
            'email_setting' => ['required', 'array'],
            'email_setting.enabled' => ['bool'],
            'email_setting.email_template_id' => ['nullable', (new Exists(EmailTemplate::class, 'id'))],
        ];
    }

    /**
     * Validate reward type and its rules.
     *
     * @param RewardFansTypeEnum $fansType
     * @return void
     * @throws ValidationException
     */
    protected function validateRewardType(RewardFansTypeEnum $fansType)
    {
        $rewardTypeValue = Arr::get($this->validated(), 'reward_type');
        $rewardType = RewardTypeEnum::tryFrom($rewardTypeValue);

        $validationRules = $rewardType->validateRules($fansType);
        $validator = Validator::make($this->all(), $validationRules);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }
    }

    /**
     * Handle a passed validation attempt.
     *
     * @return void
     */
    protected function passedValidation()
    {
        // 验证专属粉丝首单规则
        $this->validateRewardType(RewardFansTypeEnum::Exclusive);

        // 验证专属粉丝其他订单规则
        $this->validateRewardType(RewardFansTypeEnum::Exclusive);

        if (Arr::get($this->validated(), 'rules.ordinary')) {
            // 验证普通粉丝首单规则
            $this->validateRewardType( RewardFansTypeEnum::Ordinary);

            // 验证普通粉丝其他订单规则
            $this->validateRewardType(RewardFansTypeEnum::Ordinary);
        }
    }
}
