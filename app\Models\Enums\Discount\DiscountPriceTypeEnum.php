<?php

namespace App\Models\Enums\Discount;

use Illuminate\Support\Arr;

enum DiscountPriceTypeEnum: int
{
    case  Price = 1;
    case  OriginalPrice = 2;

    public function desc(): string
    {

        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::Price->value => '售价',
            self::OriginalPrice->value => '原价',
        ];
    }

}
