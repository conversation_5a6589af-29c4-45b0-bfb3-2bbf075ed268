-- ========================================
-- 任务积分系统数据库结构
-- ========================================

SET FOREIGN_KEY_CHECKS = 0;

-- ========================================
-- 1. 任务积分配置表
-- ========================================
CREATE TABLE `task_point_configs` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `task_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '任务类型',
    `task_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '任务名称',
    `channel` varchar(50) COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '渠道(如微信、微博等)',
    `points_per_action` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '每次操作获得积分',
    `daily_limit` int(11) NOT NULL DEFAULT '0' COMMENT '每日限制次数(0为无限制)',
    `total_limit` int(11) NOT NULL DEFAULT '0' COMMENT '总限制次数(0为无限制)',
    `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
    `extra_config` json NULL DEFAULT NULL COMMENT '额外配置(JSON格式)',
    `description` text COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '任务描述',
    `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `task_point_configs_task_type_channel_index` (`task_type`,`channel`),
    KEY `task_point_configs_is_active_index` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ========================================
-- 2. 用户任务记录表
-- ========================================
CREATE TABLE `user_task_records` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
    `task_config_id` bigint(20) unsigned NOT NULL COMMENT '任务配置ID',
    `task_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '任务类型',
    `channel` varchar(50) COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '渠道',
    `earned_points` decimal(10,2) NOT NULL COMMENT '获得积分',
    `task_data` json NULL DEFAULT NULL COMMENT '任务相关数据',
    `external_id` varchar(255) COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '外部ID(如帖子ID、评论ID等)',
    `completed_at` timestamp NOT NULL COMMENT '完成时间',
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `user_task_records_user_id_foreign` (`user_id`),
    KEY `user_task_records_task_config_id_foreign` (`task_config_id`),
    KEY `user_task_records_user_id_task_type_completed_at_index` (`user_id`,`task_type`,`completed_at`),
    KEY `user_task_records_user_id_task_config_id_index` (`user_id`,`task_config_id`),
    KEY `user_task_records_completed_at_index` (`completed_at`),
    CONSTRAINT `user_task_records_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
    CONSTRAINT `user_task_records_task_config_id_foreign` FOREIGN KEY (`task_config_id`) REFERENCES `task_point_configs` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ========================================
-- 3. 插入任务积分配置数据
-- ========================================
INSERT INTO `task_point_configs` (`task_type`, `task_name`, `channel`, `points_per_action`, `daily_limit`, `total_limit`, `is_active`, `description`, `sort_order`, `created_at`, `updated_at`) VALUES
-- 注册相关
('registration', '用户注册', NULL, 100.00, 0, 1, 1, '新用户注册奖励', 1, NOW(), NOW()),

-- 生日奖励
('birthday', '生日奖励', NULL, 200.00, 1, 0, 1, '生日当天奖励积分', 2, NOW(), NOW()),

-- 关注任务
('wechat_follow', '关注微信', 'wechat', 50.00, 0, 1, 1, '关注官方微信公众号', 10, NOW(), NOW()),
('weibo_follow', '关注微博', 'weibo', 30.00, 0, 1, 1, '关注官方微博账号', 11, NOW(), NOW()),
('ins_follow', '关注INS', 'instagram', 30.00, 0, 1, 1, '关注官方Instagram账号', 12, NOW(), NOW()),
('ytb_follow', '关注YTB', 'youtube', 40.00, 0, 1, 1, '关注官方YouTube频道', 13, NOW(), NOW()),

-- 分享任务
('fb_share', '在FB上分享', 'facebook', 10.00, 3, 0, 1, '在Facebook上分享内容', 20, NOW(), NOW()),
('twitter_share', '在Twitter上分享', 'twitter', 10.00, 3, 0, 1, '在Twitter上分享内容', 21, NOW(), NOW()),

-- 内容创作
('post_review', '发表审核通过的帖子', NULL, 20.00, 5, 0, 1, '发表帖子并通过审核', 30, NOW(), NOW()),
('video_review', '发表审核通过的带视频评论', NULL, 30.00, 3, 0, 1, '发表带视频的评论并通过审核', 31, NOW(), NOW()),

-- 日常任务
('check_in', '签到', NULL, 5.00, 1, 0, 1, '每日签到奖励', 40, NOW(), NOW()),

-- 问卷调查
('questionnaire', '问卷', NULL, 50.00, 2, 0, 1, '完成问卷调查', 50, NOW(), NOW()),

-- 群组加入
('fb_group_join', '加入FB群组', 'facebook', 25.00, 0, 3, 1, '加入官方Facebook群组', 60, NOW(), NOW());

SET FOREIGN_KEY_CHECKS = 1;

-- ========================================
-- 4. 常用查询语句
-- ========================================

-- 查看所有任务配置
-- SELECT * FROM task_point_configs WHERE is_active = 1 ORDER BY sort_order;

-- 查看用户今日完成的任务
-- SELECT utr.*, tpc.task_name, tpc.channel
-- FROM user_task_records utr
-- JOIN task_point_configs tpc ON utr.task_config_id = tpc.id
-- WHERE utr.user_id = 1 AND DATE(utr.completed_at) = CURDATE()
-- ORDER BY utr.completed_at DESC;

-- 查看用户任务统计
-- SELECT 
--     COUNT(*) as total_tasks,
--     SUM(earned_points) as total_points,
--     COUNT(CASE WHEN DATE(completed_at) = CURDATE() THEN 1 END) as today_tasks,
--     SUM(CASE WHEN DATE(completed_at) = CURDATE() THEN earned_points ELSE 0 END) as today_points
-- FROM user_task_records 
-- WHERE user_id = 1;

-- 查看各任务类型完成情况统计
-- SELECT 
--     tpc.task_name,
--     tpc.channel,
--     COUNT(utr.id) as completion_count,
--     SUM(utr.earned_points) as total_points,
--     AVG(utr.earned_points) as avg_points
-- FROM task_point_configs tpc
-- LEFT JOIN user_task_records utr ON tpc.id = utr.task_config_id
-- WHERE tpc.is_active = 1
-- GROUP BY tpc.id, tpc.task_name, tpc.channel
-- ORDER BY tpc.sort_order;

-- 查看用户是否可以执行特定任务（检查限制）
-- SELECT 
--     tpc.*,
--     COALESCE(daily_count.count, 0) as today_count,
--     COALESCE(total_count.count, 0) as total_count,
--     CASE 
--         WHEN tpc.is_active = 0 THEN '任务已禁用'
--         WHEN tpc.daily_limit > 0 AND COALESCE(daily_count.count, 0) >= tpc.daily_limit THEN '今日次数已用完'
--         WHEN tpc.total_limit > 0 AND COALESCE(total_count.count, 0) >= tpc.total_limit THEN '总次数已用完'
--         ELSE '可执行'
--     END as status
-- FROM task_point_configs tpc
-- LEFT JOIN (
--     SELECT task_config_id, COUNT(*) as count
--     FROM user_task_records 
--     WHERE user_id = 1 AND DATE(completed_at) = CURDATE()
--     GROUP BY task_config_id
-- ) daily_count ON tpc.id = daily_count.task_config_id
-- LEFT JOIN (
--     SELECT task_config_id, COUNT(*) as count
--     FROM user_task_records 
--     WHERE user_id = 1
--     GROUP BY task_config_id
-- ) total_count ON tpc.id = total_count.task_config_id
-- WHERE tpc.task_type = 'check_in';
