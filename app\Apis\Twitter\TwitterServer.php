<?php

namespace App\Apis\Twitter;


use GuzzleHttp\Client;
use SocialiteProviders\Twitter\Server;

class TwitterServer extends Server
{
    public function urlTemporaryCredentials()
    {
        return 'https://api.x.com/oauth/request_token';
    }

    public function urlAuthorization()
    {
        return 'https://api.x.com/oauth/authenticate';
    }

    /**
     * {@inheritdoc}
     */
    public function urlTokenCredentials()
    {
        return 'https://api.x.com/oauth/access_token';
    }

    /**
     * {@inheritdoc}
     */
    public function urlUserDetails()
    {
        return 'https://api.x.com/1.1/account/verify_credentials.json?include_email=true';
    }


    public function createHttpClient()
    {
        $clientConfig = [
            'verify' => false,
            'timeout' => 30,
        ];
        if ($proxy = config('services.socialite_proxy')) {
            if ($proxy['enabled']) {
                $clientConfig['proxy'] = [
                    'http' => $proxy['http'],
                    'https' => $proxy['https'],
                ];
            }
        }
        return new Client($clientConfig);
    }

}
