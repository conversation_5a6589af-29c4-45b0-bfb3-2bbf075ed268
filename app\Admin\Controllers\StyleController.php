<?php

namespace App\Admin\Controllers;

use App\Constants\Permissions;
use App\Http\Controllers\Controller;
use App\Models\Style;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rules\Exists;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

class StyleController extends Controller
{
    //
    public function __construct()
    {
        $this->hasPermissionOr(Permissions::StylesUpdate)->only(['update', 'store', 'destroy']);
        $this->hasPermissionOr(Permissions::StylesUpdate, Permissions::StylesIndex)->only(['index', 'show']);
    }

    public function options(Request $request): AnonymousResourceCollection
    {
        $res = QueryBuilder::for(Style::class, $request)
            ->allowedFilters([
                AllowedFilter::partial('name'),
            ])
            ->select([DB::raw('name as label'), DB::raw('id as value')])
            ->get();
        return JsonResource::collection($res);
    }

    /**
     *
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $builder = QueryBuilder::for(Style::class, $request)
            ->allowedFilters([
                AllowedFilter::partial('name'),
            ])
            ->allowedSorts(['id'])
            ->defaultSort('-id');
        $res = $builder->paginate($this->getPerPage());
        return JsonResource::collection($res);
    }

    public function show(Style $style): JsonResource
    {
        return JsonResource::make($style);
    }

    /**
     * 修改
     * @param Request $request
     * @param Style $style
     * @return JsonResource
     */
    public function update(Request $request, Style $style): JsonResource
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:64'],
            'spu_key' => ['required', 'string', 'max:12'],
        ]);
        $style->update($validated);

        return JsonResource::make($style);
    }


    /**
     * 创建
     * @param Request $request
     * @param Style $style
     * @return JsonResource
     */
    public function store(Request $request, Style $style): JsonResource
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:64'],
            'spu_key' => ['required', 'string', 'max:12'],
        ]);
        $style->update($validated);

        return JsonResource::make($style);
    }


    /**
     * 删除
     * @param Request $request
     * @return JsonResponse
     */
    public function destroy(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ids' => ['array', 'required'],
            'ids.*' => [(new Exists(Style::class, 'id'))],
        ]);
        $ids = Arr::get($validated, 'ids');

        // 删除
        Style::query()->whereIn('id', $ids)
            ->update([
                'deleted_at' => now()
            ]);
        return response()->json();
    }

}
