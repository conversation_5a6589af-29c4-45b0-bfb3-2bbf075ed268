<?php

use App\Http\Controllers\ActivityController;
use App\Http\Controllers\AddressController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\BannerController;
use App\Http\Controllers\BroadcastColumnController;
use App\Http\Controllers\CartController;
use App\Http\Controllers\CollectionController;
use App\Http\Controllers\CommonController;
use App\Http\Controllers\CouponGrantController;
use App\Http\Controllers\CurrencyController;
use App\Http\Controllers\FooterController;
use App\Http\Controllers\HelpFaqController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\PromotionController;
use App\Http\Controllers\ShippingController;
use App\Http\Controllers\SysConfigController;
use App\Http\Controllers\TestController;
use App\Http\Controllers\UserAddressController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\WithdrawController;
use App\Http\Controllers\BrandMasterProgramController;
use App\Http\Controllers\ContactUsMessageController;
use App\Http\Controllers\ActivityNavController;
use App\Http\Controllers\CmsSettingController;
use App\Http\Controllers\MembershipController;
use App\Http\Middleware\SetCurrencyFromHeader;
use Illuminate\Http\Request;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\CmsArticleController;
/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

Route::any('test', [TestController::class, 'index']);
Route::any('heartbeat', [TestController::class, 'heartbeat']);
// 字典数据
Route::get('dictionaries', [CommonController::class, 'dictionaries']);

// 需要验证语言接口的接口
Route::group([SetCurrencyFromHeader::class], function () {
    // need auth
    Route::group(['middleware' => ['auth:sanctum', 'user.check']], function () {
        // 个人信息
        Route::group([], function () {
            // 个人信息
            Route::get('me', [UserController::class, 'me']);
            Route::patch('me', [UserController::class, 'update']);
            // 修改密码
            Route::post('me/change-password', [UserController::class, 'changePassword']);
            //
            Route::post('me/check-password', [UserController::class, 'checkPassword']);
            // 邀请发送邮件
            Route::post('invite/send-email', [UserController::class, 'inviteSendEmail']);
            // 邀请奖励
            Route::get('invite/rule', [UserController::class, 'inviteRule']);
            // 地址
            Route::group([], function () {
                Route::post('me/addresses/_search', [UserAddressController::class, 'index']);
                Route::post('me/addresses', [UserAddressController::class, 'store']);
                Route::get('me/addresses/{address}', [UserAddressController::class, 'show']);
                Route::patch('me/addresses/{address}', [UserAddressController::class, 'update']);
                Route::delete('me/addresses/{address}', [UserAddressController::class, 'destroy']);
            });

            // 优惠劵动效列表
            Route::post('me/coupons/_frame', [UserController::class, 'userCouponFrames']);
            // 用户优惠券列表
            Route::post('me/coupons/_search', [UserController::class, 'userCoupons']);
            // 评论列表
            Route::post('me/comments/_search', [UserController::class, 'userComments']);
            // 邀请好友列表
            Route::post('me/invite/_search', [UserController::class, 'inviteIndex']);
            //未读消息
            Route::get('new-message', [UserController::class, 'newMessage']);
            // 商品-登录
            Route::group([], function () {
                // 商品收藏
                Route::post('product/collects', [ProductController::class, 'collectProduct']);
                // 收藏清除
                Route::post('product/collect-clear', [ProductController::class, 'collectProductClear']);
                // 我的收藏列表
                Route::get('product/collects', [ProductController::class, 'collectProducts']);
            });

            // 购物车-合并本地购物车
            Route::group([], function () {
                Route::post('cart/merge', [CartController::class, 'mergeCart']);
            });
            // 订单
            Route::group([], function () {
                // 订单列表
                Route::post('orders/_search', [OrderController::class, 'index']);
                // 重新下单
                Route::post('orders/_reorder', [OrderController::class, 'reorder']);
            });

            // 邀请奖励提现
            Route::post('invite-amount/withdraw', [WithdrawController::class, 'inviteWithdraw']);
            // 提现发送邮件
            Route::post('withdraw/send-email', [WithdrawController::class, 'withdrawSendEmail']);
            // 提现规则
            Route::get('invite-amount/rule', [WithdrawController::class, 'rule']);

            // 会员等级相关
            Route::group(['prefix' => 'membership'], function () {
                // 获取用户会员信息
                Route::get('me', [MembershipController::class, 'me']);
                // 获取用户积分记录
                Route::post('point-records/_search', [MembershipController::class, 'pointRecords']);
                // 获取积分统计信息
                Route::get('point-stats', [MembershipController::class, 'pointStats']);
                // 手动检查并升级用户等级
                Route::post('check-upgrade', [MembershipController::class, 'checkUpgrade']);
                // 获取用户会员等级历史
                Route::post('level-history/_search', [MembershipController::class, 'levelHistory']);
                // 获取会员等级升级进度
                Route::get('upgrade-progress', [MembershipController::class, 'upgradeProgress']);
                // 获取本月积分获得情况
                Route::get('monthly-points', [MembershipController::class, 'monthlyPoints']);
                // 获取年度积分获得情况
                Route::get('yearly-points', [MembershipController::class, 'yearlyPoints']);
            });
        });
    });


    // 无需登录
    Route::group([], function () {
        // auth
        Route::group([], function () {
            // 获取验证code
            Route::post('auth/register', [AuthController::class, 'register']);
            // 重置密码-发送邮件
            Route::post('auth/reset/send-email', [AuthController::class, 'resetSendEmail']);
            // 重置密码
            Route::post('auth/reset-password', [AuthController::class, 'resetPassword']);
            // 登录
            Route::post('auth/login', [AuthController::class, 'login']);
            // 授权登录-google
            Route::post('auth/login-google', [AuthController::class, 'loginByGoogle']);

            // 授权登录推特
            Route::get('auth/login-twitter/redirect', [AuthController::class, 'twitterRedirect'])
                ->middleware([StartSession::class]);
            // 登录
            Route::get('auth/login-twitter', [AuthController::class, 'loginByTwitter'])
                ->middleware([StartSession::class]);

            // 授权登录-facebook
            // 前端-》跳第三防授权，
            Route::post('auth/login-facebook', [AuthController::class, 'loginByFacebook']);

            //sitemap相关数据
            Route::get('sitemap', [ProductController::class, 'sitemapData']);
        });


        // 省市区
        Route::group([], function () {
            // 地址匹配
            Route::post('address-match', [AddressController::class, 'addressMatch']);
            // 国家列表
            Route::post('countries/_search', [AddressController::class, 'countries']);
            // option countries
            Route::post('countries/_option', [AddressController::class, 'optionCountries']);
            // zones
            Route::post('zones/_search', [AddressController::class, 'zones']);
            // 国家货币列表
            Route::post('countries/currencies/_search', [AddressController::class, 'countryCurrencies']);
        });
        // 系统配置
        Route::get('sys-configs/{key}', [SysConfigController::class, 'show']);

        // 根据二维码参数获取前台展示地址
        Route::get('qr-code', [CommonController::class, 'qrCode']);

        // paypal 支付配置
        Route::get('paypal-config', [CommonController::class, 'paypalConfig']);

        // 商品属性
        Route::get('attributes', [ProductController::class, 'attributes']);

        // 会员等级公开信息
        Route::group(['prefix' => 'membership'], function () {
            // 获取所有会员等级列表
            Route::get('levels', [MembershipController::class, 'levels']);
            // 获取会员等级权益详情
            Route::get('levels/{level}/benefits', [MembershipController::class, 'levelBenefits']);
            // 获取会员等级对比
            Route::get('level-comparison', [MembershipController::class, 'levelComparison']);
        });

        // 广播栏列表
        Route::post('broadcasts/_search', [BroadcastColumnController::class, 'index']);
        // banner列表
        Route::post('banners/_search', [BannerController::class, 'index']);
        // footers
        Route::post('footers/_search', [FooterController::class, 'index']);
        // banner列表
        Route::post('currencies/_search', [CurrencyController::class, 'index']);
        // 购物车优惠列表
        Route::post('coupons/_search', [CommonController::class, 'coupons']);

        // HelpFaq;
        Route::group([], function () {
            // 列表
            Route::get('help-faqs', [HelpFaqController::class, 'index']);
            // 列表分页筛选
            Route::post('help-faqs/_search', [HelpFaqController::class, 'list']);
        });


        // 优惠券发放;
        Route::group([], function () {
            // 随机优惠券活动
            Route::get('random-grant/rules/{rule}', [CouponGrantController::class, 'randomGrantRule']);
            // 随机优惠券领取
            Route::post('random-grant/rules/{rule}/lottery', [CouponGrantController::class, 'randomGrantLottery']);
        });

        // 品牌大使计划
        Route::post('brand-master-program', [BrandMasterProgramController::class, 'store']);

        // 联系我们
        Route::post('contact-us-messages', [ContactUsMessageController::class, 'store']);
    });

    /**
     *  登不登录都可以
     */
    Route::group(['middleware' => ['auth:sanctum']], function () {
        // 上传图片
        Route::post('upload', [UserController::class, 'upload']);
        // 订阅
        Route::post('subscribe', [UserController::class, 'subscribe']);

        // 订阅确认
        Route::post('subscribe/confirm', [UserController::class, 'subscribeConfirm']);
        // 广告组件列表
        Route::post('promotions/_search', [PromotionController::class, 'index']);

        // 商品
        Route::group([], function () {
            // 商品列表
            Route::post('products/_search', [ProductController::class, 'index']);
            // 关键词提醒
            Route::get('product/suggestions', [ProductController::class, 'suggestions']);
            // 商品详情
            Route::post('products/_info', [ProductController::class, 'show']);
            // 商品匹配
            Route::post('products/_info/match', [ProductController::class, 'match']);

            // 评论
            Route::group([], function () {
                // 评论
                Route::post('products/{product}/comments', [ProductController::class, 'storeComment']);
                // 评论列表
                Route::post('product/comments/_search', [ProductController::class, 'commentList']);
            });

            //活动特价商品
            Route::post('products/_info/activity', [ProductController::class, 'activity']);

            // 商品收藏
            Route::post('products/_by_ids/collect', [ProductController::class, 'collectProductByIds']);
        });

        //特价活动导航
        Route::group([], function () {
            Route::post('activity-navs/_search', [ActivityNavController::class, 'index']);
        });

        // 商品集合/导航
        Route::group([], function () {
            // 集合列表
            Route::get('collections/_tree', [CollectionController::class, 'treeIndex']);
            // index
            Route::post('collections/_search', [CollectionController::class, 'index']);
            // 集合详情
            Route::get('collections/{slug_title}', [CollectionController::class, 'show']);
        });


        // 购物车
        Route::group([], function () {
            // 购物车添加商品
            Route::post('cart', [CartController::class, 'addCart']);
            // 修改运费类别
            Route::patch('cart', [CartController::class, 'updateCart']);
            // 查看购物车详情
            Route::get('cart', [CartController::class, 'show']);
            // 修改
            Route::patch('cart/items/{item}', [CartController::class, 'itemUpdate']);
            // 删除
            Route::delete('cart/items/{item}', [CartController::class, 'itemDestroy']);
            // 切换优惠券
            Route::post('cart/select-coupon', [CartController::class, 'selectCoupon']);
            //移除优惠券
            Route::patch('cart/remove-coupon', [CartController::class, 'removeCoupon']);
            //切换收货地址
            Route::patch('cart/switch-address', [CartController::class, 'switchAddress']);
        });

        // 优惠卷
        Route::group([], function () {
            // 优惠卷列表
            Route::get('coupons/shipping', [CartController::class, 'shippingCoupon']);
        });

        // 提交订单
        Route::group([], function () {
            // 订单确认页
            Route::get('orders/{order}/confirm', [OrderController::class, 'confirm']);
            // 查物流号
            Route::get('orders/shipping-number', [OrderController::class, 'shippingNumber']);
            // 购物车提交订单并支付 卡支付
            Route::post('orders/by-cart/payment', [OrderController::class, 'paymentByCart']);
            //购物车提交订单并支付 paypal
            Route::post('orders/by-cart/payment_paypal', [OrderController::class, 'paymentByCartForPayPal']);
            // 商品直接购买
            Route::post('orders/by-product/payment', [OrderController::class, 'paymentByProduct']);
            // 从购物车直接支付
            Route::post('orders/by-cart/fast-payment', [OrderController::class, 'fastPaymentByCart']);
            // 订单详情
            Route::get('orders/{order}', [OrderController::class, 'show']);
            // 从订单号获取注册的用户
            Route::post('orders/{order}/fast-user ', [OrderController::class, 'fastRegisterUser']);
        });

        // 支付相关
        Route::group([], function () {
            // paypal直接提交支付
            // 快捷支付
            Route::post('fast-orders/{fast_paypal_order}/paypal/confirm', [PaymentController::class, 'paypalFastConfirm']);
            // paypal支付结果
            Route::post('orders/{paypal_order}/paypal/confirm', [PaymentController::class, 'paypalConfirm']);
            // 支付回调
            Route::any('payment/paypal/notify', [PaymentController::class, 'paypalNotify']);
            //卡支付继续确认
            Route::post('payment/airwallex/3ds-check', [PaymentController::class, 'payment3dsCheck']);
        });

        // 运输方式
        Route::group([], function () {
            // 运输方式列表
            Route::get('shipping/_option', [ShippingController::class, 'option']);
        });

        //cms配置
        Route::group([], function () {
            // 列表
            Route::get('cms-settings', [CmsSettingController::class, 'index']);
            // 搜索
            Route::post('cms-settings/_search', [CmsSettingController::class, 'search']);

            Route::post('cms_article/_search', [CmsArticleController::class, 'index']);
            //获取所有标签
            Route::get('cms_article/tags', [CmsArticleController::class, 'tags']);
            //获取文章详情
            Route::get('cms_article/{cms_article}', [CmsArticleController::class, 'show']);
        });
    });
});

/**
 *  第三方接口
 */
Route::group(['middleware' => ['external.sign']], function () {
    //  external
    Route::get('external/orders', [OrderController::class, 'orders']);
    // 修改状态
    Route::post('external/order/sync-status', [OrderController::class, 'syncOrdersStatus']);
    // 更新物流
    Route::post('external/order/sync-shipping-number', [OrderController::class, 'syncOrdersShippingNumber']);
    // 更新商品状态
    Route::post('external/products/status', [ProductController::class, 'syncProductStatus']);
});
