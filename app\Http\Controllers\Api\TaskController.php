<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\TaskPointService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class TaskController extends Controller
{
    public function __construct(
        private TaskPointService $taskPointService
    ) {}

    /**
     * 获取用户可执行的任务列表
     */
    public function getAvailableTasks(): JsonResponse
    {
        $userId = Auth::id();
        $tasks = $this->taskPointService->getUserAvailableTasks($userId);

        return response()->json([
            'success' => true,
            'data' => $tasks,
        ]);
    }

    /**
     * 执行任务
     */
    public function executeTask(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'task_type' => 'required|string',
            'channel' => 'nullable|string',
            'task_data' => 'nullable|array',
            'external_id' => 'nullable|string',
        ]);

        $userId = Auth::id();
        
        try {
            $taskRecord = $this->taskPointService->executeTask(
                $userId,
                $validated['task_type'],
                $validated['channel'] ?? null,
                $validated['task_data'] ?? null,
                $validated['external_id'] ?? null
            );

            if (!$taskRecord) {
                return response()->json([
                    'success' => false,
                    'message' => '任务执行失败，可能已达到限制或任务不存在',
                ], 400);
            }

            return response()->json([
                'success' => true,
                'message' => '任务完成成功',
                'data' => [
                    'task_record_id' => $taskRecord->id,
                    'earned_points' => $taskRecord->earned_points,
                    'task_type' => $taskRecord->task_type,
                    'channel' => $taskRecord->channel,
                    'completed_at' => $taskRecord->completed_at,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '任务执行失败：' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * 获取用户任务统计
     */
    public function getTaskStats(): JsonResponse
    {
        $userId = Auth::id();
        $stats = $this->taskPointService->getUserTaskStats($userId);

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }

    /**
     * 获取用户任务记录
     */
    public function getTaskRecords(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'task_type' => 'nullable|string',
            'channel' => 'nullable|string',
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:100',
        ]);

        $userId = Auth::id();
        $query = \App\Models\Task\UserTaskRecord::byUser($userId)
            ->with('taskConfig')
            ->orderBy('completed_at', 'desc');

        if (!empty($validated['task_type'])) {
            $query->byTaskType($validated['task_type']);
        }

        if (!empty($validated['channel'])) {
            $query->where('channel', $validated['channel']);
        }

        $perPage = $validated['per_page'] ?? 20;
        $records = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $records->items(),
            'pagination' => [
                'current_page' => $records->currentPage(),
                'last_page' => $records->lastPage(),
                'per_page' => $records->perPage(),
                'total' => $records->total(),
            ],
        ]);
    }

    /**
     * 签到
     */
    public function checkIn(): JsonResponse
    {
        $userId = Auth::id();
        
        try {
            $taskRecord = $this->taskPointService->executeTask(
                $userId,
                'check_in'
            );

            if (!$taskRecord) {
                return response()->json([
                    'success' => false,
                    'message' => '今日已签到或签到任务不可用',
                ], 400);
            }

            return response()->json([
                'success' => true,
                'message' => '签到成功',
                'data' => [
                    'earned_points' => $taskRecord->earned_points,
                    'completed_at' => $taskRecord->completed_at,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '签到失败：' . $e->getMessage(),
            ], 500);
        }
    }
}
