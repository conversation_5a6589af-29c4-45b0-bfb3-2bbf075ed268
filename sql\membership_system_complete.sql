-- ========================================
-- 会员等级系统完整SQL脚本
-- 包含：建表语句、示例数据、常用查询
-- ========================================

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ========================================
-- 1. 创建会员等级表
-- ========================================
DROP TABLE IF EXISTS `membership_levels`;
CREATE TABLE `membership_levels` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '会员等级名称',
    `slug` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '会员等级标识',
    `description` text COLLATE utf8mb4_unicode_ci COMMENT '会员等级描述',
    `min_spend_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '最低消费金额',
    `max_spend_amount` decimal(12,2) DEFAULT NULL COMMENT '最高消费金额(null表示无上限)',
    `point_rate` decimal(5,4) NOT NULL DEFAULT '0.0100' COMMENT '积分兑换比例(消费1元获得多少积分)',
    `benefits` json DEFAULT NULL COMMENT '会员权益(JSON格式)',
    `color` varchar(7) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '#000000' COMMENT '等级颜色',
    `icon` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '等级图标',
    `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
    `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `membership_levels_slug_unique` (`slug`),
    KEY `membership_levels_is_active_sort_order_index` (`is_active`,`sort_order`),
    KEY `membership_levels_min_spend_amount_index` (`min_spend_amount`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员等级表';

-- ========================================
-- 2. 创建用户会员等级关联表
-- ========================================
DROP TABLE IF EXISTS `user_membership_levels`;
CREATE TABLE `user_membership_levels` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `user_id` bigint(20) NOT NULL COMMENT '用户ID',
    `membership_level_id` bigint(20) NOT NULL COMMENT '会员等级ID',
    `total_spend_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '累计消费金额',
    `current_year_spend` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '当年消费金额',
    `achieved_at` timestamp NULL DEFAULT NULL COMMENT '达到该等级时间',
    `expires_at` timestamp NULL DEFAULT NULL COMMENT '等级过期时间(null表示永久)',
    `is_current` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否当前等级',
    `upgrade_history` json DEFAULT NULL COMMENT '升级历史记录',
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_current_level` (`user_id`,`is_current`),
    KEY `user_membership_levels_user_id_index` (`user_id`),
    KEY `user_membership_levels_membership_level_id_index` (`membership_level_id`),
    KEY `user_membership_levels_user_id_membership_level_id_index` (`user_id`,`membership_level_id`),
    KEY `user_membership_levels_achieved_at_index` (`achieved_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户会员等级关联表';

-- ========================================
-- 3. 创建会员积分记录表
-- ========================================
DROP TABLE IF EXISTS `membership_point_records`;
CREATE TABLE `membership_point_records` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `user_id` bigint(20) NOT NULL COMMENT '用户ID',
    `membership_level_id` bigint(20) NOT NULL COMMENT '会员等级ID',
    `order_id` bigint(20) DEFAULT NULL COMMENT '订单ID',
    `order_amount` decimal(12,2) NOT NULL COMMENT '订单金额',
    `point_rate` decimal(5,4) NOT NULL COMMENT '积分比例',
    `earned_points` decimal(10,2) NOT NULL COMMENT '获得积分',
    `source_type` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'order' COMMENT '积分来源类型',
    `source_id` bigint(20) DEFAULT NULL COMMENT '来源ID',
    `description` text COLLATE utf8mb4_unicode_ci COMMENT '描述',
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `membership_point_records_user_id_index` (`user_id`),
    KEY `membership_point_records_user_id_created_at_index` (`user_id`,`created_at`),
    KEY `membership_point_records_order_id_index` (`order_id`),
    KEY `membership_point_records_source_type_source_id_index` (`source_type`,`source_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员积分获得记录表';

-- ========================================
-- 4. 插入示例会员等级数据
-- ========================================
INSERT INTO `membership_levels` (`name`, `slug`, `description`, `min_spend_amount`, `max_spend_amount`, `point_rate`, `benefits`, `color`, `icon`, `sort_order`, `is_active`, `created_at`, `updated_at`) VALUES
('普通会员', 'bronze', '新注册用户默认等级，享受基础服务', 0.00, 499.99, 0.0100, '{"point_rate": "1%", "birthday_bonus": "生日当月额外5%积分", "free_shipping_threshold": 99}', '#CD7F32', 'bronze-medal', 1, 1, NOW(), NOW()),
('银卡会员', 'silver', '累计消费满500元可升级，享受更多优惠', 500.00, 1999.99, 0.0150, '{"point_rate": "1.5%", "birthday_bonus": "生日当月额外10%积分", "free_shipping_threshold": 79, "exclusive_coupons": "专属优惠券"}', '#C0C0C0', 'silver-medal', 2, 1, NOW(), NOW()),
('金卡会员', 'gold', '累计消费满2000元可升级，享受高级服务', 2000.00, 4999.99, 0.0200, '{"point_rate": "2%", "birthday_bonus": "生日当月额外15%积分", "free_shipping_threshold": 59, "exclusive_coupons": "专属优惠券", "priority_customer_service": "优先客服"}', '#FFD700', 'gold-medal', 3, 1, NOW(), NOW()),
('白金会员', 'platinum', '累计消费满5000元可升级，享受顶级服务', 5000.00, 9999.99, 0.0250, '{"point_rate": "2.5%", "birthday_bonus": "生日当月额外20%积分", "free_shipping_threshold": 0, "exclusive_coupons": "专属优惠券", "priority_customer_service": "优先客服", "early_access": "新品抢先购"}', '#E5E4E2', 'platinum-medal', 4, 1, NOW(), NOW()),
('钻石会员', 'diamond', '累计消费满10000元可升级，享受至尊服务', 10000.00, NULL, 0.0300, '{"point_rate": "3%", "birthday_bonus": "生日当月额外25%积分", "free_shipping_threshold": 0, "exclusive_coupons": "专属优惠券", "priority_customer_service": "专属客服", "early_access": "新品抢先购", "vip_events": "VIP活动邀请", "personal_shopper": "专属购物顾问"}', '#B9F2FF', 'diamond', 5, 1, NOW(), NOW());

SET FOREIGN_KEY_CHECKS = 1;

-- ========================================
-- 5. 常用查询语句
-- ========================================

-- 查看所有会员等级
-- SELECT * FROM membership_levels WHERE is_active = 1 ORDER BY sort_order;

-- 根据消费金额查找对应等级
-- SELECT * FROM membership_levels 
-- WHERE is_active = 1 
--   AND min_spend_amount <= 1500 
--   AND (max_spend_amount IS NULL OR max_spend_amount >= 1500)
-- ORDER BY sort_order 
-- LIMIT 1;

-- 查看用户当前会员等级
-- SELECT u.id as user_id, u.name as user_name, ml.name as level_name, 
--        uml.total_spend_amount, uml.achieved_at
-- FROM users u
-- LEFT JOIN user_membership_levels uml ON u.id = uml.user_id AND uml.is_current = 1
-- LEFT JOIN membership_levels ml ON uml.membership_level_id = ml.id
-- WHERE u.id = 1;

-- 统计各等级用户数量
-- SELECT ml.name, COUNT(uml.user_id) as user_count,
--        SUM(uml.total_spend_amount) as total_spend,
--        AVG(uml.total_spend_amount) as avg_spend
-- FROM membership_levels ml
-- LEFT JOIN user_membership_levels uml ON ml.id = uml.membership_level_id AND uml.is_current = 1
-- WHERE ml.is_active = 1
-- GROUP BY ml.id, ml.name
-- ORDER BY ml.sort_order;

-- 查看用户积分记录
-- SELECT mpr.*, ml.name as level_name
-- FROM membership_point_records mpr
-- LEFT JOIN membership_levels ml ON mpr.membership_level_id = ml.id
-- WHERE mpr.user_id = 1
-- ORDER BY mpr.created_at DESC
-- LIMIT 10;
