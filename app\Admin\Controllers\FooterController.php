<?php

namespace App\Admin\Controllers;

use App\Constants\Permissions;
use App\Exceptions\DataException;
use App\Http\Controllers\Controller;
use App\Models\Footer\Footer;
use App\Models\Footer\FooterLink;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rules\Exists;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

class FooterController extends Controller
{

    public function __construct()
    {
        $this->hasPermissionOr(Permissions::FootersUpdate)->only(['store', 'update', 'destroy',]);
        $this->hasPermissionOr(Permissions::FootersUpdate, Permissions::FootersUpdate)->only(['index', 'show']);
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        $builder = QueryBuilder::for(Footer::class, $request)
            ->allowedFilters([
                AllowedFilter::partial('name'),
                AllowedFilter::exact('slug'),
            ])
            ->with('links')
            ->allowedSorts(['id'])
            ->defaultSort('-id');
        $res = $builder->paginate($this->getPerPage());
        return JsonResource::collection($res);
    }

    public function store(Request $request,Footer $footer): JsonResource
    {
        $validated = $request->validate([
            'name'           => ['required', 'string', 'max:255'],
            'slug'           => ['required', 'string', 'max:255', 'unique:footers,slug'],
            'links'          => ['required', 'array', 'min:1'],
            'links.*.label'  => ['required', 'string', 'max:255'],
            'links.*.url'    => ['required', 'string', 'max:512'],
            'links.*.sort'   => ['nullable', 'integer'],
        ]);

        DB::beginTransaction();
        try {
            $footer = Footer::create(Arr::only($validated, ['name', 'slug']));

            Arr::map($validated['links'], function ($link) use ($footer) {
                $footer->links()->create($link);
            });


            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }

        return JsonResource::make($footer->load('links'));
    }

    public function update(Request $request, Footer $footer)
    {
        $validated = $request->validate([
            'name'           => ['required', 'string', 'max:255'],
            'slug'           => ['required', 'string', 'max:255', 'unique:footers,slug,' . $footer->id],
            'links'          => ['required', 'array', 'min:1'],
            'links.*.id'     => ['nullable', 'exists:footer_links,id'],
            'links.*.label'  => ['required', 'string', 'max:255'],
            'links.*.url'    => ['required', 'string', 'max:512'],
            'links.*.sort'   => ['nullable', 'integer'],
        ]);

        DB::beginTransaction();
        try {
            $footer->update(Arr::only($validated, ['name', 'slug']));

            $linkIds = collect($validated['links'])->pluck('id')->filter();
            $footer->links()->whereNotIn('id', $linkIds)->delete();

            Arr::map($validated['links'], function ($link) use ($footer) {
                if (isset($link['id'])) {
                    FooterLink::find($link['id'])->update($link);
                } else {
                    $footer->links()->create($link);
                }
            });

            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }

        return JsonResource::make($footer->load('links'));
    }

    public function destroy(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ids' => ['array', 'required'],
            'ids.*' => [(new Exists(Footer::class, 'id'))],
        ]);
        $ids = Arr::get($validated, 'ids');
        DB::beginTransaction();
        try {
            // $footer->links()->delete();
            // $footer->delete();
            FooterLink::query()->whereIn('footer_id', $ids)->delete();
            Footer::query()->whereIn('id', $ids)->delete();

            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }

        return response()->json(['message' => '删除成功']);
    }

    public function show(Request $request, Footer $footer): JsonResource
    {
        return JsonResource::make($footer->load('links'));
    }

}
