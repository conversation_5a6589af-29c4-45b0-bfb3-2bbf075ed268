# 会员等级系统文档

## 概述

会员等级系统是一个完全动态配置的系统，允许管理员通过后台界面创建和管理不同的会员等级，用户根据累计消费金额自动升级会员等级，并享受相应的积分奖励和权益。

## 系统特性

- **动态配置**：所有会员等级通过后台管理界面配置，无需修改代码
- **自动升级**：用户消费后自动检查并升级会员等级
- **积分奖励**：不同等级享受不同的积分返还比例
- **权益管理**：灵活的权益配置系统，支持JSON格式存储
- **完整审计**：记录所有会员升级和积分变动历史

## 数据库结构

### 会员等级表 (membership_levels)
- `name`: 等级名称
- `slug`: 等级标识符
- `description`: 等级描述
- `min_spend_amount`: 最低消费金额
- `max_spend_amount`: 最高消费金额（可为null表示无上限）
- `point_rate`: 积分返还比例（0-1之间的小数）
- `benefits`: 权益配置（JSON格式）
- `color`: 等级颜色
- `icon`: 等级图标
- `sort_order`: 排序顺序
- `is_active`: 是否激活

### 用户会员等级表 (user_membership_levels)
- `user_id`: 用户ID
- `membership_level_id`: 会员等级ID
- `total_spend_amount`: 累计消费金额
- `current_year_spend_amount`: 当年消费金额
- `achieved_at`: 达到该等级的时间
- `is_current`: 是否为当前等级

### 会员积分记录表 (membership_point_records)
- `user_id`: 用户ID
- `membership_level_id`: 会员等级ID
- `source_type`: 积分来源类型
- `source_id`: 来源记录ID（如订单ID）
- `earned_points`: 获得积分
- `description`: 描述

## API接口

### 用户端接口

#### 获取用户会员信息
```
GET /api/membership/user-info
```

#### 获取用户积分记录
```
GET /api/membership/point-records
```

#### 获取用户积分统计
```
GET /api/membership/point-stats
```

#### 获取升级进度
```
GET /api/membership/upgrade-progress
```

#### 比较会员等级
```
GET /api/membership/compare-levels/{level1}/{level2}
```

### 公开接口

#### 获取所有会员等级
```
GET /api/membership/public/levels
```

#### 获取会员等级统计
```
GET /api/membership/public/level-stats
```

### 管理后台接口

#### 会员等级管理
```
POST /admin/membership/levels/_search     # 获取等级列表
POST /admin/membership/levels             # 创建等级
PUT /admin/membership/levels/{level}      # 更新等级
DELETE /admin/membership/levels/{level}   # 删除等级
```

#### 用户会员管理
```
POST /admin/membership/user-memberships/_search           # 用户会员列表
GET /admin/membership/users/{user}/membership            # 用户详细信息
POST /admin/membership/users/{user}/adjust-level         # 手动调整等级
```

#### 积分管理
```
POST /admin/membership/point-records/_search  # 积分记录列表
POST /admin/membership/grant-points           # 手动发放积分
```

#### 统计信息
```
GET /admin/membership/level-stats                        # 等级统计
POST /admin/membership/recalculate-user-levels          # 重新计算用户等级
```

## 使用指南

### 1. 初始化系统

首先运行数据库迁移：
```bash
php artisan migrate
```

可选：创建默认会员等级数据：
```bash
php artisan db:seed --class=MembershipLevelSeeder
```

### 2. 配置会员等级

通过管理后台创建会员等级：

1. 访问管理后台的会员等级管理页面
2. 点击"新增等级"
3. 填写等级信息：
   - 等级名称：如"普通会员"、"VIP会员"
   - 标识符：如"bronze"、"gold"（用于程序识别）
   - 消费门槛：设置最低和最高消费金额
   - 积分比例：设置该等级的积分返还比例
   - 权益配置：JSON格式配置等级权益
   - 显示设置：颜色、图标、排序等

### 3. 权益配置示例

```json
{
  "point_rate": "2%",
  "birthday_bonus": "生日当月额外15%积分",
  "free_shipping_threshold": 59,
  "exclusive_coupons": "专属优惠券",
  "priority_customer_service": "优先客服",
  "early_access": "新品抢先购"
}
```

### 4. 系统集成

系统已自动集成到订单支付流程中：

1. 用户完成订单支付后，系统自动：
   - 计算并发放积分
   - 检查是否满足升级条件
   - 自动升级会员等级
   - 记录相关历史

2. 积分发放规则：
   - 积分 = 订单金额 × 当前会员等级积分比例
   - 积分会自动添加到用户钱包

### 5. 管理功能

#### 用户会员管理
- 查看所有用户的会员等级和消费情况
- 手动调整用户会员等级
- 查看用户的会员升级历史

#### 积分管理
- 查看所有积分记录
- 手动为用户发放积分
- 积分统计和分析

#### 数据统计
- 各等级用户数量统计
- 消费金额统计
- 积分发放统计

### 6. 批量操作

系统提供批量重新计算用户等级的功能，适用于：
- 修改等级规则后重新计算
- 数据修复
- 系统维护

## 注意事项

1. **等级删除限制**：如果有用户正在使用某个等级，该等级无法删除
2. **消费金额计算**：基于已支付订单的总金额
3. **积分精度**：积分计算保留2位小数
4. **等级排序**：等级按sort_order和min_spend_amount排序
5. **数据一致性**：所有操作都有完整的审计记录

## 扩展开发

### 添加新的积分来源

1. 在`MembershipPointSourceEnum`中添加新的来源类型
2. 在`WalletChangeTypeEnum`中添加对应的钱包变动类型
3. 在`MembershipService`中添加处理方法
4. 调用`MembershipPointRecord::create()`记录积分

### 自定义权益处理

可以在业务逻辑中根据用户的会员等级权益进行特殊处理：

```php
$userLevel = UserMembershipLevel::getCurrentLevel($userId);
if ($userLevel) {
    $benefits = $userLevel->membershipLevel->benefits;
    
    // 根据权益进行业务处理
    if (isset($benefits['free_shipping_threshold'])) {
        // 处理免运费逻辑
    }
    
    if (isset($benefits['exclusive_coupons'])) {
        // 处理专属优惠券逻辑
    }
}
```

## 故障排除

### 常见问题

1. **用户等级未自动升级**
   - 检查订单状态是否为已支付
   - 确认ProcessMembershipRewards任务是否正常执行
   - 检查会员等级配置是否正确

2. **积分未正确发放**
   - 检查会员等级的积分比例配置
   - 确认用户钱包系统正常工作
   - 查看积分记录表是否有相关记录

3. **管理后台无法访问**
   - 确认路由配置正确
   - 检查管理员权限设置
   - 确认控制器文件存在

如有其他问题，请查看系统日志或联系开发团队。
