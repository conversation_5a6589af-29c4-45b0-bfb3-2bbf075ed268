<?php

namespace App\Imports;

use App\Models\Attribute;
use App\Models\AttributeValue;
use App\Models\Brand;
use App\Models\Category;
use App\Models\Enums\AttributesEnum;
use App\Models\Material;
use App\Models\Mode;
use App\Models\Product\Product;
use App\Models\Product\ProductColor;
use App\Models\Product\ProductVariant;
use App\Models\Style;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Concerns\RegistersEventListeners;
use Maatwebsite\Excel\Concerns\SkipsEmptyRows;
use Maatwebsite\Excel\Concerns\SkipsOnFailure;
use Maatwebsite\Excel\Concerns\ToArray;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Events\AfterImport;
use Maatwebsite\Excel\Events\BeforeImport;
use Maatwebsite\Excel\Imports\HeadingRowFormatter;
use Maatwebsite\Excel\Validators\Failure;
use PhpOffice\PhpSpreadsheet\Shared\Date;

class ProductsImport implements ToArray, WithHeadingRow, WithEvents, WithValidation, SkipsEmptyRows, SkipsOnFailure
{

    use RegistersEventListeners;

    /**
     * @var \Illuminate\Database\Eloquent\Collection
     */
    protected Collection $projects;

    public static function beforeImport(BeforeImport $event): void
    {
        HeadingRowFormatter::extend('slug', function ($value, $key) {
            return Arr::get(static::$header, $value);
        });
        HeadingRowFormatter::default('slug');
    }

    public static function afterImport(AfterImport $event): void
    {
        HeadingRowFormatter::reset();
    }

    //
    public static array $header = [
        "SKU编码" => "sku",
        "商品名称" => "title",
        "描述" => "desc",
        "关键词" => "keywords",
        "简介" => "short_desc",
        "分类" => "collection_name",
        "model描述" => "model_desc",
        "meta标题" => "meta_title",
        "meta关键词" => "meta_keywords",
        "meta description" => "meta_description",
        "slug标题" => "slug_title",
        "品牌名称" => "brand_name",
        "品类" => "category_name",
        "款式" => 'style_name',
        "材质" => 'material_name',
        "生产时间" => 'make_at',
        "风格" => 'mode_name',
        "SKU主图" => 'sku_image',
        "推荐" => 'is_featured',
        "颜色" => 'color_attribute_value',
        "尺码" => 'size_attribute_value',
        "售价($)" => 'price',
        "原价($)" => 'original_price',
        "库存" => 'stock',
        "SKC排序" => 'skc_sort',
        "SKC主图" => 'skc_image',
        "SKC图片" => 'skc_images',
        "是否上架" => 'is_publish'
    ];

    public function rules(): array
    {
        return [
            'sku' => ['required', 'string'],
            'title' => ['required', 'string'],
            'desc' => ['nullable', 'string'],
            'keywords' => ['nullable', 'string'],
            'short_desc' => ['nullable', 'string'],
            'collection_name' => ['required', 'string'],
            'model_desc' => ['nullable', 'string'],
            'meta_title' => ['nullable', 'string'],
            'meta_keywords' => ['nullable', 'string'],
            'meta_description' => ['nullable', 'string'],
            'slug_title' => ['nullable', 'string'],
            'brand_name' => ['required', 'string'],
            'category_name' => ['required', 'string'],
            'style_name' => ['required', 'string'],
            'material_name' => ['required', 'string'],
            'make_at' => ['required', 'date'],
            'mode_name' => ['nullable', 'string'],
            'sku_image' => ['nullable', 'string'],
            'is_featured' => ['nullable', 'bool'],
            'is_publish' => ['nullable', 'bool'],
            'color_attribute_value' => ['nullable', 'string'],
            'size_attribute_value' => ['nullable', 'string'],
            'price' => ['required', 'numeric'],
            'original_price' => ['required', 'numeric'],
            'stock' => ['nullable', 'integer'],
            'skc_sort' => ['nullable', 'integer'],
            'skc_image' => ['nullable', 'string'],
            'skc_images' => ['nullable', 'string'],
        ];
    }

    public function array(array $array)
    {
        // 错误信息
        $errors = [];
        // 已存在的产品
        $exitProducts = Product::query()
            ->select(['id', 'spu'])
            ->pluck('id', 'spu')
            ->toArray();
        $exitProducts = array_change_key_case($exitProducts, CASE_UPPER);
        // 已存在的变体
        $exitVariants = ProductVariant::query()
            ->select(['id', 'sku'])
            ->whereIn('product_id', $exitProducts)
            ->pluck('id', 'sku')
            ->toArray();
        $exitVariants = array_change_key_case($exitVariants, CASE_UPPER);
        // 已存在的属性
        $attributes = Attribute::query()
            ->select('id', 'name')
            ->pluck('id', 'name')
            ->toArray();
        $attributes = array_change_key_case($attributes, CASE_UPPER);
        // 已存在的属性值
        $attributeValues = AttributeValue::query()
            ->select('id', 'value', 'attribute_id')
            ->get()
            ->groupBy('attribute_id')
            ->map(function ($items, $attribute_id) {
                return $items->pluck('id', 'value')->toArray();
            })
            ->mapWithKeys(function ($values, $attribute_id) {
                return [$attribute_id => array_change_key_case($values, CASE_UPPER)];
            })
            ->toArray();
        // 已存在的品牌
        $brands = Brand::query()
            ->select(['id', 'name'])
            ->pluck('id', 'name')
            ->toArray();
        $brands = array_change_key_case($brands, CASE_UPPER);
        // 已存在的品类
        $categories = Category::query()
            ->select(['id', 'name'])
            ->pluck('id', 'name')
            ->toArray();
        $categories = array_change_key_case($categories, CASE_UPPER);
        // 已存在的材质
        $materials = Material::query()
            ->select(['id', 'name'])
            ->pluck('id', 'name')
            ->toArray();
        $materials = array_change_key_case($materials, CASE_UPPER);
        // 已存在的款式
        $styles = Style::query()
            ->select(['id', 'name'])
            ->pluck('id', 'name')
            ->toArray();
        $styles = array_change_key_case($styles, CASE_UPPER);
        // 已存在的模式
        $modes = Mode::query()
            ->select(['id', 'name'])
            ->pluck('id', 'name')
            ->toArray();
        $modes = array_change_key_case($modes, CASE_UPPER);
        // 已存在的集合
        $collections = \App\Models\Collection::query()
            ->select(['id', 'title'])
            ->pluck('id', 'title')
            ->toArray();
        // 将键转换为大写
        $collections = array_change_key_case($collections, CASE_UPPER);
        // 本地图片目录
        $product_dir = 'storage' . DIRECTORY_SEPARATOR . 'product' . DIRECTORY_SEPARATOR . 'images' . DIRECTORY_SEPARATOR;
        // 整理数据
        $products = [];
        foreach ($array as $value) {
            try {
                $sku = trim($value['sku']);
                $color = trim($value['color_attribute_value']);
                $size = trim($value['size_attribute_value']);
                $spu = Str::upper(join('_', [$sku, $color, $size]));
                // 已存在产品\已存在产品变体\无图片跳出
                if (Arr::exists($exitProducts, $sku)) {
                    $errors[] = 'SKU: ' . $sku . ' 已存在';
                    continue;
                }
                if (!$value['title']) {
                    $errors[] = 'SKU: ' . $sku . ' 无标题';
                    continue;
                }
                if (Arr::exists($exitVariants, $spu)) {
                    $errors[] = 'SPU: ' . $spu . ' 已存在';
                    continue;
                }
                // if (!$value['sku_image'] || !$value['skc_image'] || !$value['skc_images']) {
                //     $errors[] = 'SKU: ' . $sku . ' 无图片';
                //     continue;
                // }
                // 主体数据
                if (!Arr::has($products, $sku)) {
                    // 品牌
                    if ($brand_name = trim($value['brand_name'])) {
                        if (Arr::get($brands, Str::upper($brand_name))) {
                            $brand_id = Arr::get($brands, Str::upper($brand_name));
                        } else {
                            $brand = new Brand([
                                'name' => $brand_name,
                                'spu_key' => Str::upper(Str::substr($brand_name, 0, 1))
                            ]);
                            $brand->save();
                            $brands[Str::upper($brand_name)] = $brand->id;
                            $brand_id = $brand->id;
                        }
                    } else {
                        $brand_id = null;
                    }
                    // 品类
                    if ($category_name = trim($value['category_name'])) {
                        list($en_category_name, $zh_category_name) = splitEnZh($category_name);
                        if ($en_category_name) {
                            if (Arr::get($categories, Str::upper($en_category_name))) {
                                $category_id = Arr::get($categories, Str::upper($en_category_name));
                            } else {
                                $category = new Category([
                                    'name' => $en_category_name,
                                    'name_zh' => $zh_category_name,
                                    'spu_key' => Str::upper(Str::substr($en_category_name, 0, 1))
                                ]);
                                $category->save();
                                $categories[Str::upper($en_category_name)] = $category->id;
                                $category_id = $category->id;
                            }
                        } else {
                            $category_id = null;
                        }
                    } else {
                        $category_id = null;
                    }
                    // 款式
                    if ($style_name = trim($value['style_name'])) {
                        list($en_style_name, $zh_style_name) = splitEnZh($style_name);
                        if ($en_style_name) {
                            if (Arr::get($styles, Str::upper($en_style_name))) {
                                $style_id = Arr::get($styles, Str::upper($en_style_name));
                            } else {
                                $style = new Style([
                                    'name' => $en_style_name,
                                    'name_zh' => $zh_style_name,
                                    'spu_key' => Str::upper(Str::substr($en_style_name, 0, 1))
                                ]);
                                $style->save();
                                $styles[Str::upper($en_style_name)] = $style->id;
                                $style_id = $style->id;
                            }
                        } else {
                            $style_id = null;
                        }
                    } else {
                        $style_id = null;
                    }
                    // 材质
                    if ($material_name = $value['material_name']) {
                        list($en_material_name, $zh_material_name) = splitEnZh($material_name);
                        if ($en_material_name) {
                            if (Arr::get($materials, Str::upper($en_material_name))) {
                                $material_id = Arr::get($materials, Str::upper($en_material_name));
                            } else {
                                $material = new Material([
                                    'name' => $en_material_name,
                                    'name_zh' => $zh_material_name,
                                    'spu_key' => Str::upper(Str::substr($en_material_name, 0, 1))
                                ]);
                                $material->save();
                                $materials[Str::upper($en_material_name)] = $material->id;
                                $material_id = $material->id;
                            }
                        } else {
                            $material_id = null;
                        }
                    } else {
                        $material_id = null;
                    }
                    // 时间
                    $time = Date::excelToTimestamp($value['make_at']);
                    // 商品
                    $product = new Product([
                        'spu' => $sku,
                        'title' => trim($value['title']) ?: ' ',
                        'desc' => $value['desc'] ? str_replace(['\r\n', '\n', '\r'], '<br>', $value['desc']) : NUll,
                        'keywords' => $value['keywords'] ? trim($value['keywords'], ',') : NUll,
                        'short_desc' => $value['short_desc'] ?: NUll,
                        'model_desc' => $value['model_desc'] ?: NUll,
                        'meta_title' => $value['meta_title'] ?: NUll,
                        'meta_keywords' => $value['meta_keywords'] ?: NUll,
                        'meta_description' => $value['meta_description'] ?: NUll,
                        'slug_title' => $value['slug_title'] ?: NUll,
                        'brand_id' => $brand_id,
                        'category_id' => $category_id,
                        'material_id' => $material_id,
                        'style_id' => $style_id,
                        'is_featured' => $value['is_featured'] ? true : false,
                        'make_at' => Carbon::createFromTimestamp($time),
                        'is_publish' => $value['is_publish'] ? true : false,
                    ]);
                    // 产品的图片
                    if ($value['sku_image']) {
                        if (is_numeric($value['sku_image'])) {
                            // 图片id
                            $sku_image = $value['sku_image'];
                        } elseif (Str::isUrl($value['sku_image'])) {
                            // 图片url
                            $sku_image = productService()->uploadFileFromUrl($value['sku_image']);
                        } elseif (is_file(Storage::disk('public')->path($product_dir . $value['sku_image']))) {
                            // 图片本地路径
                            $sku_image = productService()->uploadFileFromLocalPath($product_dir . $value['sku_image']);
                        } else {
                            // 图片本地文件夹路径
                            $sku_image = Arr::first(productService()->uploadFileFromLocalDir($product_dir . $value['sku_image']));
                        }
                        // // 无图片不存储
                        // if (!$sku_image) {
                        //     $errors[] = 'SKU: ' . $sku . ' 无产品主图片';
                        //     continue;
                        // }
                        $sku_image && $product->image()->associate($sku_image);
                    }
                    $products[$sku] = $product;
                    // 集合
                    $collection_ids = [];
                    if ($value['collection_name']) {
                        $collection_names = explode(',', trim($value['collection_name'], ','));
                        foreach ($collection_names as $collection_name) {
                            $collection_name = trim($collection_name);
                            if (Arr::get($collections, Str::upper($collection_name))) {
                                $collection_ids[] = Arr::get($collections, Str::upper($collection_name));
                            } else {
                                $errors[] = 'SKU: ' . $sku . ' 集合: ' . $collection_name . ' 不存在';
                            }
                        }
                    }
                    $products[$sku]['collections'] = $collection_ids;
                    // 风格
                    $mode_ids = [];
                    if ($value['mode_name']) {
                        $mode_names = explode(',', trim($value['mode_name'], ','));
                        foreach ($mode_names as $mode_name) {
                            if (Arr::get($modes, Str::upper($mode_name))) {
                                $mode_ids[] = Arr::get($modes, Str::upper($mode_name));
                            } else {
                                $mode = new Mode([
                                    'name' => $mode_name
                                ]);
                                $mode->save();
                                $modes[Str::upper($mode_name)] = $mode->id;
                                $mode_ids[] = $mode->id;
                            }
                        }
                    }
                    $products[$sku]['modes'] = $mode_ids;
                }
                // 变体数据
                // 颜色
                if ($color) {
                    if (Arr::get($attributes, 'COLOR')) {
                        $attribute_id = Arr::get($attributes, 'COLOR');
                    } else {
                        $attribute = new Attribute([
                            'name' => 'Color',
                        ]);
                        $attribute->save();
                        $attribute_id = $attribute->id;
                        $attributes['COLOR'] = $attribute_id;
                    }

                    if (Arr::get(Arr::get($attributeValues, $attribute_id), str::upper($color))) {
                        $color_attribute_value_id = Arr::get(Arr::get($attributeValues, $attribute_id), str::upper($color));
                    } else {
                        $attributeValue = new AttributeValue([
                            'attribute_id' => $attribute_id,
                            'value' => $color,
                        ]);
                        $attributeValue->save();
                        $color_attribute_value_id = $attributeValue->id;
                        $attributeValues[$attribute_id][str::upper($color)] = $color_attribute_value_id;
                    }
                } else {
                    $color_attribute_value_id = null;
                }

                // 尺寸
                if ($size) {
                    if (Arr::get($attributes, 'SIZE')) {
                        $attribute_id = Arr::get($attributes, 'SIZE');
                    } else {
                        $attribute = new Attribute([
                            'name' => 'Size',
                        ]);
                        $attribute->save();
                        $attribute_id = $attribute->id;
                        $attributes['SIZE'] = $attribute_id;
                    }

                    if (Arr::get(Arr::get($attributeValues, $attribute_id), str::upper($size))) {
                        $size_attribute_value_id = Arr::get(Arr::get($attributeValues, $attribute_id), str::upper($size));
                    } else {
                        $attributeValue = new AttributeValue([
                            'attribute_id' => $attribute_id,
                            'value' => $size,
                        ]);
                        $attributeValue->save();
                        $size_attribute_value_id = $attributeValue->id;
                        $attributeValues[$attribute_id][str::upper($size)] = $size_attribute_value_id;
                    }
                } else {
                    $size_attribute_value_id = null;
                }
                $variant = new ProductVariant([
                    'sku' => $spu,
                    'color_attribute_value_id' => $color_attribute_value_id,
                    'size_attribute_value_id' => $size_attribute_value_id,
                    'price' => $value['price'] ?: 0,
                    'original_price' => $value['original_price'] ?: 0,
                    'stock' => $value['stock'] ?: 100,
                    'sort' => $value['skc_sort'] ? : 0,
                ]);
                if ($value['skc_image']) {
                    // 变体主图
                    if (is_numeric($value['skc_image'])) {
                        // 图片id
                        $skc_image = $value['skc_image'];
                    } elseif (Str::isUrl($value['skc_image'])) {
                        // 图片url
                        $skc_image = productService()->uploadFileFromUrl($value['skc_image']);
                    } elseif (is_file(Storage::disk('public')->path($product_dir . $value['skc_image']))) {
                        // 图片本地路径
                        $skc_image = productService()->uploadFileFromLocalPath($product_dir . $value['skc_image']);
                    } else {
                        // 图片本地文件夹路径
                        $skc_image = Arr::first(productService()->uploadFileFromLocalDir($product_dir . $value['skc_image']));
                    }
                    // 无图片不存储
                    // if (!$skc_image) {
                    //     $errors[] = 'SKU: ' . $sku . ' 无变体主图片';
                    //     continue;
                    // }
                    $variant->image()->associate($skc_image);
                }

                // 图片集
                if ($value['skc_images']) {
                    $image_ids = [];
                    $skc_images = explode(',', trim($value['skc_images'], ','));
                    foreach ($skc_images as $image) {
                        // 变体主图
                        if (is_numeric($image)) {
                            // 图片id
                            $image_ids[] = $image;
                        } elseif (Str::isUrl($image)) {
                            // 图片url
                            $attachment = productService()->uploadFileFromUrl($image);
                            $image_ids[] = $attachment->id;
                        } elseif (is_file(Storage::disk('public')->path($product_dir . $image))) {
                            // 图片本地路径
                            $attachment = productService()->uploadFileFromLocalPath($product_dir . $image);
                            $image_ids[] = $attachment->id;
                        } else {
                            // 图片本地文件夹路径
                            $attachments = productService()->uploadFileFromLocalDir($product_dir . $image);
                            $image_ids = array_merge($image_ids, Arr::pluck($attachments, 'id'));
                        }
                    }
                    // // 无图片不存储
                    // if (!$image_ids) {
                    //     $errors[] = 'SKU: ' . $sku . ' 无变体图片集';
                    //     continue;
                    // }
                    $variant['images'] = $image_ids;
                }
                // 用于新增的数据
                $products[$sku]['variants'][] = $variant;
                // 避免重复的变体
                $exitVariants[$spu] = $variant;
            } catch (\Exception $e) {
                $errors[] = 'SKU：' . $sku . '，' . $e->getMessage();
            }
        }
        // 保存数据
        foreach ($products as $product) {
            try {
                DB::beginTransaction();
                $variants = Arr::pull($product, 'variants');
                if ($variants->isEmpty()) {
                    continue;
                }
                $collections = Arr::pull($product, 'collections');
                $modes = Arr::pull($product, 'modes');
                // 新增
                $product->save();
                // 集合
                if ($collections) {
                    $product->collections()->attach($collections);
                }
                // 风格
                if ($modes) {
                    $product->modes()->attach($modes);
                }
                /**
                 * 变体
                 * @var ProductVariant $variant
                 */
                $colors = [];
                foreach ($variants as $variant) {
                    $images = Arr::pull($variant, 'images');
                    // 无图片不存储
                    // if (!$images) {
                    //     continue;
                    // }
                    // 关联产品
                    $variant->product()->associate($product);
                    $variant->save();
                    // 图片
                    if ($images) {
                        $variant->images()->attach($images);
                    }

                    // 同步颜色以及对应的图片
                    if (!Arr::exists($colors, $variant->color_attribute_value_id)) {
                        $colors[$variant->color_attribute_value_id] = $variant->color_attribute_value_id;
                        $color = new ProductColor([
                            'product_id' => $product->id,
                            'color_id' => $variant->color_attribute_value_id,
                        ]);
                        $color->save();
                        if ($images) {
                            $color->images()->attach($images);
                        }
                    }


                }
                // 更新价格
                $product->update([
                    'origin_price' => $variant['original_price'],
                    'min_price' => max(Arr::pluck($variants, 'price')),
                    'max_price' => min(Arr::pluck($variants, 'price')),
                ]);

                // 缺一个删除本地产品图的功能
                DB::commit();
            } catch (\Exception $e) {
                DB::rollBack();
                $errors[] = 'SKU：' . $product['spu'] . '，' . $e->getMessage();
            }
        }

        return $errors;
    }

    public function onFailure(Failure ...$failures)
    {
    }
}
