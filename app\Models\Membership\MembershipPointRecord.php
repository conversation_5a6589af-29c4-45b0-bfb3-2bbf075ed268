<?php

namespace App\Models\Membership;

use App\Models\MiddleModel;
use App\Models\User\User;
use App\Models\Order\Order;
use App\Models\Enums\Membership\MembershipPointSourceEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Builder;

/**
 * @property int $id
 * @property int $user_id
 * @property int $membership_level_id
 * @property int|null $order_id
 * @property float $order_amount
 * @property float $point_rate
 * @property float $earned_points
 * @property string $source_type
 * @property int|null $source_id
 * @property string|null $description
 */
class MembershipPointRecord extends MiddleModel
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
        'order_amount' => 'float',
        'point_rate' => 'float',
        'earned_points' => 'float',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 所属用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 会员等级
     */
    public function membershipLevel(): BelongsTo
    {
        return $this->belongsTo(MembershipLevel::class);
    }

    /**
     * 关联订单
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * 积分来源（多态关联）
     */
    public function source(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * 作用域：按用户筛选
     */
    public function scopeByUser(Builder $query, int $userId): Builder
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 作用域：按来源类型筛选
     */
    public function scopeBySourceType(Builder $query, string $sourceType): Builder
    {
        return $query->where('source_type', $sourceType);
    }

    /**
     * 作用域：按日期范围筛选
     */
    public function scopeByDateRange(Builder $query, $startDate, $endDate): Builder
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * 作用域：本月记录
     */
    public function scopeThisMonth(Builder $query): Builder
    {
        return $query->whereMonth('created_at', now()->month)
                    ->whereYear('created_at', now()->year);
    }

    /**
     * 作用域：本年记录
     */
    public function scopeThisYear(Builder $query): Builder
    {
        return $query->whereYear('created_at', now()->year);
    }

    /**
     * 创建订单积分记录
     */
    public static function createOrderRecord(
        int $userId,
        int $membershipLevelId,
        int $orderId,
        float $orderAmount,
        float $pointRate,
        float $earnedPoints,
        ?string $description = null
    ): self {
        return self::create([
            'user_id' => $userId,
            'membership_level_id' => $membershipLevelId,
            'order_id' => $orderId,
            'order_amount' => $orderAmount,
            'point_rate' => $pointRate,
            'earned_points' => $earnedPoints,
            'source_type' => MembershipPointSourceEnum::Order->value,
            'source_id' => $orderId,
            'description' => $description ?? '订单消费获得积分',
        ]);
    }

    /**
     * 创建生日奖励记录
     */
    public static function createBirthdayRecord(
        int $userId,
        int $membershipLevelId,
        float $earnedPoints,
        ?string $description = null
    ): self {
        return self::create([
            'user_id' => $userId,
            'membership_level_id' => $membershipLevelId,
            'order_amount' => 0,
            'point_rate' => 0,
            'earned_points' => $earnedPoints,
            'source_type' => MembershipPointSourceEnum::Birthday->value,
            'description' => $description ?? '生日奖励积分',
        ]);
    }

    /**
     * 创建活动奖励记录
     */
    public static function createPromotionRecord(
        int $userId,
        int $membershipLevelId,
        float $earnedPoints,
        $source = null,
        ?string $description = null
    ): self {
        $record = self::create([
            'user_id' => $userId,
            'membership_level_id' => $membershipLevelId,
            'order_amount' => 0,
            'point_rate' => 0,
            'earned_points' => $earnedPoints,
            'source_type' => MembershipPointSourceEnum::Promotion->value,
            'description' => $description ?? '活动奖励积分',
        ]);

        if ($source) {
            $record->source()->associate($source);
            $record->save();
        }

        return $record;
    }

    /**
     * 获取用户积分统计
     */
    public static function getUserPointStats(int $userId): array
    {
        $totalPoints = self::byUser($userId)->sum('earned_points');
        $thisMonthPoints = self::byUser($userId)->thisMonth()->sum('earned_points');
        $thisYearPoints = self::byUser($userId)->thisYear()->sum('earned_points');
        $orderPoints = self::byUser($userId)->bySourceType(MembershipPointSourceEnum::Order->value)->sum('earned_points');

        return [
            'total_points' => $totalPoints,
            'this_month_points' => $thisMonthPoints,
            'this_year_points' => $thisYearPoints,
            'order_points' => $orderPoints,
        ];
    }

    /**
     * 获取积分来源分布
     */
    public static function getPointSourceDistribution(int $userId): array
    {
        return self::byUser($userId)
            ->selectRaw('source_type, SUM(earned_points) as total_points, COUNT(*) as count')
            ->groupBy('source_type')
            ->get()
            ->mapWithKeys(function ($item) {
                return [$item->source_type => [
                    'total_points' => $item->total_points,
                    'count' => $item->count,
                ]];
            })
            ->toArray();
    }
}
