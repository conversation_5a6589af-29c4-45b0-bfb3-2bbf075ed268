<?php

namespace App\Models\Enums\Membership;

enum MembershipLevelEnum: string
{
    case Bronze = 'bronze';
    case Silver = 'silver';
    case Gold = 'gold';
    case Platinum = 'platinum';
    case Diamond = 'diamond';

    /**
     * 获取等级名称
     */
    public function getName(): string
    {
        return match ($this) {
            self::Bronze => '普通会员',
            self::Silver => '银卡会员',
            self::Gold => '金卡会员',
            self::Platinum => '白金会员',
            self::Diamond => '钻石会员',
        };
    }

    /**
     * 获取等级颜色
     */
    public function getColor(): string
    {
        return match ($this) {
            self::Bronze => '#CD7F32',
            self::Silver => '#C0C0C0',
            self::Gold => '#FFD700',
            self::Platinum => '#E5E4E2',
            self::Diamond => '#B9F2FF',
        };
    }

    /**
     * 获取积分比例
     */
    public function getPointRate(): float
    {
        return match ($this) {
            self::Bronze => 0.01,
            self::Silver => 0.015,
            self::Gold => 0.02,
            self::Platinum => 0.025,
            self::Diamond => 0.03,
        };
    }

    /**
     * 获取最低消费金额
     */
    public function getMinSpendAmount(): float
    {
        return match ($this) {
            self::Bronze => 0.00,
            self::Silver => 500.00,
            self::Gold => 2000.00,
            self::Platinum => 5000.00,
            self::Diamond => 10000.00,
        };
    }

    /**
     * 获取最高消费金额
     */
    public function getMaxSpendAmount(): ?float
    {
        return match ($this) {
            self::Bronze => 499.99,
            self::Silver => 1999.99,
            self::Gold => 4999.99,
            self::Platinum => 9999.99,
            self::Diamond => null, // 无上限
        };
    }

    /**
     * 获取所有等级选项
     */
    public static function options(): array
    {
        return array_map(fn($case) => [
            'value' => $case->value,
            'label' => $case->getName(),
            'color' => $case->getColor(),
            'point_rate' => $case->getPointRate(),
            'min_spend' => $case->getMinSpendAmount(),
            'max_spend' => $case->getMaxSpendAmount(),
        ], self::cases());
    }
}
