<?php

namespace App\Admin\Controllers;

use App\Constants\Permissions;
use App\Exceptions\DataException;
use App\Http\Controllers\Controller;
use App\Models\Attachment;
use App\Models\Collection;
use App\Models\Enums\CollectionJumpTypeEnum;
use App\Models\Product\Product;
use App\Models\CollectionProducts;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rules\Enum;
use Illuminate\Validation\Rules\Exists;
use Illuminate\Validation\Rules\In;
use Illuminate\Validation\Rules\Unique;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

class CollectionController extends Controller
{

    public function __construct()
    {
        $this->hasPermissionOr(Permissions::CollectionsUpdate)->only(['store', 'patchUpdate', 'destroy', 'productUpdate']);
        $this->hasPermissionOr(Permissions::CollectionsUpdate, Permissions::CollectionsUpdate)->only(['index', 'show']);
    }

    public function treeIndex(Request $request): JsonResource
    {
        $list = Collection::query()
            ->with([
                'image:id,path,disk,module'
            ])
            ->orderBy('sort')
            ->get();
        $treeList = buildTree($list->toArray(), needTreeIds: true);
        return JsonResource::make($treeList);
    }

    /**
     * 创建
     * @param Request $request
     * @param Collection $collection
     * @return JsonResource
     */
    public function store(Request $request, Collection $collection): JsonResource
    {
        $validated = $request->validate([
            'parent_id' => ['nullable', new Exists(Collection::class, 'id')],
            'title' => ['required', 'string', 'max:128', (new Unique(Collection::class, 'title'))->whereNull('deleted_at')],
            'sort' => ['numeric'],
            'image_id' => ['nullable', new Exists(Attachment::class, 'id')],
            'image_banner_id' => ['nullable', new Exists(Attachment::class, 'id')],
            'jump_type' => ['int', new Enum(CollectionJumpTypeEnum::class)],
            'url' => ['required_if:jump_type,' . CollectionJumpTypeEnum::Link->value, 'nullable', 'string', 'max:512'],
            'active' => ['bool'],
            'is_banner_hidden' => ['bool'],
            // 'is_related' => ['bool'],
            'extra' => ['array'],
            'meta_title' => ['nullable', 'max:255'],
            'meta_description' => ['nullable', 'max:512'],
            'associated_ids' => ['array'],
            'associated_ids.*' => ['int', new Exists(Collection::class, 'id')],
            'tab_ids' => ['array'],
            'tab_ids.*' => ['int', new Exists(Collection::class, 'id')],
        ]);
        DB::beginTransaction();
        try {
            // 推荐集合IDs
            $associated_ids = Arr::pull($validated, 'associated_ids', []);
            // tab 集合IDs
            $tab_ids = Arr::pull($validated, 'tab_ids', []);
            // 集合信息
            $collection->fill($validated)->save();
            // 关联集合
            $collection->associatedCollections()->sync($associated_ids);
            // tab 集合
            $collection->tabCollections()->sync($tab_ids);
            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw  new DataException($throwable->getMessage());
        }

        return JsonResource::make($collection);
    }

    /**
     * 商品更新
     * @param Request $request
     * @param Collection $collection
     * @return JsonResource
     */
    public function productUpdate(Request $request, Collection $collection): JsonResource
    {
        $validated = $request->validate([
            'type' => ['required', new In(['attach', 'detach', 'sync'])],
            'product_ids' => ['array'],
            'product_ids.*' => ['int', new Exists(Product::class, 'id')],
        ]);
        $productIds = Arr::get($validated, 'product_ids');
        // 处理
        if (is_array($productIds)) {
            // 瞎写的,别骂,想改自己改,做到这里时候不确定前端给什么
            switch (Arr::get($validated, 'type')) {
                case 'attach':
                    $collection->products()->sync($productIds, false);
                    break;
                case 'detach':
                    $collection->products()->detach($productIds);
                    break;
                default:
                    $collection->products()->sync($productIds);
                    break;
            }
        }
        $collection->loadMissing(['products:id']);
        $collection->setHidden(['products']);
        return JsonResource::make($collection);

    }

    /**
     * 修改部分值
     * @param Request $request
     * @param Collection $collection
     * @return JsonResource
     */
    public function patchUpdate(Request $request, Collection $collection): JsonResource
    {
        $validated = $request->validate([
            'parent_id' => ['nullable', new Exists(Collection::class, 'id')],
            'title' => ['string', 'max:128', (new Unique(Collection::class, 'title'))->ignore($collection)->whereNull('deleted_at')],
            'sort' => ['numeric'],
            'image_id' => ['nullable', new Exists(Attachment::class, 'id')],
            'image_banner_id' => ['nullable', new Exists(Attachment::class, 'id')],
            'jump_type' => ['int', new Enum(CollectionJumpTypeEnum::class)],
            'url' => ['required_if:jump_type,' . CollectionJumpTypeEnum::Link->value, 'nullable', 'string', 'max:512'],
            'active' => ['bool'],
            'is_banner_hidden' => ['bool'],
            // 'is_related' => ['bool'],
            'extra' => ['array'],
            'meta_title' => ['nullable', 'max:255'],
            'meta_description' => ['nullable', 'max:512'],
            'associated_ids' => ['array'],
            'associated_ids.*' => ['int', new Exists(Collection::class, 'id')],
            'tab_ids' => ['array'],
            'tab_ids.*' => ['int', new Exists(Collection::class, 'id')],
        ]);
        // 推荐集合Ids
        $associated_ids = Arr::pull($validated, 'associated_ids', []);
        // tab 集合Ids
        $tab_ids = Arr::pull($validated, 'tab_ids', []);
        // 更新部分字段
        if (is_array($validated) && $validated) {
            $collection->update($validated);
        }
        // 关联集合
        if (is_array($associated_ids)) {
            $collection->associatedCollections()->sync($associated_ids);
        }
        // tab 集合
        if (is_array($tab_ids)) {
            $collection->tabCollections()->sync($tab_ids);
        }
        return JsonResource::make($collection);
    }


    /**
     *
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $builder = QueryBuilder::for(Collection::class, $request)
            ->with([
                'image:id,path,disk,module',
                'imageBanner:id,path,disk,module'
            ])
            ->allowedFilters([
                AllowedFilter::partial('is_related'),
                AllowedFilter::partial('title'),
                AllowedFilter::partial('slug_title'),
                AllowedFilter::partial('active'),
                AllowedFilter::exact('parent_id'),
            ])
            ->allowedSorts(['id', 'sort', 'active'])
            ->defaultSort('-sort');
        $res = $builder->paginate($this->getPerPage());
        // 处理每个集合项，递归拼接父级title
        $res->getCollection()->transform(function ($item) {
            $item->title = $this->getFullTitle($item);  // 拼接 title
            return $item;
        });
        return JsonResource::collection($res);
    }

    // 递归拼接父级 title
    public function getFullTitle($item)
    {
        $titles = [$item->title];
        $parent = $item->parent;

        while ($parent) {
            array_unshift($titles, $parent->title);
            $parent = $parent->parent;
        }

        return implode(' / ', $titles);
    }


    public function show(Collection $collection): JsonResource
    {
        $collection
            ->loadMissing([
                'parent',
                'image:path,id,module',
                'imageBanner:path,id,disk,module',
                'children',
                'associatedCollections',
                'tabCollections'
            ]);
        return JsonResource::make($collection);
    }

    /**
     * 删除
     * @param Request $request
     * @return JsonResponse
     */
    public function destroy(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ids' => ['array', 'required'],
            'ids.*' => [(new Exists(Collection::class, 'id'))],
        ]);
        $ids = Arr::get($validated, 'ids');
        //找到ids所属的所有包含自己的子集
        $allIds = $this->getAllChildIds($ids);
        //查询是否绑定产品
        $collectionProducts = CollectionProducts::query()->whereIn('collection_id', $allIds)->get();
        if($collectionProducts->isNotEmpty()){
            throw new DataException('Please unbind the product first');
        }

        // 删除
        Collection::query()->whereIn('id', $allIds)
            ->update([
                'deleted_at' => now()
            ]);
        return response()->json();
    }

    // 启用禁用
    public function status(Request $request, Collection $collection): JsonResource{
        $validated = $request->validate([
            'active' => ['required', 'boolean'],
        ]);
        $collection->update($validated);
        return JsonResource::make($collection);
    }

    public function getAllChildIds(array $ids)
    {
        $allIds = $ids;
        $checkedIds = [];
        $queue = $ids;

        while (!empty($queue)) {
            // 查询当前层的所有子ID
            $childIds = Collection::whereIn('parent_id', $queue)->pluck('id')->toArray();
            // 过滤掉已检查过的，避免重复查询
            $childIds = array_diff($childIds, $checkedIds);

            if (empty($childIds)) {
                break;
            }
            // 记录已检查过的ID
            $checkedIds = array_merge($checkedIds, $queue);
            // 更新所有ID
            $allIds = array_merge($allIds, $childIds);

            $queue = $childIds;
        }

        return array_unique($allIds);
    }

    /**
     * 选择
     * @param \Illuminate\Http\Request $request
     * @return JsonResource
     */
    public function options(Request $request): JsonResource
    {
        $res =  QueryBuilder::for(Collection::class, $request)
        ->allowedFilters([
            AllowedFilter::partial('title'),
            AllowedFilter::exact('jump_type'),
        ])
        ->select([DB::raw('title as label'), DB::raw('id as value')])
        ->get();

        $res->each(function ($item) {
            $item->makeHidden(['product_ids']);
        });

        return JsonResource::make($res);
    }

}
