<?php

namespace App\Apis\Bing;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class BingAddressService
{
    public static function autoSuggest(string $query, string $ct)
    {
        $param = [
            'query' => $query,
            'includeEntityTypes' => 'Address',
            'countryFilter' => $ct,
            'maxResults' => 10,
        ];
        $response = BingAddressClient::getInstance()->get('/REST/v1/Autosuggest', $param);
        try {
            return $response->json('resourceSets.0.resources.0.value', []);
        } catch (\Throwable $e) {
            Log::error('Bing Search' . $e->getMessage());
        }
        return [];
    }
}
