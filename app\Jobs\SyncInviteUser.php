<?php

namespace App\Jobs;

use App\Models\Enums\User\InviteUserTypeEnum;
use App\Models\SharingRule;
use App\Models\User\InviteUser;
use App\Models\User\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SyncInviteUser implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 同步邀请用户
     */
    public function __construct(public User $user)
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $user = $this->user;
        // 获取所有上级
        $parents = $user->allParents()->get();
        // 同步邀请用户
        foreach ($parents as $parent) {
            // 新增
            $inviteUser = new InviteUser();
            $inviteUser->user_id = $parent->id;
            $inviteUser->invited_user_id = $user->id;
            if ($user->parent_id == $parent->id) {
                // 专属粉丝
                $inviteUser->type = InviteUserTypeEnum::ExclusiveFans->value;
            } else {
                // 普通粉丝
                $inviteUser->type = InviteUserTypeEnum::OrdinaryFans->value;
            }
            $inviteUser->created_at = date('Y-m-d H:i:s');
            $inviteUser->save();
        }
    }
}
