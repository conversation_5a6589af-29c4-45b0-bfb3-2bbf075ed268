<?php

namespace App\Models\Task;

use App\Models\User\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserTaskRecord extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'task_config_id',
        'task_type',
        'channel',
        'earned_points',
        'task_data',
        'external_id',
        'completed_at',
    ];

    protected $casts = [
        'user_id' => 'integer',
        'task_config_id' => 'integer',
        'earned_points' => 'float',
        'task_data' => 'array',
        'completed_at' => 'datetime',
    ];

    /**
     * 关联用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 关联任务配置
     */
    public function taskConfig(): BelongsTo
    {
        return $this->belongsTo(TaskPointConfig::class, 'task_config_id');
    }

    /**
     * 作用域：按用户
     */
    public function scopeByUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 作用域：按任务类型
     */
    public function scopeByTaskType($query, string $taskType)
    {
        return $query->where('task_type', $taskType);
    }

    /**
     * 作用域：今日完成的
     */
    public function scopeToday($query)
    {
        return $query->whereDate('completed_at', today());
    }

    /**
     * 作用域：本月完成的
     */
    public function scopeThisMonth($query)
    {
        return $query->whereMonth('completed_at', now()->month)
                    ->whereYear('completed_at', now()->year);
    }

    /**
     * 创建任务记录
     */
    public static function createRecord(
        int $userId,
        int $taskConfigId,
        string $taskType,
        ?string $channel,
        float $earnedPoints,
        ?array $taskData = null,
        ?string $externalId = null
    ): self {
        return self::create([
            'user_id' => $userId,
            'task_config_id' => $taskConfigId,
            'task_type' => $taskType,
            'channel' => $channel,
            'earned_points' => $earnedPoints,
            'task_data' => $taskData,
            'external_id' => $externalId,
            'completed_at' => now(),
        ]);
    }
}
