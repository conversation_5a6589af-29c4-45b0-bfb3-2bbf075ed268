<?php

namespace App\Admin\Controllers;

use App\Constants\Permissions;
use App\Http\Controllers\Controller;
use App\Models\Brand;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rules\Exists;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

class BrandController extends Controller
{
    //
    public function __construct()
    {
        // 编辑权限
        $this->hasPermissionOr(Permissions::BrandsUpdate)->only(['store', 'update', 'destroy']);
        $this->hasPermissionOr(Permissions::BrandsIndex, Permissions::BrandsUpdate)->only(['index', 'show']);
    }

    public function options(Request $request): AnonymousResourceCollection
    {
        $res = QueryBuilder::for(Brand::class, $request)
            ->allowedFilters([
                AllowedFilter::partial('name'),
            ])
            ->select([DB::raw('name as label'), DB::raw('id as value')])
            ->get();
        return JsonResource::collection($res);
    }

    /**
     * 用户列表
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $builder = QueryBuilder::for(Brand::class, $request)
            ->allowedFilters([
                AllowedFilter::partial('name'),
            ])
            ->allowedSorts(['id'])
            ->defaultSort('-id');
        $res = $builder->paginate($this->getPerPage());
        return JsonResource::collection($res);
    }

    public function show(Brand $brand): JsonResource
    {
        return JsonResource::make($brand);
    }

    /**
     * 修改
     * @param Request $request
     * @param Brand $brand
     * @return JsonResource
     */
    public function update(Request $request, Brand $brand): JsonResource
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:64'],
            'spu_key' => ['required', 'string', 'max:12'],
        ]);
        $brand->update($validated);

        return JsonResource::make($brand);
    }


    /**
     * 创建
     * @param Request $request
     * @param Brand $brand
     * @return JsonResource
     */
    public function store(Request $request, Brand $brand): JsonResource
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:64'],
            'spu_key' => ['required', 'string', 'max:12'],
        ]);
        $brand->update($validated);

        return JsonResource::make($brand);
    }


    /**
     * 删除
     * @param Request $request
     * @return JsonResponse
     */
    public function destroy(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ids' => ['array', 'required'],
            'ids.*' => [(new Exists(Brand::class, 'id'))],
        ]);
        $ids = Arr::get($validated, 'ids');

        // 删除
        Brand::query()->whereIn('id', $ids)
            ->update([
                'deleted_at' => now()
            ]);
        return response()->json();
    }

}
