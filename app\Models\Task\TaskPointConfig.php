<?php

namespace App\Models\Task;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class TaskPointConfig extends Model
{
    use HasFactory;

    protected $fillable = [
        'task_type',
        'task_name',
        'channel',
        'points_per_action',
        'daily_limit',
        'total_limit',
        'is_active',
        'extra_config',
        'description',
        'sort_order',
    ];

    protected $casts = [
        'points_per_action' => 'float',
        'daily_limit' => 'integer',
        'total_limit' => 'integer',
        'is_active' => 'boolean',
        'extra_config' => 'array',
        'sort_order' => 'integer',
    ];

    /**
     * 关联用户任务记录
     */
    public function userTaskRecords(): HasMany
    {
        return $this->hasMany(UserTaskRecord::class, 'task_config_id');
    }

    /**
     * 作用域：启用的任务
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 作用域：按排序
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('id');
    }

    /**
     * 作用域：按任务类型
     */
    public function scopeByTaskType($query, string $taskType)
    {
        return $query->where('task_type', $taskType);
    }

    /**
     * 作用域：按渠道
     */
    public function scopeByChannel($query, ?string $channel)
    {
        if ($channel) {
            return $query->where('channel', $channel);
        }
        return $query->whereNull('channel');
    }

    /**
     * 检查用户今日是否还能执行此任务
     */
    public function canUserExecuteToday(int $userId): bool
    {
        if ($this->daily_limit <= 0) {
            return true; // 无限制
        }

        $todayCount = UserTaskRecord::where('user_id', $userId)
            ->where('task_config_id', $this->id)
            ->whereDate('completed_at', today())
            ->count();

        return $todayCount < $this->daily_limit;
    }

    /**
     * 检查用户总共是否还能执行此任务
     */
    public function canUserExecuteTotal(int $userId): bool
    {
        if ($this->total_limit <= 0) {
            return true; // 无限制
        }

        $totalCount = UserTaskRecord::where('user_id', $userId)
            ->where('task_config_id', $this->id)
            ->count();

        return $totalCount < $this->total_limit;
    }

    /**
     * 检查用户是否可以执行此任务
     */
    public function canUserExecute(int $userId): bool
    {
        return $this->is_active && 
               $this->canUserExecuteToday($userId) && 
               $this->canUserExecuteTotal($userId);
    }

    /**
     * 获取用户今日已完成次数
     */
    public function getUserTodayCount(int $userId): int
    {
        return UserTaskRecord::where('user_id', $userId)
            ->where('task_config_id', $this->id)
            ->whereDate('completed_at', today())
            ->count();
    }

    /**
     * 获取用户总完成次数
     */
    public function getUserTotalCount(int $userId): int
    {
        return UserTaskRecord::where('user_id', $userId)
            ->where('task_config_id', $this->id)
            ->count();
    }

    /**
     * 获取任务类型选项
     */
    public static function getTaskTypeOptions(): array
    {
        return [
            'registration' => '注册',
            'birthday' => '生日奖励',
            'wechat_follow' => '关注微信',
            'weibo_follow' => '关注微博',
            'ins_follow' => '关注INS',
            'ytb_follow' => '关注YTB',
            'fb_share' => '在FB上分享',
            'twitter_share' => '在Twitter上分享',
            'post_review' => '发表审核通过的帖子',
            'video_review' => '发表审核通过的带视频评论',
            'check_in' => '签到',
            'questionnaire' => '问卷',
            'fb_group_join' => '加入FB群组',
        ];
    }

    /**
     * 获取渠道选项
     */
    public static function getChannelOptions(): array
    {
        return [
            'wechat' => '微信',
            'weibo' => '微博',
            'instagram' => 'Instagram',
            'youtube' => 'YouTube',
            'facebook' => 'Facebook',
            'twitter' => 'Twitter',
            'website' => '官网',
        ];
    }
}
