<?php

namespace App\Jobs;

use App\Models\Enums\Order\OrderStatusEnum;
use App\Models\Order\Order;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class CreateUserInviteReward implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 新增订单返现奖励
     */
    public function __construct(public OrderStatusEnum $status, public Order $order)
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        if ($this->status === OrderStatusEnum::Paid) {
            orderService()->createUserInviteReward($this->order);
        } elseif ($this->status === OrderStatusEnum::Cancel) {
            $this->order->inviteRewards()->update(['is_effective' => false]);
        }
    }
}

