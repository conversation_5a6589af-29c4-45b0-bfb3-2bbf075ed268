<?php

namespace App\Admin\Controllers;

use App\Constants\Permissions;
use App\Http\Controllers\Controller;
use App\Models\Category;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rules\Exists;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

class CategoryController extends Controller
{
    public function __construct()
    {
        $this->hasPermissionOr(Permissions::CategoriesUpdate)->only(['store', 'update', 'destroy']);
        $this->hasPermissionOr(Permissions::CategoriesUpdate, Permissions::CategoriesIndex)->only(['index', 'show']);
    }

    public function options(Request $request): AnonymousResourceCollection
    {
        $res = QueryBuilder::for(Category::class, $request)
            ->allowedFilters([
                AllowedFilter::partial('name'),
            ])
            ->select([DB::raw('name as label'), DB::raw('id as value')])
            ->get();
        return JsonResource::collection($res);
    }

    /**
     *
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $builder = QueryBuilder::for(Category::class, $request)
            ->allowedFilters([
                AllowedFilter::partial('name'),
            ])
            ->allowedSorts(['id'])
            ->defaultSort('-id');
        $res = $builder->paginate($this->getPerPage());
        return JsonResource::collection($res);
    }

    public function show(Category $category): JsonResource
    {
        return JsonResource::make($category);
    }

    /**
     * 修改
     * @param Request $request
     * @param Category $category
     * @return JsonResource
     */
    public function update(Request $request, Category $category): JsonResource
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:64'],
            'spu_key' => ['required', 'string', 'max:12'],
        ]);
        $category->update($validated);

        return JsonResource::make($category);
    }


    /**
     * 创建
     * @param Request $request
     * @param Category $category
     * @return JsonResource
     */
    public function store(Request $request, Category $category): JsonResource
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:64'],
            'spu_key' => ['required', 'string', 'max:12'],
        ]);
        $category->update($validated);

        return JsonResource::make($category);
    }


    /**
     * 删除
     * @param Request $request
     * @return JsonResponse
     */
    public function destroy(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ids' => ['array', 'required'],
            'ids.*' => [(new Exists(Category::class, 'id'))],
        ]);
        $ids = Arr::get($validated, 'ids');

        // 删除
        Category::query()->whereIn('id', $ids)
            ->update([
                'deleted_at' => now()
            ]);
        return response()->json();
    }

}
