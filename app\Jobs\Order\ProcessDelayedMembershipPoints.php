<?php

namespace App\Jobs\Order;

use App\Constants\QueueKey;
use App\Models\Membership\MembershipPointRecord;
use App\Models\Enums\User\WalletChangeTypeEnum;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class ProcessDelayedMembershipPoints implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 处理延迟发放的会员积分
     */
    public function __construct(public MembershipPointRecord $pointRecord)
    {
        $this->onQueue(QueueKey::Order->value);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            // 检查积分记录是否已经发放
            if ($this->pointRecord->points_distributed) {
                return;
            }

            // 检查用户是否存在
            $user = $this->pointRecord->user;
            if (!$user) {
                return;
            }

            DB::beginTransaction();
            try {
                // 发放积分到用户钱包
                userWalletService()->walletChangePoint(
                    $user->wallet,
                    WalletChangeTypeEnum::MembershipOrder,
                    $this->pointRecord->earned_points,
                    "会员订单积分奖励（延迟发放）",
                    $this->pointRecord->order
                );

                // 标记积分已发放
                $this->pointRecord->update([
                    'points_distributed' => true,
                    'points_distributed_at' => now(),
                ]);

                DB::commit();

                Log::info('Delayed membership points distributed successfully', [
                    'point_record_id' => $this->pointRecord->id,
                    'user_id' => $this->pointRecord->user_id,
                    'order_id' => $this->pointRecord->order_id,
                    'earned_points' => $this->pointRecord->earned_points,
                    'membership_level_id' => $this->pointRecord->membership_level_id,
                ]);

            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }

        } catch (\Exception $e) {
            Log::error('Failed to process delayed membership points', [
                'point_record_id' => $this->pointRecord->id,
                'user_id' => $this->pointRecord->user_id,
                'order_id' => $this->pointRecord->order_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // 重新抛出异常以便队列系统处理重试
            throw $e;
        }
    }

    /**
     * 处理失败的任务
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('ProcessDelayedMembershipPoints job failed permanently', [
            'point_record_id' => $this->pointRecord->id,
            'user_id' => $this->pointRecord->user_id,
            'order_id' => $this->pointRecord->order_id,
            'error' => $exception->getMessage(),
        ]);
    }
}
