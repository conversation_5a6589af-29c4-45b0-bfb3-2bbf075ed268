<?php

namespace App\Models\Enums\Promotion;

use Illuminate\Support\Arr;

enum PromotionStatusEnum: int
{
    case StatusDisabled = 0;
    case StatusEnable = 1;

    public function desc(): string
    {
        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::StatusEnable->value => 'Enable',
            self::StatusDisabled->value => 'Disabled',
        ];
    }

}
