<?php

namespace App\Models\Enums\User;

use Illuminate\Support\Arr;

enum UserStatusEnum: int
{
    case  StatusDisabled = 0;
    case  StatusEnable = 1;

    public function desc(): string
    {
//        return match ($this) {
//            self::StatusEnable => "开启",
//            self::StatusDisabled => "禁用",
//        };
        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::StatusEnable->value => 'Enable',
            self::StatusDisabled->value => 'Disabled',
        ];
    }

}
