<?php

namespace App\Models\Enums\Discount;

use Illuminate\Support\Arr;

enum DiscountAmountTypeEnum: int
{
    case  FixedAmount = 1;
    case  PercentAmount = 2;

    case  RandomAmount = 3;

    public function desc(): string
    {
        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::FixedAmount->value => '固定金额',
            self::PercentAmount->value => '百分比金额',
            self::RandomAmount->value => '随机金额',
        ];
    }

}
