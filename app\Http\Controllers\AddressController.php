<?php

namespace App\Http\Controllers;


use App\Apis\Bing\BingAddressService;
use App\Models\Country;
use App\Models\Zone;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Arr;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\AllowedInclude;
use Spatie\QueryBuilder\QueryBuilder;
use App\Models\CountryCurrency;

class AddressController extends Controller
{
    // 获取国家列表(分页)
    public function countries(Request $request): AnonymousResourceCollection
    {
        $builder = QueryBuilder::for(Country::class, $request)
            ->allowedFilters([
                AllowedFilter::partial('name'),
                AllowedFilter::exact('iso_code'),
                AllowedFilter::exact('zone_id'),
                AllowedFilter::exact('id'),
            ])
            ->allowedIncludes([
                AllowedInclude::relationship('states', 'states:name,country_id,iso_code'),
            ])
            ->paginate($this->getPerPage());

        return JsonResource::collection($builder);
    }

    // 选项数据国家列表
    public function optionCountries(Request $request)
    {
        $res = QueryBuilder::for(Country::class, $request)
            ->allowedFilters([
                AllowedFilter::partial('name'),
                AllowedFilter::exact('iso_code'),
                AllowedFilter::exact('zone_id'),
                AllowedFilter::exact('id'),
            ])
            ->allowedIncludes([
                AllowedInclude::relationship('states', 'states:name,country_id,iso_code'),
            ])
            ->get();
        return JsonResource::collection($res);
    }

    // 州列表
    public function zones(Request $request): AnonymousResourceCollection
    {
        $builder = QueryBuilder::for(Zone::class, $request)
            ->allowedFilters([
                AllowedFilter::partial('name'),
            ])
            ->paginate($this->getPerPage());

        return JsonResource::collection($builder);
    }

    /**
     * @param Request $request
     * @return JsonResource
     */
    public function addressMatch(Request $request): JsonResource
    {
        $validated = $request->validate([
            'query' => 'string|required',
            'ct' => 'string'
        ]);
        $list = BingAddressService::autoSuggest($validated['query'], Arr::get($validated, 'ct', 'US'));
        return JsonResource::make(Arr::map($list, function ($row) {
            return Arr::get($row, 'address');
        }));
    }

    public function countryCurrencies(Request $request): JsonResource
    {
        $query = QueryBuilder::for(CountryCurrency::class)
            ->with(['image:id,path,disk,module', 'country:id,name,iso_code', 'currency:id,currency_code,symbol'])
            ->allowedFilters([
                AllowedFilter::exact('active'),
                AllowedFilter::partial('currency_code'),
                AllowedFilter::partial('country_code'),
            ])
            ->allowedSorts(['country_code', 'currency_code'])
            ->defaultSort('country_code');

        if ($request->boolean('all', false)) {
            return JsonResource::collection($query->get());
        }

        return JsonResource::collection($query->paginate($this->getPerPage()));
    }
}
