# 延迟积分发放功能实现

## 概述

根据用户需求，实现了以下积分发放策略：
- **任务积分**：立即发放（保持现状）
- **下单积分**：延迟发放，延迟天数从 `sharing_rules` 表获取

## 实现细节

### 1. 数据库变更

#### 新增字段到 `membership_point_records` 表：
- `points_distributed` (boolean): 积分是否已发放
- `points_distributed_at` (timestamp): 积分发放时间
- `scheduled_distribution_at` (timestamp): 计划发放时间

#### 迁移文件：
- `database/migrations/2025_07_02_120000_add_delayed_distribution_to_membership_point_records.php`

### 2. 核心功能修改

#### MembershipService 类增强：
- 新增 `getPointDelayDays()` 方法：从 `sharing_rules` 表获取延迟天数
- 修改 `processOrderPoints()` 方法：支持延迟发放逻辑

#### MembershipPointRecord 模型增强：
- 新增字段的 cast 配置
- 修改 `createOrderRecord()` 方法支持延迟发放参数
- 新增查询作用域：
  - `pendingDistribution()`: 待发放的积分
  - `readyForDistribution()`: 可发放的积分（已到时间）
  - `distributed()`: 已发放的积分

### 3. 延迟处理机制

#### 新增 Job：
- `app/Jobs/Order/ProcessDelayedMembershipPoints.php`
- 处理单个延迟积分记录的发放

#### 新增命令：
- `app/Console/Commands/ProcessDelayedMembershipPoints.php`
- 批量处理到期的延迟积分
- 支持 `--dry-run` 和 `--limit` 参数

### 4. 配置获取逻辑

延迟天数从 `sharing_rules` 表获取，条件：
- `is_global = true`
- `is_publish = true`
- 从 `condition` JSON 字段中读取 `days` 值

配置示例：
```json
{
    "enabled": true,
    "days": 45
}
```

- 如果 `enabled = false`，则立即发放（延迟0天）
- 如果 `enabled = true`，则延迟 `days` 天发放
- 如果没有找到规则，默认延迟45天

## 使用方法

### 1. 设置延迟配置

在 `sharing_rules` 表中设置全局规则：
```sql
UPDATE sharing_rules 
SET condition = '{"enabled": true, "days": 30}' 
WHERE is_global = true AND is_publish = true;
```

### 2. 处理延迟积分

运行命令处理到期的延迟积分：
```bash
# 正常处理
php artisan membership:process-delayed-points

# 预览模式（不实际处理）
php artisan membership:process-delayed-points --dry-run

# 限制处理数量
php artisan membership:process-delayed-points --limit=50
```

### 3. 定时任务配置

建议在 `app/Console/Kernel.php` 中添加定时任务：
```php
protected function schedule(Schedule $schedule)
{
    $schedule->command('membership:process-delayed-points')
             ->hourly()
             ->withoutOverlapping();
}
```

## 工作流程

### 下单积分处理流程：

1. 用户下单并支付成功
2. `ProcessMembershipRewards` Job 被触发
3. `MembershipService::processOrderPoints()` 被调用
4. 获取延迟天数配置
5. 创建积分记录：
   - 如果延迟天数 = 0：立即发放积分，标记为已发放
   - 如果延迟天数 > 0：创建待发放记录，设置计划发放时间
6. 定时任务定期检查并处理到期的延迟积分

### 任务积分处理流程：

任务积分保持原有逻辑，立即发放，不受延迟配置影响。

## 测试

创建了测试文件 `tests/Feature/DelayedMembershipPointsTest.php`，包含：
- 延迟发放功能测试
- 立即发放功能测试
- 配置获取功能测试

## 监控和日志

系统会记录以下日志：
- 延迟积分创建
- 延迟积分发放成功/失败
- 配置获取异常

## 兼容性

- 现有的任务积分系统不受影响
- 现有的立即发放逻辑作为延迟天数=0的特殊情况保留
- 向后兼容，不影响现有数据
