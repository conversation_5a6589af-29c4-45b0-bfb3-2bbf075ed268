<?php

namespace App\Models\Order;

use App\Constants\QueueKey;
use App\Jobs\CouponGrantCheck;
use App\Jobs\CreateUserInviteReward;
use App\Jobs\EmailRuleNotice;
use App\Jobs\Order\OrderHistoryRecord;
use App\Jobs\Order\OrderReleaseProductStock;
use App\Jobs\OrderSaveUserAddress;
use App\Jobs\Order\OrderIsFirstOrder;
use App\Jobs\Order\ProcessMembershipRewards;
use App\Jobs\Product\ProductSyncSaleNum;
use App\Models\Currency;
use App\Models\Enums\CouponGrantType;
use App\Models\Enums\EmailRuleEventEnum;
use App\Models\Enums\Order\OrderAddressTypeEnum;
use App\Models\Enums\Order\OrderPaidStatusEnum;
use App\Models\Enums\Order\OrderPaidTypeEnum;
use App\Models\Enums\Order\OrderStatusEnum;
use App\Models\Order\Payment\Payment;
use App\Models\Order\Payment\PaypalOrder;
use App\Models\Shipping;
use App\Models\User\UserInviteReward;
use App\Models\User\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\MiddleModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use DB;

/**
 * @property int $user_id
 * @property OrderStatusEnum $status
 * @property OrderPaidTypeEnum $paid_type
 * @property Carbon $paid_at
 * @property bool $closed
 * @property OrderPaidStatusEnum $paid_status
 * @property ?PaypalOrder $paypalOrder
 * @property Payment $payment
 * @property int $id
 * @property float $shipping_fee
 * @property string $no
 * @property string $shipping_number
 * @property string $email
 * @property OrderAddress $billingAddress
 * @property OrderAddress $shippingAddress
 * @property bool $subscribed
 * @property UserInviteReward $inviteRewards
 */
class Order extends MiddleModel
{
    use HasFactory;

    protected $guarded = [];
    protected $casts = [
        'paid_at' => 'datetime',
        'status' => OrderStatusEnum::class,
        'paid_type' => OrderPaidTypeEnum::class,
        'paid_status' => OrderPaidStatusEnum::class,
        'shipping_fee' => 'decimal:2',
        'shipping_fee_discount' => 'decimal:2',
        'subtotal_price' => 'decimal:2',
        'total_price' => 'decimal:2',
        'discount_price' => 'decimal:2',
        'subscribed' => 'bool',
        'valid' => 'bool',
        'paid_amount' => 'decimal:2',
        'total' => 'decimal:2',
        'coupon_discount_price' => 'decimal:2',
        'product_info' => 'json'
    ];

    protected $appends = [
        'delivery_fee_real',
        'delivery_fee_real_agent',
        'shipping_fee_agent',
        'shipping_fee_discount_agent',
        'subtotal_price_agent',
        'total_price_agent',
        'discount_price_agent',
        'total_agent',
        'payment_method',
    ];

    public static function booted()
    {
        static::creating(function (self $model) {
            // 生成订单号 ARD+时间戳到秒+随机数字3位
            $model->no ??= 'ARD' . date('YmdHis') . mt_rand(100, 999);
            $model->user_type = $model->user?->isGuest() ? 1 : 0;
        });
        static::saving(function (self $model) {
            if ($model->isDirty(['status'])) {
                // 是否为有效订单
                $model->valid = $model->status->valid();
                // 订单状态变更
                OrderHistoryRecord::dispatch($model->status, $model)->onQueue(QueueKey::Order->value);
            }
        });
        static::saved(function (self $model) {
            if ($model->isDirty(['status'])) {
                // 是否首单
                OrderIsFirstOrder::dispatch($model)->onQueue(QueueKey::Order->value);
                // 取消或者退款
                if (in_array($model->status, [OrderStatusEnum::Refund, OrderStatusEnum::Cancel])) {
                    // 释放库存
                    OrderReleaseProductStock::dispatch($model)->onQueue(QueueKey::Order->value);
                }
                // 已付款
                if ($model->status == OrderStatusEnum::Paid) {
                    // 加载订单详情和相关数据
                    $model->load([
                        'items.product',
                        'billingAddress',
                        'shippingAddress',
                        'currency',
                        'user',
                        'items.userCoupon'
                    ]);
                    // 扣掉个人优惠券
                    dispatch(function () use ($model) {
                        $model->items()->with(['userCoupon'])->each(function (OrderItem $orderItem) {
                            $orderItem?->userCoupon?->update([
                                'used_at' => now()
                            ]);
                        });
                        $model->items()->with(['coupon'])->each(function (OrderItem $orderItem) use ($model) {
                            // 更新全局优惠券使用次数
                            if ($orderItem->coupon && $orderItem->coupon->is_global && !$orderItem->coupon->is_auto) {

                                // 检查用户是否已有此优惠券记录
                                $existingUserCoupon = DB::table('user_coupons')->where('user_id', $model->user_id)
                                    ->where('coupon_id', $orderItem->coupon->id)
                                    ->first();

                                // 如果不存在，则创建一条记录
                                if (!$existingUserCoupon) {
                                    $userCouponId = DB::table('user_coupons')->insertGetId([
                                        'user_id' => $model->user_id,
                                        'coupon_id' => $orderItem->coupon->id,
                                        'code' => $orderItem->coupon->code,
                                        'effective_start_at' => $orderItem->coupon->effective_start_at,
                                        'effective_end_at' => $orderItem->coupon->effective_end_at,
                                        'used_at' => now(), // 标记为已使用
                                        'created_at' => now(),
                                        'updated_at' => now()
                                    ]);

                                    // 更新订单项，关联新创建的用户优惠券
                                    $orderItem->update(['user_coupon_id' => $userCouponId]);
                                    $orderItem->coupon->decrement('total_count');
                                }
                            }
                        });
                    })->onQueue(QueueKey::Order->value);
                    // 同步产品数量
                    ProductSyncSaleNum::dispatch($model)->onQueue(QueueKey::Product->value);
                    // 同步订单地址  不用列队，他们需要同步账单地址的时候直接同步user
                    // OrderSaveUserAddress::dispatch($model)->onQueue(QueueKey::Order->value);
                    // 首单奖励优惠券发放埋点 不用列队，他们需要直接发放优惠劵
                    // CouponGrantCheck::dispatch(CouponGrantType::FirstOrder, $model)->onQueue(QueueKey::Coupon->value);
                    if ($model->user && $model->user->parent_id) {
                        // 创建订单返现奖励
                        CreateUserInviteReward::dispatch(OrderStatusEnum::Paid, $model)->onQueue(QueueKey::Order->value);
                    }
                    // 处理会员等级升级和积分奖励
                    ProcessMembershipRewards::dispatch($model)->onQueue(QueueKey::Order->value);
                    // 分享邀请返佣埋点
                    CouponGrantCheck::dispatch(CouponGrantType::ShareInvite, $model)->onQueue(QueueKey::Coupon->value);


                    // 邮件规则推送 - 支付成功
                    $user = Auth::user();
                    EmailRuleNotice::dispatch(EmailRuleEventEnum::OrderPaidSuccess, user: $user, order: $model)->onQueue(QueueKey::Default->value);
                }
                if ($model->status == OrderStatusEnum::Received) {
                    // 邮件规则推送 - 物流更新
                    EmailRuleNotice::dispatch(EmailRuleEventEnum::LogisticsInspection, user: $user, order: $model)->onQueue(QueueKey::Default->value);
                    // 邮件规则推送 - 邀请评论
                    EmailRuleNotice::dispatch(EmailRuleEventEnum::InviteComment, user: $user, order: $model)->onQueue(QueueKey::Default->value);
                }
                if ($model->status == OrderStatusEnum::Cancel) {
                    // 取消订单返现奖励
                    CreateUserInviteReward::dispatch(OrderStatusEnum::Cancel, $model)->onQueue(QueueKey::Order->value);
                }
                // 订单状态变更
                OrderHistoryRecord::dispatch($model->status, $model)->onQueue(QueueKey::Order->value);
            }
        });
    }

    /**************** 关联模型 ****************/
    // 支付信息
    public function payment(): MorphTo
    {
        return $this->morphTo();
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function items(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }

    public function paypalOrder(): HasOne
    {
        return $this->hasOne(PaypalOrder::class);
    }

    public function billingAddress(): HasOne
    {
        return $this->hasOne(OrderAddress::class)->where('type', OrderAddressTypeEnum::Billing);
    }

    public function shippingAddress(): HasOne
    {
        return $this->hasOne(OrderAddress::class)->where('type', OrderAddressTypeEnum::Shipping);
    }

    public function shipping()
    {
        return $this->belongsTo(Shipping::class);
    }

    public function inviteRewards()
    {
        return $this->hasMany(UserInviteReward::class);
    }

    public function histories(): HasMany
    {
        return $this->hasMany(OrderHistory::class);
    }

    public function currency(): BelongsTo
    {
        return $this->belongsTo(Currency::class);
    }

    /**************** 金额币种转换 ****************/
    public function deliveryFeeRealAgent(): Attribute
    {
        return Attribute::get(
            fn() => convertPrice((float) $this->shipping_fee - $this->shipping_fee_discount, $this->currency)
        );
    }

    public function shippingFeeAgent(): Attribute
    {
        return Attribute::get(
            fn() => convertPrice($this->shipping_fee, $this->currency)
        );
    }

    public function shippingFeeDiscountAgent(): Attribute
    {
        return Attribute::get(
            fn() => convertPrice($this->shipping_fee_discount, $this->currency)
        );
    }

    public function subtotalPriceAgent(): Attribute
    {
        return Attribute::get(
            fn() => convertPrice($this->subtotal_price, $this->currency)
        );
    }

    public function totalPriceAgent(): Attribute
    {
        return Attribute::get(
            fn() => convertPrice($this->total_price, $this->currency)
        );
    }

    public function discountPriceAgent(): Attribute
    {
        return Attribute::get(
            fn() => convertPrice($this->discount_price, $this->currency)
        );
    }

    public function totalAgent(): Attribute
    {
        return Attribute::get(
            fn() => convertPrice($this->total, $this->currency)
        );
    }

    public function paymentMethod(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->paid_type?->desc() ?? '未支付',
        );
    }

    // 运费实际金额
    public function deliveryFeeReal(): Attribute
    {
        return Attribute::get(
            fn() =>  $this->shipping_fee - $this->shipping_fee_discount
        );
    }

    public function syncAddressToUser($address, $isBilling = true)
    {
        $user = $address->order->user;
        if (!$user) {
            return;
        }

        $addressData = Arr::only($address->toArray(), ['first_name', 'last_name', 'country_id', 'address', 'address1', 'state', 'city', 'zip', 'phone']);
        if ($isBilling) {
            $addressData['is_billing_default'] = true;
            $user->addresses()->update(['is_billing_default' => false]);
        } else {
            $addressData['is_shipping_default'] = true;
            $user->addresses()->update(['is_shipping_default' => false]);
        }

        $user->addresses()->create($addressData);
    }
}
