<?php

namespace App\Services;

use App\Exceptions\DataException;
use App\Models\Enums\User\WalletChangeTypeEnum;
use App\Models\User\UserWallet;
use App\Models\User\UserWalletInviteAmountRecord;
use App\Models\User\UserWalletPointRecord;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

/**
 * 金币变更相关内容
 */
class UserWalletService
{

    /**
     * @param UserWallet $wallet
     * @param WalletChangeTypeEnum $type
     * @param $point
     * @param string $desc
     * @param Model|null $source
     * @return UserWalletPointRecord
     * @throws DataException
     * @throws \Throwable
     */
    public function walletChangePoint(UserWallet $wallet, WalletChangeTypeEnum $type, $point, string $desc = "", ?Model $source = null): UserWalletPointRecord
    {
        // 取绝对值
        $point = match ($type) {
            WalletChangeTypeEnum::Consume, WalletChangeTypeEnum::Withdraw => -abs($point),
            default => abs($point)
        };

        DB::beginTransaction();
        try {
            $record = new UserWalletPointRecord([
                'user_id' => $wallet->user_id,
                'change_type' => $type,
                'change_point' => $point,
                'current_point' => min([$wallet->point + $point, 9999999999.99]),
                'before_point' => $wallet->point,
                'desc' => $desc ?: $type->desc(),
            ]);
            // 加载关系
            $source && $record->source()->associate($source);
            // 保存
            $wallet->changeRecords()->save($record);
            if ($point > 0) {
                $wallet->increment('point', $point);
            } else {
                $wallet->decrement('point', abs($point));
            }
        } catch (\Throwable $exception) {
            DB::rollBack();
            logger()->error('run in UserChangeWalletJob', [$exception->getMessage()]);
            throw  new DataException("User points operation failed.");
        }
        DB::commit();
        return $record;
    }


    /**
     * @param UserWallet $wallet
     * @param WalletChangeTypeEnum $type
     * @param $amount
     * @param string $desc
     * @param Model|null $source
     * @return UserWalletInviteAmountRecord
     * @throws DataException
     * @throws \Throwable
     */
    public function walletChangeInviteAmount(UserWallet $wallet, WalletChangeTypeEnum $type, $amount, string $desc = "", ?Model $source = null): UserWalletInviteAmountRecord
    {
        // 取绝对值
        $amount = match ($type) {
            WalletChangeTypeEnum::Consume, WalletChangeTypeEnum::Withdraw => -abs($amount),
            default => abs($amount)
        };

        DB::beginTransaction();
        try {
            $record = new UserWalletInviteAmountRecord([
                'user_id' => $wallet->user_id,
                'change_type' => $type,
                'change_amount' => $amount,
                'current_amount' => min([$wallet->invite_amount + $amount, 9999999999.99]),
                'before_amount' => $wallet->invite_amount,
                'desc' => $desc ?: $type->desc(),
            ]);
            // 加载关系
            $source && $record->source()->associate($source);
            // 保存
            $wallet->inviteAmountRecords()->save($record);
            if ($amount > 0) {
                $wallet->increment('invite_amount', $amount);
            } else {
                $wallet->decrement('invite_amount', abs($amount));
            }
        } catch (\Throwable $exception) {
            DB::rollBack();
            logger()->error('run in UserChangeWalletJob', [$exception->getMessage()]);
            throw  new DataException("User points operation failed.");
        }
        DB::commit();
        return $record;
    }

}
