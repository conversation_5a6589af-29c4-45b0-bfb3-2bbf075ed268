<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User\User;
use App\Models\Membership\MembershipLevel;
use App\Models\Membership\UserMembershipLevel;
use App\Models\Membership\MembershipPointRecord;
use App\Services\MembershipService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class MembershipSystemTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected MembershipService $membershipService;
    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->membershipService = app(MembershipService::class);
        $this->user = User::factory()->create();
        
        // 创建测试会员等级
        $this->createTestMembershipLevels();
    }

    protected function createTestMembershipLevels(): void
    {
        MembershipLevel::create([
            'name' => '普通会员',
            'slug' => 'bronze',
            'description' => '新用户默认等级',
            'min_spend_amount' => 0.00,
            'point_rate' => 0.01,
            'benefits' => ['point_rate' => '1%'],
            'color' => '#CD7F32',
            'sort_order' => 1,
            'is_active' => true,
        ]);

        MembershipLevel::create([
            'name' => '银卡会员',
            'slug' => 'silver',
            'description' => '消费满500元升级',
            'min_spend_amount' => 500.00,
            'point_rate' => 0.015,
            'benefits' => ['point_rate' => '1.5%'],
            'color' => '#C0C0C0',
            'sort_order' => 2,
            'is_active' => true,
        ]);

        MembershipLevel::create([
            'name' => '金卡会员',
            'slug' => 'gold',
            'description' => '消费满2000元升级',
            'min_spend_amount' => 2000.00,
            'point_rate' => 0.02,
            'benefits' => ['point_rate' => '2%'],
            'color' => '#FFD700',
            'sort_order' => 3,
            'is_active' => true,
        ]);
    }

    /** @test */
    public function it_can_get_level_by_spend_amount()
    {
        $bronzeLevel = MembershipLevel::getLevelBySpendAmount(100);
        $this->assertEquals('普通会员', $bronzeLevel->name);

        $silverLevel = MembershipLevel::getLevelBySpendAmount(800);
        $this->assertEquals('银卡会员', $silverLevel->name);

        $goldLevel = MembershipLevel::getLevelBySpendAmount(2500);
        $this->assertEquals('金卡会员', $goldLevel->name);
    }

    /** @test */
    public function it_can_get_lowest_level()
    {
        $lowestLevel = MembershipLevel::getLowestLevel();
        $this->assertEquals('普通会员', $lowestLevel->name);
        $this->assertEquals(1, $lowestLevel->sort_order);
    }

    /** @test */
    public function it_can_get_next_level()
    {
        $bronzeLevel = MembershipLevel::where('slug', 'bronze')->first();
        $nextLevel = $bronzeLevel->getNextLevel();
        
        $this->assertEquals('银卡会员', $nextLevel->name);
    }

    /** @test */
    public function it_can_calculate_upgrade_required_amount()
    {
        $bronzeLevel = MembershipLevel::where('slug', 'bronze')->first();
        $requiredAmount = $bronzeLevel->getUpgradeRequiredAmount(300);

        $this->assertEquals(200, $requiredAmount); // 500 - 300 = 200 (升级到银卡需要500)
    }

    /** @test */
    public function it_can_check_and_upgrade_user_level()
    {
        // 初始状态：用户没有会员等级
        $this->assertNull(UserMembershipLevel::getCurrentLevel($this->user->id));

        // 模拟用户消费600元，应该升级到银卡
        $this->membershipService->checkAndUpgradeUserLevel($this->user, 600);

        $userLevel = UserMembershipLevel::getCurrentLevel($this->user->id);
        $this->assertNotNull($userLevel);
        $this->assertEquals('银卡会员', $userLevel->membershipLevel->name);
        $this->assertEquals(600, $userLevel->total_spend_amount);

        // 再次消费1500元，总计2100元，应该升级到金卡
        $this->membershipService->checkAndUpgradeUserLevel($this->user, 2100);

        $userLevel = UserMembershipLevel::getCurrentLevel($this->user->id);
        $this->assertEquals('金卡会员', $userLevel->membershipLevel->name);
        $this->assertEquals(2100, $userLevel->total_spend_amount);
    }

    /** @test */
    public function it_can_process_order_points()
    {
        // 先设置用户为银卡会员
        $silverLevel = MembershipLevel::where('slug', 'silver')->first();
        UserMembershipLevel::createOrUpdate($this->user->id, $silverLevel->id, 600);

        // 创建模拟订单
        $order = new \stdClass();
        $order->id = 123;
        $order->user_id = $this->user->id;
        $order->total = 100.0;

        // 直接测试积分记录创建
        $expectedPoints = $order->total * $silverLevel->point_rate; // 100 * 0.015 = 1.5

        $pointRecord = MembershipPointRecord::createOrderRecord(
            $this->user->id,
            $silverLevel->id,
            $order->id,
            $order->total,
            $silverLevel->point_rate,
            $expectedPoints,
            '订单消费获得积分'
        );

        $this->assertNotNull($pointRecord);
        $this->assertEquals($expectedPoints, $pointRecord->earned_points);
        $this->assertEquals('订单消费获得积分', $pointRecord->description);
    }

    /** @test */
    public function it_can_get_user_membership_info()
    {
        // 设置用户为银卡会员
        $silverLevel = MembershipLevel::where('slug', 'silver')->first();
        UserMembershipLevel::createOrUpdate($this->user->id, $silverLevel->id, 800);

        $membershipInfo = $this->membershipService->getUserMembershipInfo($this->user);

        $this->assertNotNull($membershipInfo['current_level']);
        $this->assertEquals('银卡会员', $membershipInfo['membership_level']->name);
        $this->assertEquals(800, $membershipInfo['total_spend_amount']);
        $this->assertEquals('金卡会员', $membershipInfo['next_level']->name);
        $this->assertEquals(1200, $membershipInfo['upgrade_required_amount']); // 2000 - 800 = 1200
    }

    /** @test */
    public function it_handles_user_without_membership_level()
    {
        $membershipInfo = $this->membershipService->getUserMembershipInfo($this->user);

        $this->assertNull($membershipInfo['current_level']);
        $this->assertNotNull($membershipInfo['default_level']);
        $this->assertEquals('普通会员', $membershipInfo['default_level']->name);
    }

    /** @test */
    public function it_can_create_point_records()
    {
        $silverLevel = MembershipLevel::where('slug', 'silver')->first();
        
        $pointRecord = MembershipPointRecord::createOrderRecord(
            $this->user->id,
            $silverLevel->id,
            123, // order_id
            100.0, // order_amount
            0.015, // point_rate
            1.5, // earned_points
            'test-order-123'
        );

        $this->assertNotNull($pointRecord);
        $this->assertEquals($this->user->id, $pointRecord->user_id);
        $this->assertEquals($silverLevel->id, $pointRecord->membership_level_id);
        $this->assertEquals(123, $pointRecord->source_id);
        $this->assertEquals(1.5, $pointRecord->earned_points);
    }

    /** @test */
    public function it_can_get_benefits_description()
    {
        $silverLevel = MembershipLevel::where('slug', 'silver')->first();
        $silverLevel->update([
            'benefits' => [
                'point_rate' => '1.5%',
                'birthday_bonus' => '生日当月额外10%积分',
                'free_shipping_threshold' => 79,
                'exclusive_coupons' => '专属优惠券'
            ]
        ]);

        $benefits = $silverLevel->getBenefitsDescription();

        $this->assertContains('积分比例：1.5%', $benefits);
        $this->assertContains('生日福利：生日当月额外10%积分', $benefits);
        $this->assertContains('满79元免运费', $benefits);
        $this->assertContains('专属优惠券', $benefits);
    }

    /** @test */
    public function it_prevents_downgrade_to_lower_level()
    {
        // 设置用户为金卡会员
        $goldLevel = MembershipLevel::where('slug', 'gold')->first();
        UserMembershipLevel::createOrUpdate($this->user->id, $goldLevel->id, 2500);

        // 尝试"降级"到银卡（消费金额减少），应该保持金卡
        $this->membershipService->checkAndUpgradeUserLevel($this->user, 800);

        $userLevel = UserMembershipLevel::getCurrentLevel($this->user->id);
        $this->assertEquals('金卡会员', $userLevel->membershipLevel->name);
        $this->assertEquals(800, $userLevel->total_spend_amount); // 消费金额应该更新
    }
}
