<?php

use App\Models\Enums\Order\OrderPaidStatusEnum;
use App\Models\Enums\Order\OrderStatusEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_id')->nullable()->comment('用户')->index();
            $table->boolean('user_type')->default(false)->comment('用户类型');
            $table->string('email')->nullable()->comment('邮箱,非登录用户存在,提供完成支付后快速创建');
            $table->string('no')->index()->comment('订单号');
            // 状态
            $table->tinyInteger('status')->default(OrderStatusEnum::Unpaid)->comment('订单状态');
            $table->boolean('subscribed')->nullable()->comment('是否订阅');
            $table->boolean('valid')->comment('是否有效');
            $table->boolean('is_first_order')->default(false)->comment('是否首单');
            // 价格信息
            $table->bigInteger('shipping_id')->nullable()->comment('运费模板id');
            $table->string('shipping_type')->default('general')->comment('运费类型');
            $table->decimal('shipping_fee', 8, 2)->default(0)->comment('运费');
            $table->decimal('shipping_fee_discount', 8, 2)->default(0)->comment('运费折扣');
            $table->bigInteger('shipping_fee_discount_id')->default(0)->comment('用户使用运费优惠券id');
            $table->decimal('subtotal_price', 10, 4)->default(0)->comment('商品小计/原价 (无条件origin_price)');
            $table->decimal('total_price', 10, 4)->default(0)->comment('商品总价(需支付 origin_price-discount_price || price)');
            $table->decimal('discount_price', 10, 4)->default(0)->comment('抵扣价/优惠券部分金额');
            $table->decimal('total', 10, 4)->default(0)->comment('订单总计(商品总价+shipping_fee-shipping_fee_discount)');
            // 支付信息
            $table->decimal('paid_amount', 10, 4)->default(0)->comment('支付金额(实际支付)');
            $table->dateTime('paid_at')->nullable();
            $table->tinyInteger('paid_type')->nullable();
            $table->tinyInteger('paid_status')->default(OrderPaidStatusEnum::Unpaid)->comment('支付状态');
            $table->string('payment_type')->nullable();
            $table->bigInteger('payment_id')->nullable();
            $table->string('transaction_id')->index()->comment('交易号');

            // 备注
            $table->string('remark', 512)->nullable()->comment('备注');
            $table->string('shipping_number', 512)->nullable()->comment('物流信息');
            $table->boolean('closed')->default(false)->comment('超时关闭');
            $table->timestamps();
            $table->engine('InnoDB');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
