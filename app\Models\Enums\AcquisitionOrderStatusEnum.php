<?php

namespace App\Models\Enums;

use Illuminate\Support\Arr;

enum AcquisitionOrderStatusEnum: int
{
    case  PendingApproval = 0;
    case  Approved = 1;
    case  ApprovalRejection = 2;

    public function desc(): string
    {
//        return match ($this) {
//            self::StatusEnable => "开启",
//            self::StatusDisabled => "禁用",
//        };
        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::PendingApproval->value => '待审核',
            self::Approved->value => '审核通过',
            self::ApprovalRejection->value => '审核不通过',
        ];
    }

}
