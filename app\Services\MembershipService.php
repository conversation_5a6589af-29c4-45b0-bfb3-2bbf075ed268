<?php

namespace App\Services;

use App\Models\User\User;
use App\Models\Order\Order;
use App\Models\Membership\MembershipLevel;
use App\Models\Membership\UserMembershipLevel;
use App\Models\Membership\MembershipPointRecord;
use App\Models\Enums\User\WalletChangeTypeEnum;
use App\Models\Enums\Membership\MembershipPointSourceEnum;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class MembershipService
{
    /**
     * 检查并升级用户会员等级
     */
    public function checkAndUpgradeUserLevel(User $user): ?UserMembershipLevel
    {
        // 计算用户总消费金额
        $totalSpendAmount = $this->calculateUserTotalSpend($user);
        
        // 获取当前应该的会员等级
        $targetLevel = MembershipLevel::getLevelBySpendAmount($totalSpendAmount);
        
        if (!$targetLevel) {
            return null;
        }

        // 获取用户当前会员等级
        $currentUserLevel = UserMembershipLevel::getCurrentLevel($user->id);
        
        // 如果用户还没有会员等级或者需要升级
        if (!$currentUserLevel || $currentUserLevel->membership_level_id !== $targetLevel->id) {
            return $this->upgradeUserLevel($user, $targetLevel, $totalSpendAmount, $currentUserLevel);
        }

        // 更新消费金额
        if ($currentUserLevel) {
            $currentUserLevel->update(['total_spend_amount' => $totalSpendAmount]);
        }

        return $currentUserLevel;
    }

    /**
     * 升级用户会员等级
     */
    protected function upgradeUserLevel(
        User $user, 
        MembershipLevel $targetLevel, 
        float $totalSpendAmount, 
        ?UserMembershipLevel $currentUserLevel
    ): UserMembershipLevel {
        DB::beginTransaction();
        try {
            $fromLevelId = $currentUserLevel ? $currentUserLevel->membership_level_id : null;
            
            // 创建或更新用户会员等级
            $newUserLevel = UserMembershipLevel::createOrUpdate(
                $user->id, 
                $targetLevel->id, 
                $totalSpendAmount
            );

            // 记录升级历史
            if ($fromLevelId && $fromLevelId !== $targetLevel->id) {
                $newUserLevel->recordUpgrade($fromLevelId, $targetLevel->id, $totalSpendAmount);
                
                Log::info('User membership level upgraded', [
                    'user_id' => $user->id,
                    'from_level_id' => $fromLevelId,
                    'to_level_id' => $targetLevel->id,
                    'total_spend' => $totalSpendAmount,
                ]);
            }

            DB::commit();
            return $newUserLevel;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to upgrade user membership level', [
                'user_id' => $user->id,
                'target_level_id' => $targetLevel->id,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * 计算用户总消费金额
     */
    public function calculateUserTotalSpend(User $user): float
    {
        return $user->orders()
            ->where('paid_status', \App\Models\Enums\Order\OrderPaidStatusEnum::Paid)
            ->sum('total');
    }

    /**
     * 处理订单完成后的积分奖励
     */
    public function processOrderPoints(Order $order): ?MembershipPointRecord
    {
        if (!$order->user_id || $order->total <= 0) {
            return null;
        }

        $user = $order->user;
        if (!$user) {
            return null;
        }

        // 检查并升级会员等级
        $userLevel = $this->checkAndUpgradeUserLevel($user);
        
        if (!$userLevel) {
            return null;
        }

        // 计算积分
        $pointRate = $userLevel->membershipLevel->point_rate;
        $earnedPoints = $order->total * $pointRate;

        DB::beginTransaction();
        try {
            // 创建积分记录
            $pointRecord = MembershipPointRecord::createOrderRecord(
                $user->id,
                $userLevel->membership_level_id,
                $order->id,
                $order->total,
                $pointRate,
                $earnedPoints
            );

            // 更新用户钱包积分
            userWalletService()->walletChangePoint(
                $user->wallet,
                WalletChangeTypeEnum::MembershipOrder,
                $earnedPoints,
                "会员订单积分奖励",
                $order
            );

            DB::commit();
            
            Log::info('Order points processed', [
                'user_id' => $user->id,
                'order_id' => $order->id,
                'order_amount' => $order->total,
                'point_rate' => $pointRate,
                'earned_points' => $earnedPoints,
                'membership_level' => $userLevel->membershipLevel->name,
            ]);

            return $pointRecord;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to process order points', [
                'user_id' => $user->id,
                'order_id' => $order->id,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * 发放生日积分奖励
     */
    public function processBirthdayBonus(User $user): ?MembershipPointRecord
    {
        $userLevel = UserMembershipLevel::getCurrentLevel($user->id);
        
        if (!$userLevel) {
            return null;
        }

        $benefits = $userLevel->membershipLevel->benefits ?? [];
        $birthdayBonus = $benefits['birthday_bonus'] ?? null;
        
        if (!$birthdayBonus) {
            return null;
        }

        // 解析生日奖励（例如："生日当月额外10%积分"）
        // 这里简化处理，给予固定积分
        $bonusPoints = 100; // 可以根据会员等级调整

        DB::beginTransaction();
        try {
            // 创建积分记录
            $pointRecord = MembershipPointRecord::createBirthdayRecord(
                $user->id,
                $userLevel->membership_level_id,
                $bonusPoints,
                "生日奖励积分"
            );

            // 更新用户钱包积分
            userWalletService()->walletChangePoint(
                $user->wallet,
                WalletChangeTypeEnum::MembershipBirthday,
                $bonusPoints,
                "生日奖励积分",
                $pointRecord
            );

            DB::commit();
            
            Log::info('Birthday bonus processed', [
                'user_id' => $user->id,
                'bonus_points' => $bonusPoints,
                'membership_level' => $userLevel->membershipLevel->name,
            ]);

            return $pointRecord;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to process birthday bonus', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * 获取用户会员信息
     */
    public function getUserMembershipInfo(User $user): array
    {
        $userLevel = UserMembershipLevel::getCurrentLevel($user->id);
        $totalSpend = $this->calculateUserTotalSpend($user);

        if (!$userLevel) {
            // 用户还没有会员等级，获取最低等级作为默认等级
            $defaultLevel = MembershipLevel::getLowestLevel();
            return [
                'current_level' => null,
                'default_level' => $defaultLevel,
                'total_spend_amount' => $totalSpend,
                'next_level' => $defaultLevel ? $defaultLevel->getNextLevel() : null,
                'upgrade_required_amount' => $defaultLevel ? $defaultLevel->getUpgradeRequiredAmount($totalSpend) : null,
                'point_stats' => MembershipPointRecord::getUserPointStats($user->id),
            ];
        }

        $currentLevel = $userLevel->membershipLevel;
        $nextLevel = $currentLevel->getNextLevel();

        return [
            'current_level' => $userLevel,
            'membership_level' => $currentLevel,
            'total_spend_amount' => $totalSpend,
            'next_level' => $nextLevel,
            'upgrade_required_amount' => $currentLevel->getUpgradeRequiredAmount($totalSpend),
            'point_stats' => MembershipPointRecord::getUserPointStats($user->id),
            'benefits' => $currentLevel->getBenefitsDescription(),
        ];
    }

    /**
     * 获取所有会员等级列表
     */
    public function getAllMembershipLevels(): \Illuminate\Database\Eloquent\Collection
    {
        return MembershipLevel::getAllLevels();
    }

    /**
     * 处理订单退款时的积分扣除
     */
    public function processOrderRefund(Order $order): void
    {
        // 查找该订单的积分记录
        $pointRecord = MembershipPointRecord::where('order_id', $order->id)->first();
        
        if (!$pointRecord) {
            return;
        }

        $user = $order->user;
        if (!$user) {
            return;
        }

        DB::beginTransaction();
        try {
            // 扣除积分
            userWalletService()->walletChangePoint(
                $user->wallet,
                WalletChangeTypeEnum::MembershipRefund,
                -$pointRecord->earned_points,
                "订单退款扣除积分",
                $order
            );

            // 标记积分记录为已退款
            $pointRecord->update([
                'description' => $pointRecord->description . ' (已退款)',
            ]);

            DB::commit();
            
            Log::info('Order refund points processed', [
                'user_id' => $user->id,
                'order_id' => $order->id,
                'deducted_points' => $pointRecord->earned_points,
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to process order refund points', [
                'user_id' => $user->id,
                'order_id' => $order->id,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }
}
