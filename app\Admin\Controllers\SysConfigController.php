<?php

namespace App\Admin\Controllers;

use App\Constants\Permissions;
use App\Exceptions\DataException;
use App\Http\Controllers\Controller;
use App\Models\Enums\SysConfigKeyEnum;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Models\Attachment;

class SysConfigController extends Controller
{
    //
    public function __construct()
    {
        $this->hasPermissionOr(Permissions::SysConfigsUpdate)->only(['update']);
        $this->hasPermissionOr(Permissions::SysConfigsUpdate, Permissions::SysConfigsIndex)->only(['show']);
    }

    /**
     *
     * @param $key
     * @return JsonResponse
     * @throws DataException
     */
    public function show($key): JsonResponse
    {
        $key = SysConfigKeyEnum::tryFrom($key);
        if (empty($key)) {
            throw new DataException("参数错误");
        }
        if($key == SysConfigKeyEnum::ModalConfig){
            $value = sysConfigService()->get($key);
            // 获取图片id,path,disk,module
            $value['modal1']['image'] = Attachment::where('id', $value['modal1']['image_id'])->select('id', 'path', 'disk', 'module')->first();
            $value['modal2']['image'] = Attachment::where('id', $value['modal2']['image_id'])->select('id', 'path', 'disk', 'module')->first();
            $value['modal2']['image_app'] = Attachment::where('id', $value['modal2']['image_app_id'])->select('id', 'path', 'disk', 'module')->first();
            $value['modal2']['logo_image_first'] = Attachment::where('id', $value['modal2']['logo_image_first_id'])->select('id', 'path', 'disk', 'module')->first();
            $value['modal2']['logo_image_second'] = Attachment::where('id', $value['modal2']['logo_image_second_id'])->select('id', 'path', 'disk', 'module')->first();
        }else{
            $value = sysConfigService()->get($key);
        }
        return response()->json($value);
    }

    /**
     * 更新配置
     * @param Request $request
     * @param $key
     * @return JsonResponse
     * @throws DataException
     */
    public function update(Request $request, $key): JsonResponse
    {
        $key = SysConfigKeyEnum::tryFrom($key);
        if (empty($key)) {
            throw new DataException("参数错误");
        }
    
        $validated = $request->validate($key->rule());
        sysConfigService()->set($key, $validated);

        return response()->json();
    }
}
