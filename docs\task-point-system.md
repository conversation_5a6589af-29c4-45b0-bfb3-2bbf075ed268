# 任务积分系统使用说明

## 概述

任务积分系统允许用户通过完成各种任务来获得积分奖励，支持多种任务类型和渠道，具有完善的限制机制和管理功能。

## 功能特点

- ✅ 支持多种任务类型（注册、签到、关注、分享、发帖等）
- ✅ 支持多渠道区分（微信、微博、Instagram、YouTube、Facebook、Twitter等）
- ✅ 灵活的限制机制（每日限制、总限制、无限制）
- ✅ 完整的积分记录和统计
- ✅ 后台管理界面
- ✅ 防刷机制

## 数据库迁移

```bash
# 运行迁移创建表结构
php artisan migrate

# 运行种子文件创建默认配置（可选）
php artisan db:seed --class=TaskPointConfigSeeder
```

## API 接口

### 用户端接口

#### 1. 获取可执行任务列表
```http
GET /api/tasks/available
Authorization: Bearer {token}
```

响应示例：
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "task_type": "check_in",
            "task_name": "签到",
            "channel": null,
            "points_per_action": 5,
            "daily_limit": 1,
            "total_limit": 0,
            "today_count": 0,
            "total_count": 0,
            "description": "每日签到奖励"
        }
    ]
}
```

#### 2. 执行任务
```http
POST /api/tasks/execute
Authorization: Bearer {token}
Content-Type: application/json

{
    "task_type": "check_in",
    "channel": null,
    "task_data": {},
    "external_id": null
}
```

#### 3. 签到（快捷接口）
```http
POST /api/tasks/check-in
Authorization: Bearer {token}
```

#### 4. 获取任务统计
```http
GET /api/tasks/stats
Authorization: Bearer {token}
```

#### 5. 获取任务记录
```http
POST /api/tasks/records/_search
Authorization: Bearer {token}
Content-Type: application/json

{
    "task_type": "check_in",
    "channel": null,
    "page": 1,
    "per_page": 20
}
```

### 管理端接口

#### 1. 任务配置管理
```http
# 获取配置列表
GET /admin/task-point-configs

# 创建配置
POST /admin/task-point-configs

# 更新配置
PUT /admin/task-point-configs/{id}

# 删除配置
DELETE /admin/task-point-configs/{id}
```

#### 2. 批量操作
```http
# 批量启用/禁用
POST /admin/task-point-configs/batch-toggle-status

# 更新排序
POST /admin/task-point-configs/update-sort
```

## 使用示例

### 1. 用户签到
```php
use App\Services\TaskPointService;

$taskPointService = app(TaskPointService::class);
$taskRecord = $taskPointService->executeTask($userId, 'check_in');

if ($taskRecord) {
    echo "签到成功，获得 {$taskRecord->earned_points} 积分";
} else {
    echo "签到失败，可能今日已签到";
}
```

### 2. 社交媒体关注
```php
// 关注微信公众号
$taskRecord = $taskPointService->executeTask(
    $userId, 
    'wechat_follow', 
    'wechat'
);

// Facebook分享
$taskRecord = $taskPointService->executeTask(
    $userId, 
    'fb_share', 
    'facebook', 
    ['post_url' => 'https://example.com/post/123'],
    'post_123'
);
```

### 3. 获取用户可执行任务
```php
$availableTasks = $taskPointService->getUserAvailableTasks($userId);
foreach ($availableTasks as $task) {
    echo "{$task['task_name']}: {$task['points_per_action']} 积分\n";
}
```

## 任务类型说明

| 任务类型 | 说明 | 默认积分 | 限制 |
|---------|------|---------|------|
| registration | 用户注册 | 100 | 总限制1次 |
| birthday | 生日奖励 | 200 | 每日1次 |
| check_in | 签到 | 5 | 每日1次 |
| wechat_follow | 关注微信 | 50 | 总限制1次 |
| weibo_follow | 关注微博 | 30 | 总限制1次 |
| ins_follow | 关注INS | 30 | 总限制1次 |
| ytb_follow | 关注YTB | 40 | 总限制1次 |
| fb_share | FB分享 | 10 | 每日3次 |
| twitter_share | Twitter分享 | 10 | 每日3次 |
| post_review | 发帖审核通过 | 20 | 每日5次 |
| video_review | 视频评论审核通过 | 30 | 每日3次 |
| questionnaire | 问卷调查 | 50 | 每日2次 |
| fb_group_join | 加入FB群组 | 25 | 总限制3次 |

## 渠道说明

- `wechat` - 微信
- `weibo` - 微博
- `instagram` - Instagram
- `youtube` - YouTube
- `facebook` - Facebook
- `twitter` - Twitter
- `website` - 官网

## 限制机制

1. **每日限制** (`daily_limit`)：用户每天最多可执行的次数
2. **总限制** (`total_limit`)：用户总共最多可执行的次数
3. **无限制**：设置为 0 表示无限制

## 积分流水

所有任务执行都会自动：
1. 创建任务执行记录 (`user_task_records`)
2. 创建积分记录 (`membership_point_records`)
3. 更新用户钱包积分余额

## 注意事项

1. 任务执行前会自动检查用户是否达到限制
2. 所有积分操作都在数据库事务中执行
3. 失败的任务执行会记录日志
4. 管理员可以通过后台动态配置所有任务参数
5. 系统支持扩展新的任务类型和渠道
