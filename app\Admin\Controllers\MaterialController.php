<?php

namespace App\Admin\Controllers;

use App\Constants\Permissions;
use App\Http\Controllers\Controller;
use App\Models\Material;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rules\Exists;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

class MaterialController extends Controller
{
    //
    public function __construct()
    {
        $this->hasPermissionOr(Permissions::MaterialsUpdate)->only(['store', 'update', 'destroy',]);
        $this->hasPermissionOr(Permissions::MaterialsUpdate, Permissions::MaterialsIndex)->only(['index', 'show']);
    }

    public function options(Request $request): AnonymousResourceCollection
    {
        $res = QueryBuilder::for(Material::class, $request)
            ->allowedFilters([
                AllowedFilter::partial('name'),
            ])
            ->select([DB::raw('name as label'), DB::raw('id as value')])
            ->get();
        return JsonResource::collection($res);
    }

    /**
     *
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $builder = QueryBuilder::for(Material::class, $request)
            ->allowedFilters([
                AllowedFilter::partial('name'),
            ])
            ->allowedSorts(['id'])
            ->defaultSort('-id');
        $res = $builder->paginate($this->getPerPage());
        return JsonResource::collection($res);
    }

    public function show(Material $material): JsonResource
    {
        return JsonResource::make($material);
    }

    /**
     * 修改
     * @param Request $request
     * @param Material $material
     * @return JsonResource
     */
    public function update(Request $request, Material $material): JsonResource
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:64'],
            'spu_key' => ['required', 'string', 'max:12'],
        ]);
        $material->update($validated);

        return JsonResource::make($material);
    }


    /**
     * 创建
     * @param Request $request
     * @param Material $material
     * @return JsonResource
     */
    public function store(Request $request, Material $material): JsonResource
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:64'],
            'spu_key' => ['required', 'string', 'max:12'],
        ]);
        $material->update($validated);

        return JsonResource::make($material);
    }


    /**
     * 删除
     * @param Request $request
     * @return JsonResponse
     */
    public function destroy(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ids' => ['array', 'required'],
            'ids.*' => [(new Exists(Material::class, 'id'))],
        ]);
        $ids = Arr::get($validated, 'ids');

        // 删除
        Material::query()->whereIn('id', $ids)
            ->update([
                'deleted_at' => now()
            ]);
        return response()->json();
    }

}
