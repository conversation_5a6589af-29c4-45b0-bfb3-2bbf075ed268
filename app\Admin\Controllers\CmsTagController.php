<?php

namespace App\Admin\Controllers;

use App\Http\Controllers\Controller;
use App\Models\CmsTag;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\JsonResponse;
use App\Constants\Permissions;
use Illuminate\Support\Arr;
use Illuminate\Validation\Rules\Exists;
use App\Exceptions\DataException;
use Spatie\QueryBuilder\QueryBuilder;
use Spatie\QueryBuilder\AllowedFilter;

class CmsTagController extends Controller
{
    public function __construct()
    {
        // 编辑权限
        $this->hasPermissionOr(Permissions::CmsSettingsUpdate)->except(['index', 'store', 'update', 'destroy']);
        $this->hasPermissionOr(Permissions::CmsSettingsIndex, Permissions::CmsSettingsUpdate)->only(['index', 'show']);
    }
    // 列表
    public function index(Request $request): JsonResource
    {
        $query = QueryBuilder::for(CmsTag::class)
            ->with('adminUser')
            ->allowedFilters([
                AllowedFilter::exact('status'),
                AllowedFilter::partial('name'),
            ])
            ->allowedSorts(['created_at', 'name', 'status'])
            ->defaultSort('-created_at');

        if ($request->boolean('all', false)) {
            return JsonResource::collection($query->get());
        }

        return JsonResource::collection($query->paginate($this->getPerPage()));
    }

    // 新增
    public function store(Request $request): JsonResource
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:50'],
            'status' => ['required', 'boolean'],
        ]);
        $validated['admin_user_id'] = Auth::user()->id;
        $tag = CmsTag::create($validated);
        return JsonResource::make($tag);
    }

    // 修改
    public function update(Request $request, CmsTag $cmsTag): JsonResource
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:50'],
            'status' => ['required', 'boolean'],
        ]);
        $cmsTag->update($validated);
        return JsonResource::make($cmsTag);
    }

    // 删除
    public function destroy(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ids' => ['array', 'required'],
            'ids.*' => [(new Exists(CmsTag::class, 'id'))],
        ]);
        $ids = Arr::get($validated, 'ids');
        try {
            CmsTag::query()->whereIn('id', $ids)->update([
                'deleted_at' => now()
            ]);

        } catch (\Throwable $throwable) {
            throw new DataException($throwable->getMessage());
        }
        return response()->json();
    }
} 