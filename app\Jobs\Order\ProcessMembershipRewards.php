<?php

namespace App\Jobs\Order;

use App\Constants\QueueKey;
use App\Models\Order\Order;
use App\Services\MembershipService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessMembershipRewards implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 处理会员等级升级和积分奖励
     */
    public function __construct(public Order $order)
    {
        $this->onQueue(QueueKey::Order->value);
    }

    /**
     * Execute the job.
     */
    public function handle(MembershipService $membershipService): void
    {
        try {
            // 只处理已支付的订单
            if ($this->order->paid_status !== \App\Models\Enums\Order\OrderPaidStatusEnum::Paid) {
                return;
            }

            // 只处理有用户的订单
            if (!$this->order->user_id) {
                return;
            }

            // 处理订单积分奖励（包含会员等级检查和升级）
            $pointRecord = $membershipService->processOrderPoints($this->order);

            if ($pointRecord) {
                Log::info('Membership rewards processed successfully', [
                    'order_id' => $this->order->id,
                    'user_id' => $this->order->user_id,
                    'earned_points' => $pointRecord->earned_points,
                    'membership_level_id' => $pointRecord->membership_level_id,
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Failed to process membership rewards', [
                'order_id' => $this->order->id,
                'user_id' => $this->order->user_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // 重新抛出异常以便队列系统处理重试
            throw $e;
        }
    }

    /**
     * 处理失败的任务
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('ProcessMembershipRewards job failed permanently', [
            'order_id' => $this->order->id,
            'user_id' => $this->order->user_id,
            'error' => $exception->getMessage(),
        ]);
    }
}
