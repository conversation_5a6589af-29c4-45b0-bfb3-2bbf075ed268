<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Services\MembershipService;
use App\Models\Membership\MembershipLevel;
use App\Models\Membership\UserMembershipLevel;
use App\Models\Membership\MembershipPointRecord;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Auth;
use Spatie\QueryBuilder\QueryBuilder;

class MembershipController extends Controller
{
    protected MembershipService $membershipService;

    public function __construct(MembershipService $membershipService)
    {
        $this->membershipService = $membershipService;
    }

    /**
     * 获取用户会员信息
     */
    public function me(): JsonResource
    {
        $user = Auth::user();
        $membershipInfo = $this->membershipService->getUserMembershipInfo($user);

        return JsonResource::make($membershipInfo);
    }

    /**
     * 获取所有会员等级列表
     */
    public function levels(): AnonymousResourceCollection
    {
        $levels = $this->membershipService->getAllMembershipLevels();

        return JsonResource::collection($levels);
    }

    /**
     * 获取用户积分记录
     */
    public function pointRecords(Request $request): AnonymousResourceCollection
    {
        $user = Auth::user();
        
        $records = QueryBuilder::for(MembershipPointRecord::class, $request)
            ->where('user_id', $user->id)
            ->with(['membershipLevel', 'order'])
            ->allowedSorts(['created_at', 'earned_points'])
            ->allowedFilters(['source_type', 'membership_level_id'])
            ->defaultSort('-created_at')
            ->paginate($request->get('per_page', 15));

        return JsonResource::collection($records);
    }

    /**
     * 获取积分统计信息
     */
    public function pointStats(): JsonResource
    {
        $user = Auth::user();
        $stats = MembershipPointRecord::getUserPointStats($user->id);
        $sourceDistribution = MembershipPointRecord::getPointSourceDistribution($user->id);

        return JsonResource::make([
            'stats' => $stats,
            'source_distribution' => $sourceDistribution,
        ]);
    }

    /**
     * 手动检查并升级用户等级（用于测试或管理员操作）
     */
    public function checkUpgrade(): JsonResource
    {
        $user = Auth::user();
        $userLevel = $this->membershipService->checkAndUpgradeUserLevel($user);

        return JsonResource::make([
            'success' => true,
            'user_level' => $userLevel,
            'message' => $userLevel ? '等级检查完成' : '暂无可升级等级',
        ]);
    }

    /**
     * 获取会员等级权益详情
     */
    public function levelBenefits(MembershipLevel $level): JsonResource
    {
        return JsonResource::make([
            'level' => $level,
            'benefits' => $level->getBenefitsDescription(),
            'next_level' => $level->getNextLevel(),
        ]);
    }

    /**
     * 获取用户会员等级历史
     */
    public function levelHistory(Request $request): AnonymousResourceCollection
    {
        $user = Auth::user();
        
        $history = QueryBuilder::for(UserMembershipLevel::class, $request)
            ->where('user_id', $user->id)
            ->with('membershipLevel')
            ->allowedSorts(['achieved_at', 'total_spend_amount'])
            ->defaultSort('-achieved_at')
            ->paginate($request->get('per_page', 15));

        return JsonResource::collection($history);
    }

    /**
     * 获取会员等级升级进度
     */
    public function upgradeProgress(): JsonResource
    {
        $user = Auth::user();
        $membershipInfo = $this->membershipService->getUserMembershipInfo($user);
        
        $currentLevel = $membershipInfo['current_level'];
        $nextLevel = $membershipInfo['next_level'];
        $totalSpend = $membershipInfo['total_spend_amount'];
        $upgradeRequired = $membershipInfo['upgrade_required_amount'];

        $progress = [
            'current_level' => $currentLevel ? $currentLevel->membershipLevel : $membershipInfo['default_level'],
            'next_level' => $nextLevel,
            'total_spend_amount' => $totalSpend,
            'upgrade_required_amount' => $upgradeRequired,
            'progress_percentage' => 0,
        ];

        if ($nextLevel && $currentLevel) {
            $currentLevelMin = $currentLevel->membershipLevel->min_spend_amount;
            $nextLevelMin = $nextLevel->min_spend_amount;
            $levelRange = $nextLevelMin - $currentLevelMin;
            $currentProgress = $totalSpend - $currentLevelMin;
            $progress['progress_percentage'] = $levelRange > 0 ? min(100, ($currentProgress / $levelRange) * 100) : 100;
        }

        return JsonResource::make($progress);
    }

    /**
     * 获取本月积分获得情况
     */
    public function monthlyPoints(): JsonResource
    {
        $user = Auth::user();
        
        $monthlyRecords = MembershipPointRecord::byUser($user->id)
            ->thisMonth()
            ->with('membershipLevel')
            ->orderBy('created_at', 'desc')
            ->get();

        $totalPoints = $monthlyRecords->sum('earned_points');
        $recordsBySource = $monthlyRecords->groupBy('source_type');

        return JsonResource::make([
            'total_points' => $totalPoints,
            'records_count' => $monthlyRecords->count(),
            'records_by_source' => $recordsBySource->map(function ($records, $source) {
                return [
                    'source' => $source,
                    'count' => $records->count(),
                    'total_points' => $records->sum('earned_points'),
                    'records' => $records->take(5), // 只返回最近5条
                ];
            }),
        ]);
    }

    /**
     * 获取年度积分获得情况
     */
    public function yearlyPoints(): JsonResource
    {
        $user = Auth::user();
        
        $yearlyRecords = MembershipPointRecord::byUser($user->id)
            ->thisYear()
            ->selectRaw('MONTH(created_at) as month, SUM(earned_points) as total_points, COUNT(*) as count')
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        $totalYearPoints = MembershipPointRecord::byUser($user->id)
            ->thisYear()
            ->sum('earned_points');

        return JsonResource::make([
            'total_year_points' => $totalYearPoints,
            'monthly_breakdown' => $yearlyRecords,
        ]);
    }

    /**
     * 获取会员等级对比
     */
    public function levelComparison(): JsonResource
    {
        $levels = $this->membershipService->getAllMembershipLevels();
        
        $comparison = $levels->map(function ($level) {
            return [
                'id' => $level->id,
                'name' => $level->name,
                'slug' => $level->slug,
                'min_spend_amount' => $level->min_spend_amount,
                'max_spend_amount' => $level->max_spend_amount,
                'point_rate' => $level->point_rate,
                'point_rate_percentage' => ($level->point_rate * 100) . '%',
                'color' => $level->color,
                'benefits' => $level->getBenefitsDescription(),
                'sort_order' => $level->sort_order,
            ];
        });

        return JsonResource::make([
            'levels' => $comparison,
            'comparison_fields' => [
                'point_rate' => '积分比例',
                'benefits' => '会员权益',
                'min_spend_amount' => '升级门槛',
            ],
        ]);
    }
}
