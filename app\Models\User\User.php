<?php

namespace App\Models\User;

use App\Constants\QueueKey;
use App\Jobs\CouponGrantCheck;
use App\Jobs\EmailRuleNotice;
use App\Jobs\SyncInviteUser;
use App\Jobs\UserSharingRule;
use App\Models\Cart;
use App\Models\Enums\CouponGrantType;
use App\Models\Enums\EmailRuleEventEnum;
use App\Models\Enums\User\InviteUserTypeEnum;
use App\Models\Enums\User\UserEmailPreferencesEnum;
use App\Models\Enums\User\UserRegisterTypesEnum;
use App\Models\Enums\User\UserStatusEnum;
use App\Models\GoogleUser;
use App\Models\Group;
use App\Models\InviteAmountWithdraw;
use App\Models\Order\Order;
use App\Models\User\UserInviteReward;
use App\Models\Product\Product;
use App\Models\SharingRule;
use App\Models\User\UserGroup;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Log\Logger;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasPermissions;
use Spatie\Permission\Traits\HasRoles;


/**
 * @property int $id
 * @property UserStatusEnum $status
 * @property UserRegisterTypesEnum $register_type
 * @property Carbon $birth_date
 * @property string $password
 * @property string $email
 * @property UserWallet $wallet
 */
class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;
    use HasFactory;
    use HasRoles, HasPermissions;

    protected $guarded = [];
    protected $hidden = ['password', 'deleted_at'];

    protected $casts = [
        'birth_date' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'status' => UserStatusEnum::class,
        'register_type' => UserRegisterTypesEnum::class,
        'email_preferences' => UserEmailPreferencesEnum::class,
    ];

    // protected function serializeDate(DateTimeInterface $date)
    protected function serializeDate(\DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    public static function booted(): void
    {
        parent::booted();
        static::created(function (User $user) {
            $user->wallet()->firstOrCreate();
            UserSharingRule::dispatch($user)->onQueue(QueueKey::Default->value);
        });
        static::saved(function (User $user) {
            // 注册-优惠券奖励回调
            // CouponGrantCheck::dispatch(CouponGrantType::RegisterUser, $user)->onQueue(QueueKey::Coupon->value);
            if ($user->isDirty(['email_preferences']) && $user->email_preferences == UserEmailPreferencesEnum::None) {
                // 邮件任务-取消订阅
                EmailRuleNotice::dispatch(EmailRuleEventEnum::SubscriptionCancel, user: $user)->onQueue(QueueKey::Default->value);
            }

            if ($user->isDirty(['parent_id'])) {
                CouponGrantCheck::dispatch(CouponGrantType::ShareInvite, $user)->onQueue(QueueKey::Coupon->value);
            }

            // 注册-邀请码奖励规则
            if ($user->isDirty(['parent_id']) && $user->parent_id) {
                // 更新邀请关系表
                SyncInviteUser::dispatch($user)->onQueue(QueueKey::Default->value);
            }
        });
    }

    // 重要， 用于permission
    public function guardName(): string
    {
        return 'api';
    }

    public function password(): Attribute
    {
        return Attribute::set(function ($value) {
            return $value ? bcrypt($value) : null;
        });
    }

    /**
     * 是否为游客
     */
    public function isGuest(): Attribute
    {
        return Attribute::get(function () {
            return in_array($this->register_type, [
                UserRegisterTypesEnum::FastPay,
                UserRegisterTypesEnum::CouponRandomGrant,
                UserRegisterTypesEnum::Subscription,
                UserRegisterTypesEnum::Comment,
            ])
                && !$this->password;
        });
    }

    /**
     * 钱包
     * @return HasOne
     */
    public function wallet(): HasOne
    {
        return $this->hasOne(UserWallet::class);
    }

    /**
     * 地址
     * @return HasMany
     */
    public function addresses(): HasMany
    {
        return $this->hasMany(UserAddress::class);
    }

    /**
     * 找回密码记录
     * @return HasMany
     */
    public function passwordRetrieves(): HasMany
    {
        return $this->hasMany(UserPasswordRetrieve::class);
    }


    /**
     * 用户收藏的商品
     * @return BelongsToMany
     */
    public function collectProducts(): BelongsToMany
    {
        return $this->belongsToMany(
            Product::class,
            UserProductCollect::class,
        )->withPivot('collect_at');
    }

    public function cart(): HasOne
    {
        return $this->hasOne(Cart::class)->where('is_checkout', false);
    }

    public function carts(): HasMany
    {
        return $this->hasMany(Cart::class);
    }

    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    public function googleUser(): BelongsTo
    {
        return $this->belongsTo(GoogleUser::class, 'email', 'email');
    }

    public function userCoupons(): HasMany
    {
        return $this->hasMany(UserCoupon::class);
    }

    /**
     * 获取专属父级用户（直接关联的父）
     * @return BelongsTo
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(User::class, 'parent_id', 'id');
    }

    /**
     * 获取所有父级用户的查询构建器
     * @return Builder
     */
    public function allParents(): Builder
    {
        $allParentIds = $this->getAllParentIds();
        // 返回查询构建器
        return User::query()->whereIn('id', $allParentIds);
    }
    /**
     * 递归获取所有父级用户的 ID
     * @return array
     */
    protected function getAllParentIds(): array
    {
        $allIds = [];
        $currentUser = $this;
        while ($currentUser->parent) {
            $allIds[] = $currentUser->parent->id; // 添加当前父用户 ID
            $currentUser = $currentUser->parent; // 移动到父用户
        }
        return array_unique($allIds); // 返回唯一的 ID
    }

    // 获取专属子集（直接关联的子）
    public function children(): HasMany
    {
        return $this->hasMany(User::class, 'parent_id', 'id');
    }

    // 用户提现记录
    public function inviteAmountWithdraws(): HasMany
    {
        return $this->hasMany(InviteAmountWithdraw::class);
    }

    // 用户获得的奖励
    public function inviteRewards()
    {
        return $this->hasMany(UserInviteReward::class, 'reward_user_id');
    }

    // 用户创造的奖励
    public function orderRewards()
    {
        return $this->hasMany(UserInviteReward::class, 'order_user_id');
    }

    // 用户分享规则
    public function sharingRule(): BelongsTo
    {
        return $this->belongsTo(SharingRule::class);
    }

    // 被邀请人分享规则
    public function invitersRule(): BelongsTo
    {
        return $this->belongsTo(SharingRule::class, 'inviters_rule_id');
    }
    
    // 专属邀请的用户
    public function exclusiveInvitedUsers(): BelongsToMany
    {
        return $this->belongsToMany(
            User::class,
            InviteUser::class,
            'user_id',
            'invited_user_id',
            'id',
            'id'
        )->withPivot('type')
            ->wherePivot('type', InviteUserTypeEnum::ExclusiveFans->value);
    }

    // 普通邀请用户
    public function ordinaryInvitedUsers(): BelongsToMany
    {
        return $this->belongsToMany(
            User::class,
            InviteUser::class,
            'user_id',
            'invited_user_id',
            'id',
            'id'
        )->withPivot('type')
            ->wherePivot('type', InviteUserTypeEnum::OrdinaryFans->value);
    }

    // 所有邀请用户
    public function allInvitedUsers(): BelongsToMany
    {
        return $this->belongsToMany(
            User::class,
            InviteUser::class,
            'user_id',
            'invited_user_id',
            'id',
            'id'
        )->withPivot('type');
    }

    // 用户组
    public function groups(): BelongsToMany
    {
        return $this->belongsToMany(
            Group::class,
            UserGroup::class,
            'user_id',
            'group_id',
            'id',
            'id'
        );
    }
}
