<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('membership_levels', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100)->comment('会员等级名称');
            $table->string('slug', 50)->nullable()->comment('会员等级标识');
            $table->text('description')->nullable()->comment('会员等级描述');
            $table->decimal('min_spend_amount', 12, 2)->default(0)->comment('最低消费金额');
            $table->decimal('max_spend_amount', 12, 2)->nullable()->comment('最高消费金额(null表示无上限)');
            $table->decimal('point_rate', 5, 4)->default(0.01)->comment('积分兑换比例(消费1元获得多少积分)');
            $table->json('benefits')->nullable()->comment('会员权益(JSON格式)');
            $table->string('color', 7)->default('#000000')->comment('等级颜色');
            $table->string('icon')->nullable()->comment('等级图标');
            $table->integer('sort_order')->default(0)->comment('排序');
            $table->boolean('is_active')->default(true)->comment('是否启用');
            $table->timestamps();
            
            $table->index(['is_active', 'sort_order']);
            $table->index('min_spend_amount');
            $table->engine('InnoDB');
            $table->comment('会员等级表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('membership_levels');
    }
};
