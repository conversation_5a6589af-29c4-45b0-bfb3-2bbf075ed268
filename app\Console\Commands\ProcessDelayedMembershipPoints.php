<?php

namespace App\Console\Commands;

use App\Jobs\Order\ProcessDelayedMembershipPoints as ProcessDelayedMembershipPointsJob;
use App\Models\Membership\MembershipPointRecord;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ProcessDelayedMembershipPoints extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'membership:process-delayed-points 
                            {--limit=100 : Maximum number of records to process}
                            {--dry-run : Show what would be processed without actually processing}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '发放会员积分';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $limit = (int) $this->option('limit');
        $dryRun = $this->option('dry-run');

        $this->info("Processing delayed membership points...");
        if ($dryRun) {
            $this->warn("DRY RUN MODE - No actual processing will occur");
        }

        // 查找需要发放的积分记录
        $pendingRecords = MembershipPointRecord::readyForDistribution()
            ->limit($limit)
            ->get();

        if ($pendingRecords->isEmpty()) {
            $this->info("No pending membership points found for distribution.");
            return self::SUCCESS;
        }

        $this->info("Found {$pendingRecords->count()} pending membership point records.");

        $processed = 0;
        $failed = 0;

        foreach ($pendingRecords as $record) {
            try {
                if ($dryRun) {
                    $this->line("Would process: Record ID {$record->id}, User ID {$record->user_id}, Points: {$record->earned_points}");
                } else {
                    // 分发Job来处理积分发放
                    ProcessDelayedMembershipPointsJob::dispatch($record);
                    $this->line("Dispatched job for record ID {$record->id}");
                }
                $processed++;
            } catch (\Exception $e) {
                $this->error("Failed to process record ID {$record->id}: {$e->getMessage()}");
                Log::error('Failed to dispatch delayed membership points job', [
                    'record_id' => $record->id,
                    'error' => $e->getMessage(),
                ]);
                $failed++;
            }
        }

        if ($dryRun) {
            $this->info("DRY RUN COMPLETE: Would have processed {$processed} records.");
        } else {
            $this->info("Successfully dispatched jobs for {$processed} records.");
        }

        if ($failed > 0) {
            $this->error("Failed to process {$failed} records.");
            return self::FAILURE;
        }

        return self::SUCCESS;
    }
}
