<?php

namespace App\Http\Controllers;


use App\Constants\ErrorCode;
use App\Exceptions\DataException;
use App\Models\Collection;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;
use App\Constants\CacheKey;
use Illuminate\Support\Facades\Cache;

class CollectionController extends Controller
{
    //

    public function treeIndex(Request $request): JsonResource
    {
        $list = Collection::query()
            ->with([
                'image:id,path,disk,module',
                'imageBanner:id,path,disk,module',
                'tabCollections'
            ])
            ->orderBy('sort', 'desc')
            ->where('active', true)
            ->get();
        $treeList = buildTree($list->toArray(), needTreeIds: true);
        // $this->setImageForTree($treeList, null, true);  // 根节点设置为 true
        return JsonResource::make($treeList);
    }

    private function setImageForTree(&$treeList, $parentImage = null, $isRoot = false)
    {
        foreach ($treeList as &$node) {
            // 如果是第一级节点，并且没有图片，则不继承任何图片
            if ($isRoot && empty($node['image_banner'])) {
                $node['image_banner'] = null;
            }
            // 如果当前节点没有图片并且父级有图片，才继承父级的图片
            elseif (empty($node['image_banner']) && !empty($parentImage)) {
                $node['image_banner'] = $parentImage;
            }

            // 如果当前节点有图片，更新父级图片为当前节点的图片，并停止传递
            if (!empty($node['image_banner'])) {
                $parentImage = $node['image_banner'];
            }

            // 递归处理子节点，父级图片传递给子节点
            if (isset($node['children']) && is_array($node['children'])) {
                $this->setImageForTree($node['children'], $parentImage, false);
            }
        }
    }

    /**
     *
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $uniqueKey = md5(json_encode($request->all()));
        $content = Cache::remember(CacheKey::CollectionSearchList->getKey($uniqueKey), now()->addWeek(), function () use ($request) {
            $builder = QueryBuilder::for(Collection::class, $request)
                ->with([
                    'image:id,path,disk,module',
                    'imageBanner:id,path,disk,module',
                    'tabCollections.imageBanner'
                ])
                ->allowedFilters([
                    AllowedFilter::partial('is_related'),
                    AllowedFilter::partial('title'),
                    AllowedFilter::exact('slug_title'),  //前端精准
                    AllowedFilter::exact('parent_id'),
                    AllowedFilter::exact('active'),
                ])
                ->allowedSorts(['id'])
                ->defaultSort('-id');

            $res = $builder->paginate($this->getPerPage());
            return JsonResource::collection($res);
        });

        return JsonResource::collection($content);
    }


    public function show($slugTitle): JsonResource
    {
        $collection = Collection::query()
            ->with([
                'image:id,path,disk,module',
                'tabCollections'
            ])
            ->where('slug_title', $slugTitle)
            ->first();

        if (!$collection instanceof Collection) {
            throw new DataException("The requested data does not exist.", ErrorCode::HttpNotFound);
        }

        return JsonResource::make($collection);
    }
}
