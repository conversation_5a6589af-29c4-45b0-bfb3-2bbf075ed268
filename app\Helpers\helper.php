<?php


use App\Models\Currency;
use App\Services\OrderService;
use App\Services\PaymentService;
use App\Services\ProductService;
use App\Services\CartService;
use App\Services\SysConfigService;
use App\Services\UserService;
use App\Services\SharingRuleService;
use App\Services\UserWalletService;
use App\Services\ActivityService;
use Hashids\Hashids;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

if (!function_exists('userService')) {
    function userService(): UserService
    {
        return app()->make(UserService::class);
    }
}


if (!function_exists('userWalletService')) {
    /**
     * @return UserWalletService
     */
    function userWalletService(): UserWalletService
    {
        return app()->make(UserWalletService::class);
    }
}


if (!function_exists('productService')) {
    /**
     * @return ProductService
     */
    function productService(): ProductService
    {
        return app()->make(ProductService::class);
    }
}
if (!function_exists('cartService')) {
    /**
     * @return CartService
     */
    function cartService(): CartService
    {
        return app()->make(CartService::class);
    }
}
if (!function_exists('orderService')) {
    /**
     * @return OrderService
     */
    function orderService(): OrderService
    {
        return app()->make(OrderService::class);
    }
}
if (!function_exists('paymentService')) {
    /**
     * @return PaymentService
     */
    function paymentService(): PaymentService
    {
        return app()->make(PaymentService::class);
    }
}


if (!function_exists('sysConfigService')) {
    /**
     * @return SysConfigService
     */
    function sysConfigService(): SysConfigService
    {
        return app()->make(SysConfigService::class);
    }
}

if (!function_exists('sharingRuleService')) {
    /**
     * @return SharingRuleService
     */
    function sharingRuleService(): SharingRuleService
    {
        return app()->make(SharingRuleService::class);
    }
}

if (!function_exists('currentCurrency')) {
    /**
     * 当前货币信息
     * @return currency
     */
    function currentCurrency(): Currency
    {
        if (!app()->bound('currency')) {
            $currency = Currency::query()->where('currency_code', 'USD')->first();
            if ($currency) {
                // 设置货币符号到应用中，这里假设我们使用一个全局辅助函数或服务容器来存储
                app()->instance('currency', $currency);
            }
        }
        return app()->make('currency');
    }
}

if (!function_exists('hashids')) {
    /**
     * @return Hashids
     */
    function hashids(): Hashids
    {
        return new Hashids(config('hashing.hashids_salt'), 6);
    }
}

if (!function_exists('activityService')) {
    /**
     * @return activityService
     */
    function activityService(): ActivityService
    {
        return app()->make(ActivityService::class);
    }
}

if (!function_exists('buildTree')) {
    function buildTree($list, $id = null, $parentKey = 'parent_id', $key = 'id', $needTreeIds = false): array
    {
        if (!is_array($list)) {
            return [];
        }
        $newList = [];
        foreach ($list as $row) {
            if (Arr::get($row, $parentKey) == $id) {
                $children = buildTree($list, Arr::get($row, $key), $parentKey, $key, $needTreeIds);
                if ($needTreeIds) {
                    $row['children_ids'] = [Arr::get($row, $key), ...array_column($children, $key)];
                }
                $row['children'] = $children;
                $newList[] = $row;
            }
        }
        return $newList;
    }
}

if (!function_exists('getRate')) {
    function getRate($first, $last, $precision = 4, $unit = '%'): string
    {
        $value = 0;
        if (!(float) $first) {
            $value = 100;
        }

        if (!(float) $last) {
            $value = 0;
        }
        if ((float) $first && (float) $last) {
            if ($first > $last) {
                $value = 100;
            } else {
                $value = round(($last - $first) / $last, $precision) * 100;
            }
        }
        return "{$value}{$unit}";
    }
}

if (!function_exists('getRateNumber')) {
    function getRateNumber($first, $last, $precision = 4)
    {
        $value = 0;
        if (!(float) $first) {
            $value = 100;
        }

        if (!(float) $last) {
            $value = 0;
        }
        if ((float) $first && (float) $last) {
            if ($first > $last) {
                $value = 100;
            } else {
                $value = round(($last - $first) / $last, $precision) * 100;
            }
        }

        return $value;
    }
}


if (!function_exists('redis')) {

    /**
     * @return \Redis
     */
    function redis(): \Redis
    {
        return \Illuminate\Support\Facades\Redis::client();
    }
}

if (!function_exists('cachePartialDelete')) {

    /**
     * 根据缓存key批量删除
     * @param string $pattern
     * @return bool|int|Redis
     */
    function cachePartialDelete(string $pattern): bool|int|Redis
    {
        $redis = redis();
        // 选择缓存驱动的数据库
        $redis->select(config('database.redis.cache.database'));
        // 获取列表
        $cacheList = $redis->keys(Cache::getPrefix() . $pattern);
        // 获取redis前缀
        $redisPrefix = config('database.redis.options.prefix');
        $cacheList = array_map(fn($key) => Str::after($key, $redisPrefix), $cacheList);
        return $redis->del($cacheList);
    }
}

if (!function_exists('splitEnZh')) {
    /**
     * 拆分字符串英文和中文
     * @param mixed $string
     * @return string[]
     */
    function splitEnZh($string)
    {
        // 正则表达式匹配英文和中文部分
        if (preg_match('/^([^\x{4e00}-\x{9fa5}]+)\s*([\x{4e00}-\x{9fa5}]+)/u', $string, $enFirstMatch)) {
            // 英文在前
            $enPart = trim($enFirstMatch[1]);
            $zhPart = trim($enFirstMatch[2]);
            return [$enPart, $zhPart];
        } elseif (preg_match('/^([\x{4e00}-\x{9fa5}]+)\s*([^\x{4e00}-\x{9fa5}]+)/u', $string, $zhFirstMatch)) {
            // 中文在前
            $zhPart = trim($zhFirstMatch[1]);
            $enPart = trim($zhFirstMatch[2]);
            return [$enPart, $zhPart];
        } elseif (preg_match('/^([^\x{4e00}-\x{9fa5}]+)/u', $string, $enOnlyMatch)) {
            // 只有英文
            $enPart = trim($enOnlyMatch[1]);
            return [$enPart, ''];
        } elseif (preg_match('/^([\x{4e00}-\x{9fa5}]+)/u', $string, $zhOnlyMatch)) {
            // 只有中文
            $zhPart = trim($zhOnlyMatch[1]);
            return ['', $zhPart];
        }

        // 如果没有匹配到，返回空字符串数组
        return ['', ''];
    }
}

if (!function_exists('un_limit')) {
    /**
     * 去除脚本限制
     */
    function un_limit($limit_second = 0)
    {
        set_time_limit($limit_second);
        ini_set('memory_limit', -1);
        ini_set("max_execution_time", 0);
    }
}

if (!function_exists('convertPrice')) {
    /**
     * 货币转换
     * @param float $price
     * @param App\Models\Currency|null $toCurrency
     * @param App\Models\Currency|null $fromCurrency
     * @return float
     */
    function convertPrice($price, Currency $toCurrency = null, Currency $fromCurrency = null)
    {
        // 转换成浮点数
        $price = (float) $price;

        // 如果货币相同，直接返回价格
        if ($toCurrency == $fromCurrency) {
            return $price;
        }

        // 获取默认货币
        $defaultCurrency = Currency::getDefaultCurrency();

        if ($toCurrency == null) {
            $toCurrency = $defaultCurrency;
        }

        if ($fromCurrency == null) {
            $fromCurrency = $defaultCurrency;
        }

        if ($fromCurrency->id == $defaultCurrency->id) {
            // 转换成目标货币
            $price *= $toCurrency->exchange_rate;
        } else {
            // 还原成数据库的货币
            $price /= $fromCurrency->exchange_rate;
            // 转换成目标货币
            $price *= $toCurrency->exchange_rate;
        }

        return round($price, 2);
    }
}
