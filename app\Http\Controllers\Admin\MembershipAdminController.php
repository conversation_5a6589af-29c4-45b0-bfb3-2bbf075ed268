<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Membership\MembershipLevel;
use App\Models\Membership\UserMembershipLevel;
use App\Models\Membership\MembershipPointRecord;
use App\Models\User\User;
use App\Services\MembershipService;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Validation\Rule;
use Spatie\QueryBuilder\QueryBuilder;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\AllowedSort;

class MembershipAdminController extends Controller
{
    protected MembershipService $membershipService;

    public function __construct(MembershipService $membershipService)
    {
        $this->membershipService = $membershipService;
    }

    /**
     * 获取会员等级列表
     */
    public function levels(Request $request): AnonymousResourceCollection
    {
        $levels = QueryBuilder::for(MembershipLevel::class, $request)
            ->allowedSorts(['sort_order', 'min_spend_amount', 'created_at'])
            ->allowedFilters([
                AllowedFilter::partial('name'),
                AllowedFilter::exact('slug'),
                AllowedFilter::exact('is_active'),
            ])
            ->defaultSort('sort_order')
            ->get();

        return JsonResource::collection($levels);
    }

    /**
     * 创建会员等级
     */
    public function createLevel(Request $request): JsonResource
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:membership_levels',
            'min_spend_amount' => 'required|numeric|min:0',
            'point_rate' => 'required|numeric|min:0',
            'birthday_points_enabled' => 'nullable|boolean',
            'birthday_points_amount' => 'nullable|numeric|min:0',
            'color' => 'nullable|string|max:7',
            'icon' => 'nullable|string|max:255',
            'benefits' => 'nullable|array',
            'description' => 'nullable|string',
            'sort_order' => 'required|integer|min:0',
            'is_active' => 'boolean',
        ]);

        $level = MembershipLevel::create($validated);

        return JsonResource::make($level);
    }

    /**
     * 更新会员等级
     */
    public function updateLevel(Request $request, MembershipLevel $level): JsonResource
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'slug' => [
                'nullable',
                'string',
                'max:255',
                Rule::unique('membership_levels')->ignore($level->id)
            ],
            'min_spend_amount' => 'required|numeric|min:0',
            'point_rate' => 'required|numeric|min:0',
            'birthday_points_enabled' => 'nullable|boolean',
            'birthday_points_amount' => 'nullable|numeric|min:0',
            'color' => 'nullable|string|max:7',
            'icon' => 'nullable|string|max:255',
            'benefits' => 'nullable|array',
            'description' => 'nullable|string',
            'sort_order' => 'required|integer|min:0',
            'is_active' => 'boolean',
        ]);

        $level->update($validated);

        return JsonResource::make($level);
    }

    /**
     * 删除会员等级
     */
    public function deleteLevel(MembershipLevel $level): JsonResource
    {
        // 检查是否有用户使用此等级
        $userCount = UserMembershipLevel::where('membership_level_id', $level->id)->count();

        if ($userCount > 0) {
            return JsonResource::make([
                'success' => false,
                'message' => "无法删除，有 {$userCount} 个用户正在使用此会员等级",
            ]);
        }

        $level->delete();

        return JsonResource::make([
            'success' => true,
            'message' => '会员等级删除成功',
        ]);
    }

    /**
     * 获取用户会员信息列表
     */
    public function userMemberships(Request $request): AnonymousResourceCollection
    {
        $userMemberships = QueryBuilder::for(UserMembershipLevel::class, $request)
            ->with(['user', 'membershipLevel'])
            ->allowedSorts(['total_spend_amount', 'achieved_at', 'created_at'])
            ->allowedFilters([
                'membership_level_id',
                'is_current',
                AllowedFilter::exact('user.email'),
                AllowedFilter::partial('user.name'),
            ])
            ->where('is_current', true)
            ->defaultSort('-total_spend_amount')
            ->paginate($request->get('per_page', 15));

        return JsonResource::collection($userMemberships);
    }

    /**
     * 获取用户详细会员信息
     */
    public function userMembershipDetail(User $user): JsonResource
    {
        $membershipInfo = $this->membershipService->getUserMembershipInfo($user);

        // 获取用户的会员等级历史
        $levelHistory = UserMembershipLevel::where('user_id', $user->id)
            ->with('membershipLevel')
            ->orderBy('achieved_at', 'desc')
            ->get();

        // 获取最近的积分记录
        $recentPointRecords = MembershipPointRecord::where('user_id', $user->id)
            ->with(['membershipLevel', 'order'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        return JsonResource::make([
            'user' => $user,
            'membership_info' => $membershipInfo,
            'level_history' => $levelHistory,
            'recent_point_records' => $recentPointRecords,
        ]);
    }

    /**
     * 手动调整用户会员等级
     */
    public function adjustUserLevel(Request $request, User $user): JsonResource
    {
        $validated = $request->validate([
            'membership_level_id' => 'required|exists:membership_levels,id',
            'reason' => 'required|string|max:255',
        ]);

        $targetLevel = MembershipLevel::find($validated['membership_level_id']);
        $totalSpend = $this->membershipService->calculateUserTotalSpend($user);

        // 创建或更新用户会员等级
        $userLevel = UserMembershipLevel::createOrUpdate(
            $user->id,
            $targetLevel->id,
            $totalSpend
        );

        // 记录管理员操作
        $userLevel->update([
            'admin_adjusted' => true,
            'admin_reason' => $validated['reason'],
        ]);

        return JsonResource::make([
            'success' => true,
            'message' => '用户会员等级调整成功',
            'user_level' => $userLevel,
        ]);
    }

    /**
     * 获取会员等级统计信息
     */
    public function levelStats(): JsonResource
    {
        $stats = [];

        $levels = MembershipLevel::with(['userMembershipLevels' => function ($query) {
            $query->where('is_current', true);
        }])->get();

        foreach ($levels as $level) {
            $userCount = $level->userMembershipLevels->count();
            $totalSpend = UserMembershipLevel::where('membership_level_id', $level->id)
                ->where('is_current', true)
                ->sum('total_spend_amount');

            $stats[] = [
                'level' => $level,
                'user_count' => $userCount,
                'total_spend_amount' => $totalSpend,
                'avg_spend_amount' => $userCount > 0 ? $totalSpend / $userCount : 0,
            ];
        }

        // 总体统计
        $totalUsers = UserMembershipLevel::where('is_current', true)->count();
        $totalSpendAmount = UserMembershipLevel::where('is_current', true)->sum('total_spend_amount');
        $totalPointsAwarded = MembershipPointRecord::sum('earned_points');

        return JsonResource::make([
            'level_stats' => $stats,
            'overall_stats' => [
                'total_users' => $totalUsers,
                'total_spend_amount' => $totalSpendAmount,
                'avg_spend_per_user' => $totalUsers > 0 ? $totalSpendAmount / $totalUsers : 0,
                'total_points_awarded' => $totalPointsAwarded,
            ],
        ]);
    }

    /**
     * 获取积分记录列表
     */
    public function pointRecords(Request $request): AnonymousResourceCollection
    {
        $records = QueryBuilder::for(MembershipPointRecord::class, $request)
            ->with(['user', 'membershipLevel', 'order'])
            ->allowedSorts(['created_at', 'earned_points'])
            ->allowedFilters([
                'source_type',
                'membership_level_id',
                AllowedFilter::exact('user.email'),
                AllowedFilter::partial('user.name'),
            ])
            ->defaultSort('-created_at')
            ->paginate($request->get('per_page', 15));

        return JsonResource::collection($records);
    }

    /**
     * 手动发放积分
     */
    public function grantPoints(Request $request): JsonResource
    {
        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
            'points' => 'required|numeric|min:0.01',
            'description' => 'required|string|max:255',
        ]);

        $user = User::find($validated['user_id']);
        $userLevel = UserMembershipLevel::getCurrentLevel($user->id);

        if (!$userLevel) {
            return JsonResource::make([
                'success' => false,
                'message' => '用户还没有会员等级，无法发放积分',
            ]);
        }

        // 创建积分记录
        $pointRecord = MembershipPointRecord::create([
            'user_id' => $user->id,
            'membership_level_id' => $userLevel->membership_level_id,
            'source_type' => \App\Models\Enums\Membership\MembershipPointSourceEnum::Manual,
            'earned_points' => $validated['points'],
            'description' => $validated['description'],
        ]);

        // 更新用户钱包积分
        userWalletService()->walletChangePoint(
            $user->wallet,
            \App\Models\Enums\User\WalletChangeTypeEnum::MembershipPromotion,
            $validated['points'],
            $validated['description'],
            $pointRecord
        );

        return JsonResource::make([
            'success' => true,
            'message' => '积分发放成功',
            'point_record' => $pointRecord,
        ]);
    }

    /**
     * 批量重新计算用户会员等级
     */
    public function recalculateUserLevels(Request $request): JsonResource
    {
        $validated = $request->validate([
            'user_ids' => 'nullable|array',
            'user_ids.*' => 'exists:users,id',
        ]);

        $userIds = $validated['user_ids'] ?? User::pluck('id')->toArray();
        $processedCount = 0;
        $errors = [];

        foreach ($userIds as $userId) {
            try {
                $user = User::find($userId);
                if ($user) {
                    $this->membershipService->checkAndUpgradeUserLevel($user);
                    $processedCount++;
                }
            } catch (\Exception $e) {
                $errors[] = "用户 ID {$userId}: " . $e->getMessage();
            }
        }

        return JsonResource::make([
            'success' => true,
            'message' => "成功处理 {$processedCount} 个用户的会员等级",
            'processed_count' => $processedCount,
            'total_count' => count($userIds),
            'errors' => $errors,
        ]);
    }
}
