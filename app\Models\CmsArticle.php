<?php

namespace App\Models;

use App\Models\Attachment;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class CmsArticle extends MiddleModel
{
    use HasFactory;
    use SoftDeletes;
    public $timestamps = true;
    protected $guarded = [];

    protected $fillable = [
        'title',
        'content',
        'type',
        'sort',
        'slug_title',
        'status',
        'cms_setting_id',
        'tag_ids',
        'image_id',
        'cate_type',
        'admin_user_id',
        'published_at'
    ];

    protected $casts = [
        'status' => 'boolean',
        'sort' => 'integer',
        'type' => 'integer',
        'cms_setting_id' => 'integer',
        'tag_ids' => 'array',
        'image_id' => 'integer',
        'cate_type' => 'integer',
        'published_at' => 'datetime'
    ];

    protected $appends = [
        'admin_user_name',
        'cms_setting_name',
    ];

    public function image(): BelongsTo
    {
        return $this->belongsTo(Attachment::class, 'image_id');
    }
    //用户信息
    public function adminUser(): BelongsTo
    {
        return $this->belongsTo(AdminUser::class, 'admin_user_id');
    }

    public function getAdminUserNameAttribute()
    {
        return $this->adminUser()->select('id', 'name')->first()?->name??'';
    }

    public function cmsSetting(): BelongsTo
    {
        return $this->belongsTo(CmsSetting::class);
    }

    public function getCmsSettingNameAttribute()
    {
        return $this->cmsSetting()->select('id', 'title')->first()?->title??'';
    }

}
