<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_membership_levels', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_id')->index()->comment('用户ID');
            $table->bigInteger('membership_level_id')->index()->comment('会员等级ID');
            $table->decimal('total_spend_amount', 12, 2)->default(0)->comment('累计消费金额');
            $table->decimal('current_year_spend', 12, 2)->default(0)->comment('当年消费金额');
            $table->timestamp('achieved_at')->nullable()->comment('达到该等级时间');
            $table->timestamp('expires_at')->nullable()->comment('等级过期时间(null表示永久)');
            $table->boolean('is_current')->default(true)->comment('是否当前等级');
            $table->json('upgrade_history')->nullable()->comment('升级历史记录');
            $table->timestamps();
            
            $table->unique(['user_id', 'is_current'], 'unique_current_level');
            $table->index(['user_id', 'membership_level_id']);
            $table->index('achieved_at');
            $table->engine('InnoDB');
            $table->comment('用户会员等级关联表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_membership_levels');
    }
};
