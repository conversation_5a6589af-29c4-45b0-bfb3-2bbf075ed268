<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Task\TaskPointConfig;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\JsonResource;
use Spatie\QueryBuilder\QueryBuilder;
use Spatie\QueryBuilder\AllowedFilter;

class TaskPointConfigController extends Controller
{
    /**
     * 获取任务积分配置列表
     */
    public function index(Request $request): JsonResponse
    {
        $configs = QueryBuilder::for(TaskPointConfig::class)
            ->allowedFilters([
                AllowedFilter::partial('task_name'),
                AllowedFilter::exact('task_type'),
                AllowedFilter::exact('channel'),
                AllowedFilter::exact('is_active'),
            ])
            ->allowedSorts(['id', 'task_type', 'channel', 'points_per_action', 'sort_order', 'created_at'])
            ->defaultSort('sort_order', 'id')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $configs,
        ]);
    }

    /**
     * 创建任务积分配置
     */
    public function store(Request $request): JsonResource
    {
        $validated = $request->validate([
            'task_type' => 'required|string|max:50',
            'task_name' => 'required|string|max:100',
            'channel' => 'nullable|string|max:50',
            'points_per_action' => 'required|numeric|min:0',
            'daily_limit' => 'nullable|integer|min:0',
            'total_limit' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'extra_config' => 'nullable|array',
            'description' => 'nullable|string',
            'sort_order' => 'required|integer|min:0',
        ]);

        $config = TaskPointConfig::create($validated);

        return new JsonResource($config);
    }

    /**
     * 获取单个任务积分配置
     */
    public function show(TaskPointConfig $taskPointConfig): JsonResource
    {
        return new JsonResource($taskPointConfig);
    }

    /**
     * 更新任务积分配置
     */
    public function update(Request $request, TaskPointConfig $taskPointConfig): JsonResource
    {
        $validated = $request->validate([
            'task_type' => 'required|string|max:50',
            'task_name' => 'required|string|max:100',
            'channel' => 'nullable|string|max:50',
            'points_per_action' => 'required|numeric|min:0',
            'daily_limit' => 'nullable|integer|min:0',
            'total_limit' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'extra_config' => 'nullable|array',
            'description' => 'nullable|string',
            'sort_order' => 'required|integer|min:0',
        ]);

        $taskPointConfig->update($validated);

        return new JsonResource($taskPointConfig);
    }

    /**
     * 删除任务积分配置
     */
    public function destroy(TaskPointConfig $taskPointConfig): JsonResponse
    {
        $taskPointConfig->delete();

        return response()->json([
            'success' => true,
            'message' => '任务积分配置删除成功',
        ]);
    }

    /**
     * 批量更新排序
     */
    public function updateSort(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'configs' => 'required|array',
            'configs.*.id' => 'required|integer|exists:task_point_configs,id',
            'configs.*.sort_order' => 'required|integer|min:0',
        ]);

        foreach ($validated['configs'] as $configData) {
            TaskPointConfig::where('id', $configData['id'])
                ->update(['sort_order' => $configData['sort_order']]);
        }

        return response()->json([
            'success' => true,
            'message' => '排序更新成功',
        ]);
    }

    /**
     * 获取任务类型选项
     */
    public function getTaskTypeOptions(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => TaskPointConfig::getTaskTypeOptions(),
        ]);
    }

    /**
     * 获取渠道选项
     */
    public function getChannelOptions(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => TaskPointConfig::getChannelOptions(),
        ]);
    }

    /**
     * 批量启用/禁用
     */
    public function batchToggleStatus(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'integer|exists:task_point_configs,id',
            'is_active' => 'required|boolean',
        ]);

        TaskPointConfig::whereIn('id', $validated['ids'])
            ->update(['is_active' => $validated['is_active']]);

        $action = $validated['is_active'] ? '启用' : '禁用';
        
        return response()->json([
            'success' => true,
            'message' => "批量{$action}成功",
        ]);
    }
}
