<?php

namespace App\Admin\Controllers;

use App\Constants\Permissions;
use App\Exceptions\DataException;
use App\Http\Controllers\Controller;
use App\Models\CmsSetting;
use App\Models\CmsArticle;
use App\Models\Attachment;
use Illuminate\Support\Facades\Auth;
use App\Models\Enums\CmsSettingSortEnum;
use App\Models\Enums\CurrencyEnum;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rules\Enum;
use Illuminate\Validation\Rules\Exists;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;
use App\Models\Enums\CmsTypeEnum;


class CmsArticleController extends Controller
{
    public function __construct()
    {
        // 编辑权限
        $this->hasPermissionOr(Permissions::CmsSettingsUpdate)->except(['index', 'store', 'update', 'destroy']);
        $this->hasPermissionOr(Permissions::CmsSettingsIndex, Permissions::CmsSettingsUpdate)->only(['index', 'show']);
    }

    public function store(Request $request, CmsArticle $CmsArticle): JsonResource
    {
        $validated = $request->validate([
            'title'          => ['required', 'string', 'max:255'],
            'content'        => ['required', 'string'],
            'type'           => ['required', 'integer', new Enum(CmsTypeEnum::class)],
            'sort'           => ['nullable', 'integer'],
            'slug_title'     => ['nullable', 'string', 'max:255'],
            'status'         => ['required', 'boolean'],
            'cms_setting_id' => ['required_if:type,2', 'nullable', 'integer'],
            'tag_ids'        => ['required_if:type,3', 'nullable', 'array'],
            'tag_ids.*'      => ['required_if:type,3', 'integer'],
            'image_id'       => ['required_if:type,3', 'nullable', 'integer'],
            'cate_type'      => ['required_if:type,1', 'nullable', 'integer'],
            'desc'           => ['required_if:type,3', 'nullable', 'string', 'max:255'],
            'meta_description' => ['nullable', 'string', 'max:255'],
        ]);
        DB::beginTransaction();
        try {
            $validated['admin_user_id'] = Auth::user()->id;
            // 将 tag_ids 数组转换为 JSON
            if (isset($validated['tag_ids'])) {
                $validated['tag_ids'] = json_encode($validated['tag_ids']);
            }
            if($validated['status'] == true){
                $validated['published_at'] = now();
            }
            $CmsArticle->fill($validated)->save();
            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }

        return JsonResource::make($CmsArticle);
    }

    public function update(Request $request, CmsArticle $CmsArticle): JsonResource
    {
        $validated = $request->validate([
            'title'          => ['required', 'string', 'max:255'],
            'content'        => ['required', 'string'],
            'type'           => ['required', 'integer', new Enum(CmsTypeEnum::class)],
            'sort'           => ['nullable', 'integer'],
            'slug_title'     => ['nullable', 'string', 'max:255'],
            'status'         => ['required', 'boolean'],
            'cms_setting_id' => ['required_if:type,2', 'nullable', 'integer'],
            'tag_ids'        => ['required_if:type,3', 'nullable', 'array'],
            'tag_ids.*'      => ['required_if:type,3', 'integer'],
            'image_id'       => ['required_if:type,3', 'nullable', 'integer'],
            'cate_type'      => ['required_if:type,1', 'nullable', 'integer'],
            'desc'           => ['required_if:type,3', 'nullable', 'string', 'max:255'],
            'meta_description' => ['nullable', 'string', 'max:255'],
        ]);
        DB::beginTransaction();
        try {
            // 将 tag_ids 数组转换为 JSON
            if (isset($validated['tag_ids'])) {
                $validated['tag_ids'] = json_encode($validated['tag_ids']);
            }
            //status由false变为true时，更新published_at
            if($validated['status'] == true && $CmsArticle->status == false){
                $validated['published_at'] = now();
            }
            $validated['admin_user_id'] = Auth::user()->id;
            $CmsArticle->update($validated);
            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }

        return JsonResource::make($CmsArticle);
    }

    public function destroy(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ids' => ['array', 'required'],
            'ids.*' => [(new Exists(CmsArticle::class, 'id'))],
        ]);
        $ids = Arr::get($validated, 'ids');
        DB::beginTransaction();
        try {
            CmsArticle::query()->whereIn('id', $ids)->update([
                'deleted_at' => now()
            ]);

            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }
        return response()->json();
    }

    public function index(Request $request): AnonymousResourceCollection
    {
        $CmsArticle = QueryBuilder::for(CmsArticle::class, $request)
            ->allowedFilters([
                AllowedFilter::exact('status'),
                AllowedFilter::exact('type'),
                AllowedFilter::partial('adminUser.name'),
            ])
            ->with(['adminUser:id,name','cmsSetting:id,title','image:id,path,disk,module'])
            ->allowedSorts('sort')
            ->defaultSort('sort');
        $res = $CmsArticle->paginate($this->getPerPage());

        return JsonResource::collection($res);
    }

    //获取所有列表
    public function all(Request $request): JsonResource
    {
        $CmsArticle = QueryBuilder::for(CmsArticle::class, $request)
            ->allowedFilters([
                AllowedFilter::exact('status'),
            ])
            ->with(['adminUser:id,name'])
            ->allowedSorts('sort')
            ->defaultSort('-sort');
        $res = $CmsArticle->get();

        return JsonResource::collection($res);
    }

    public function show(CmsArticle $CmsArticle): JsonResource
    {
        $CmsArticle->loadMissing([
            'image:id,path,disk,module',
            'adminUser:id,name'
        ]);
        return JsonResource::make($CmsArticle);
    }

    //排序单独修改
    public function sort(Request $request, CmsArticle $CmsArticle): JsonResource
    {
        $validated = $request->validate([
            'sort' => ['required', 'int'],
        ]);
        $CmsArticle->update($validated);
        return JsonResource::make($CmsArticle);
    }

    // 上架文章
    public function status(Request $request, CmsArticle $CmsArticle): JsonResource
    {
        $validated = $request->validate([
            'sort_type' => ['required', 'int', new Enum(CmsSettingSortEnum::class)],
            'status' => ['required', 'boolean'],
            'cms_setting_id' => ['required', new Exists(CmsSetting::class, 'id')],
            'position' => ['nullable', 'string', new Enum(CurrencyEnum::class)],
            'cms_article_id' => ['nullable', new Exists(CmsArticle::class, 'id')],
        ]);
        DB::beginTransaction();
        try {
            // 如果 status 为 true，则更新 published_at
            if (!empty($validated['status']) && $validated['status'] == true) {
                $validated['published_at'] = now();
            }
            $validated['admin_user_id'] = Auth::user()->id;

            $CmsArticle->update(Arr::except($validated, ['sort_type', 'cms_article_id', 'position']));

            // 处理排序
            if ($validated['sort_type'] == CmsSettingSortEnum::sortType->value && $validated['status'] == true) {
                // 找到离当前数据最近的（比当前 `published_at` 大的最小的记录）
                $nearest = CmsArticle::where('published_at', '<', $CmsArticle->published_at)
                    ->whereNotNull('published_at')
                    ->where('cms_setting_id', $validated['cms_setting_id'])
                    ->orderBy('published_at', 'desc')
                    ->first();
                if ($nearest) {
                    $newSort = ($nearest->sort)+1;
                } else {
                    // 如果没有比它更大的，放到最后
                    $newSort = CmsArticle::max('sort') + 1;
                }
                $CmsArticle->update(['sort' => $newSort]);
            } elseif ($validated['sort_type'] == CmsSettingSortEnum::sortPublished->value && isset($validated['cms_article_id'], $validated['position']) && $validated['status'] == true ) {
                // 获取目标排序项
                $target = CmsArticle::find($validated['cms_article_id']);

                if ($target) {
                    if ($validated['position'] === CurrencyEnum::Before->value) {
                        CmsArticle::where('sort', '>', $target->sort)
                            ->where('cms_setting_id', $validated['cms_setting_id'])
                            ->increment('sort');

                        $CmsArticle->update(['sort' => $target->sort + 1]);
                    } elseif ($validated['position'] === CurrencyEnum::After->value) {
                        CmsArticle::where('sort', '>=', $target->sort)
                            ->where('cms_setting_id', $validated['cms_setting_id'])
                            ->increment('sort');

                        $CmsArticle->update(['sort' => $target->sort]);
                    }
                }
            }
            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }
        return JsonResource::make($CmsArticle);
    }

    //下架
    public function down(Request $request, CmsArticle $CmsArticle): JsonResource
    {
        $validated = $request->validate([
            'status' => ['required', 'boolean'],
        ]);
        if ($validated['status'] !== false) {
            throw new DataException('Status must be false.');
        }
        DB::beginTransaction();
        try {
            if (!empty($validated['status']) && $validated['status'] == false) {
                $validated['published_at'] = null;
            }
            $CmsArticle->update($validated);
            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }
        return JsonResource::make($CmsArticle);
    }
}
