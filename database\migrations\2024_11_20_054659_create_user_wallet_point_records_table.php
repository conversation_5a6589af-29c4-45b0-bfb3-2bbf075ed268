<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_wallet_point_records', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_id')->index();
            $table->bigInteger('user_wallet_id')->index();
            $table->string('change_type', 64)->comment('变更类型');
            $table->decimal('change_point', 10, 4)->comment('改变积分数量');
            $table->decimal('current_point', 10, 4)->comment('当前积分数量');
            $table->decimal('before_point', 10, 4)->comment('之前积分数量');
            $table->string('source_type', 32)->nullable()->comment('来源模型');
            $table->bigInteger('source_id')->nullable()->comment('来源id');
            $table->string('desc', 512)->nullable()->comment('描述');
            $table->engine('InnoDB');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_wallet_point_records');
    }
};
