<?php

namespace App\Models\Enums\Membership;

enum MembershipPointSourceEnum: string
{
    case Order = 'order';
    case Birthday = 'birthday';
    case Registration = 'registration';
    case Promotion = 'promotion';
    case Task = 'task';
    case Manual = 'manual';
    case Refund = 'refund';

    /**
     * 获取来源描述
     */
    public function getDescription(): string
    {
        return match ($this) {
            self::Order => '订单消费获得',
            self::Birthday => '生日奖励',
            self::Registration => '注册奖励',
            self::Promotion => '活动奖励',
            self::Task => '任务奖励',
            self::Manual => '手动调整',
            self::Refund => '退款扣除',
        };
    }

    /**
     * 是否为正向积分（增加积分）
     */
    public function isPositive(): bool
    {
        return match ($this) {
            self::Order, self::Birthday, self::Registration, self::Promotion, self::Task, self::Manual => true,
            self::Refund => false,
        };
    }

    /**
     * 获取所有来源选项
     */
    public static function options(): array
    {
        return array_map(fn($case) => [
            'value' => $case->value,
            'label' => $case->getDescription(),
            'is_positive' => $case->isPositive(),
        ], self::cases());
    }
}
