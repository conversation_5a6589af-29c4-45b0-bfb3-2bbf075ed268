<?php

namespace App\Providers;

use GuzzleHttp\Client;
use Illuminate\Support\ServiceProvider;
use Laravel\Socialite\Contracts\Factory;

class SocialiteServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {

    }

    /**
     * Bootstrap services.
     */
    public function boot(Factory $socialite): void
    {
        $clientConfig = [
            'verify' => false,
            'timeout' => 30,
        ];
        if ($proxy = config('services.socialite_proxy')) {
            if ($proxy['enabled']) {
                $clientConfig['proxy'] = [
                    'http' => $proxy['http'],
                    'https' => $proxy['https'],
                ];
            }
        }
        $client = new Client($clientConfig);
        foreach (['google', 'facebook'] as $driver) {
            $socialite->driver($driver)->setHttpClient($client);
        }
    }
}
