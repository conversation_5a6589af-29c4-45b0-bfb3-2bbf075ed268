<?php

namespace App\Models\Enums\Order\Payment;

use Illuminate\Support\Arr;

enum PaymentMethodTypeEnum: int
{
    case  PayPal = 1;
    case  Airwallex = 2;

    public function desc(): string
    {

        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::PayPal->value => 'payPal',
            self::Airwallex->value => '信用卡',
        ];
    }

    /**
     * 验证规则
     * @return array
     */
    public function validateRules(): array
    {
        return match ($this) {
            self::PayPal => [
                'sandbox' => ['required', 'boolean'],
                'client_id' => ['required', 'string'],
                'client_secret' => ['required', 'string'],
                'sandbox_client_id' => ['required', 'string'],
                'sandbox_client_secret' => ['required', 'string'],
                // 'app_id' => ['nullable', 'string'],
                // 'payment_action' => ['required', 'in:Sale,Authorization,Order'],
                // 'currency' => ['nullable', 'string'],
                // 'notify_url' => ['nullable', 'string'],
                // 'locale' => ['nullable', 'string'],
                // 'validate_ssl' => ['required', 'boolean'],
            ],
            self::Airwallex => [
                'sandbox' => ['required', 'boolean'],
                'client_id' => ['required', 'string'],
                'api_key' => ['required', 'string'],
                'sandbox_client_id' => ['required', 'string'],
                'sandbox_api_key' => ['required', 'string'],
                // '3ds_callback_url' => ['nullable', 'string'],
                // 'error_page_url' => ['nullable', 'string'],
                // 'success_page_url' => ['nullable', 'string'],
            ],
            default => [],
        };
    }
}
