<?php

namespace App\Models;

use App\Models\Order\Order;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property float $delivery_fee
 * @property float $delivery_fee_discount
 * @property float $subtotal
 * @property float $discount
 * @property float $total
 */
class Cart extends MiddleModel
{
    use HasFactory;
    use SoftDeletes;

    protected $guarded = [];
    protected $appends = [
        'delivery_fee_real',
        'delivery_fee_real_agent',
        'discount_rate',
        'total_num',
        'subtotal_agent',
        'discount_agent',
        'coupon_discount_agent',
        'delivery_fee_agent',
        'delivery_fee_discount_agent',
        'total_agent',
    ];

    protected $casts = [
        'subtotal' => 'float',
        'discount' => 'float',
        'coupon_discount' => 'float',
        'delivery_fee' => 'float',
        'delivery_fee_discount' => 'float',
        'total' => 'float',
        'is_checkout' => 'boolean',
        'is_activity' => 'boolean',
        'checkout_data' => 'json',
    ];

    public static function booted()
    {
        static::creating(function (self $model) {
            // 绑定默认运输方式
            $shipping = Shipping::getDefaultShipping();
            $model->shipping_id = $shipping->id;
            $model->shipping_type = $shipping->type;
            $model->delivery_fee = 0;
            // //绑定默认国家
            // $defaultCountryCurrency = CountryCurrency::getDefaultCountryCurrency();
            // if ($defaultCountryCurrency) {
            //     $model->country = $defaultCountryCurrency->country_code;
            //     $model->country_updated_at = now();
            // } else {
            //     $model->country = 'US';
            //     $model->country_updated_at = now();
            // }
        });
    }


    public function items(): HasMany
    {
        return $this->hasmany(CartItem::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function shipping()
    {
        return $this->belongsTo(Shipping::class);
    }

    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    // 运费实际金额
    public function deliveryFeeReal(): Attribute
    {
        return Attribute::get(
            fn () => $this->delivery_fee - $this->delivery_fee_discount
        );
    }

    public function discountRate(): Attribute
    {
        return Attribute::make(function () {
            return getRate(($this->subtotal - $this->discount), $this->subtotal, 4, "");
        });
    }

    public function totalNum(): Attribute
    {
        return Attribute::make(function () {
            if ($this->relationLoaded('items')) {
                return $this->items->sum('num');
            }
            return null;
        });
    }

    /**************** 金额币种转换 ****************/
    public function deliveryFeeRealAgent(): Attribute
    {
        return Attribute::get(
            fn () => convertPrice((float) $this->delivery_fee - $this->delivery_fee_discount, currentCurrency())
        );
    }

    public function subtotalAgent(): Attribute
    {
        return Attribute::get(function () {
            return convertPrice($this->subtotal, currentCurrency());
        });
    }

    public function discountAgent(): Attribute
    {
        return Attribute::get(
            fn () => convertPrice($this->discount, currentCurrency())
        );
    }

    public function couponDiscountAgent(): Attribute
    {
        return Attribute::get(
            fn () => convertPrice($this->coupon_discount, currentCurrency())
        );
    }

    public function deliveryFeeAgent(): Attribute
    {
        return Attribute::get(
            fn () => convertPrice($this->delivery_fee, currentCurrency())
        );
    }

    public function deliveryFeeDiscountAgent(): Attribute
    {
        return Attribute::get(
            fn () => convertPrice($this->delivery_fee_discount, currentCurrency())
        );
    }

    public function totalAgent(): Attribute
    {
        return Attribute::get(
            fn () => convertPrice($this->total, currentCurrency())
        );
    }
}
