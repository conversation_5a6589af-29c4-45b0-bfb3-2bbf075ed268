<?php

namespace App\Services;

use App\Exceptions\DataException;
use App\Models\Enums\Order\OrderPaidTypeEnum;
use App\Models\Enums\Order\OrderStatusEnum;
use App\Models\Enums\Reward\RewardFansTypeEnum;
use App\Models\Enums\Reward\RewardRemarkTypeEnum;
use App\Models\Enums\Reward\RewardTypeEnum;
use App\Models\Order\FastPaypalOrder;
use App\Models\Order\Order;
use App\Models\Order\OrderItem;
use App\Models\Product\ProductVariant;
use App\Models\User\UserCoupon;
use Arr;
use Auth;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HigherOrderWhenProxy;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\AllowedInclude;
use Spatie\QueryBuilder\QueryBuilder;
use App\Jobs\Order\OrderPaid;
use App\Models\Order\Payment\PaypalOrder;
use App\Models\Order\OrderAddress;
use App\Models\Enums\Order\OrderAddressTypeEnum;
use App\Models\Enums\Order\Payment\PaymentMethodTypeEnum;
use App\Models\Country;
use App\Models\State;
use Illuminate\Support\Str;
use Throwable;

class OrderService
{

    /**
     * @param Order $order
     * @return bool
     */
    public function orderRemoveUsedCoupon(Order $order): bool
    {
        $order->loadMissing(['items.product']);
        // 删除旧的优惠券关联
        $orderItems = $order->{'items'};
        // 带有优惠券的修改
        try {
            DB::beginTransaction();
            $userCouponId = null;
            $orderItems->whereNotNull('coupon_id')
                ->each(function (OrderItem $item) use ($orderItems, &$userCouponId) {
                    $userCouponId = $item->user_coupon_id;
                    // 同款, 删掉优惠券的, 同款数量增加
                    if (
                        $sameItem = $orderItems
                        ->where('product_type', $item->product_type)
                        ->where('product_id', $item->product_id)
                        ->whereNull('coupon_id')
                        ->first()
                    ) {
                        $sameItem->num = $sameItem->num + $item->num;
                        $sameItem->save() && $item->delete();
                    } else {
                        $item->update([
                            'user_coupon_id' => null,
                            'discount_price' => 0,
                            'price' => $item->product->getPrice(),
                            'original_price' => $item->product->getOriginalPrice(),
                        ]);
                    }
                });
            // 恢复订单优惠券
            if ($userCouponId) {
                $userCoupon = UserCoupon::query()->find($userCouponId);
                $userCoupon?->update(['used_at' => null]);
            }

            DB::commit();
            return true;
        } catch (Throwable $exception) {
            DB::rollBack();
            return false;
        }
    }

    /**
     * 订单列表
     * @param Request $request
     * @return QueryBuilder|HigherOrderWhenProxy
     */
    public function getIndexQueryBuilder(Request $request): QueryBuilder|HigherOrderWhenProxy
    {
        return QueryBuilder::for(Order::class, $request)
            ->allowedIncludes([
                'payment',
                'billingAddress',
                'billingAddress.country',
                'shippingAddress',
                'shippingAddress.country',
                'currency',
                'items',
                'user',
                AllowedInclude::callback('items.product', function ($builder) {
                    $builder->morphWith([
                        ProductVariant::class => ['product:id,title,slug_title,spu', 'colorAttribute', 'sizeAttribute', 'image:path,id,module'],
                    ]);
                })
            ])
            ->allowedFilters([
                AllowedFilter::partial('product_name', 'items.name'),
                AllowedFilter::exact('status'),
                AllowedFilter::exact('paid_status'),
                AllowedFilter::partial('no'),
                AllowedFilter::exact('is_first_order'),
                AllowedFilter::exact('user_type'),
                AllowedFilter::callback('user_email', function ($query, $value) {
                    $query->whereHas('user', function ($q) use ($value) {
                        $q->where('email', 'like', '%' . $value . '%');
                    });
                }),
                AllowedFilter::callback('payment_type', function ($query, $value) {
                    $query->where('payment_type', 'like', '%' . PaymentMethodTypeEnum::tryFrom($value)->name . '%');
                }),
                AllowedFilter::exact('email'),
                AllowedFilter::callback('start_at', function ($query, $value) {
                    $query->where('created_at', '>=', $value);
                }),
                AllowedFilter::callback('end_at', function ($query, $value) {
                    $query->where('created_at', '<=', $value);
                }),
            ])
            ->defaultSort('-created_at');
    }

    /**
     * 添加订单返现金额
     * @param \App\Models\Order\Order $order
     * @return void
     */
    public function createUserInviteReward(Order $order)
    {
        // 确保订单下生成奖励的返现奖励
        if (!$order->inviteRewards()->exists() && $order->user) {
            // 分享规则
            $rule = $order->user->sharingRule;
            if (!$rule) {
                return;
            }
            $reward_type = $rule->reward_type;

            // 专属返现
            $user_reward = [
                // 订单信息
                'order_user_id' => $order->user->id,
                'order_created_at' => $order->created_at,
                'order_total' => $order->total,
                // 奖励信息
                'reward_user_id' => $order->user->parent_id,
                'rule_id' => $rule->id,
                'reward_type' => $reward_type,
                // 发放时间
                'cashback_at' => $order->created_at->addDays(Arr::get($rule->condition, 'days', 0)),
                // 备注类型
                'remark_type' => RewardRemarkTypeEnum::OrderSuccess->value
            ];
            switch ($reward_type) {
                case RewardTypeEnum::FixedAmount->value:
                    $user_reward['reward_amount'] = sharingRuleService()->getFixedAmountReward($rule, $order, RewardFansTypeEnum::Exclusive);
                    break;
                case RewardTypeEnum::Percent->value:
                    $user_reward['reward_amount'] = sharingRuleService()->getPercentReward($rule, $order, RewardFansTypeEnum::Exclusive);
                    break;
                case RewardTypeEnum::Coupon->value:
                    $user_reward['reward_coupon_id'] = sharingRuleService()->getCouponReward($rule, $order, RewardFansTypeEnum::Exclusive);
                    break;
            }
            $order->inviteRewards()->create($user_reward);

            // 普通返现
            $ordinary_reward = null;
            switch ($reward_type) {
                case RewardTypeEnum::FixedAmount->value:
                    $ordinary_reward = sharingRuleService()->getFixedAmountReward($rule, $order, RewardFansTypeEnum::Ordinary);
                    break;
                case RewardTypeEnum::Percent->value:
                    $ordinary_reward = sharingRuleService()->getPercentReward($rule, $order, RewardFansTypeEnum::Ordinary);
                    break;
                case RewardTypeEnum::Coupon->value:
                    $ordinary_reward = sharingRuleService()->getCouponReward($rule, $order, RewardFansTypeEnum::Ordinary);
                    break;
            }

            if (!$ordinary_reward) {
                return;
            }

            // 获取订单用的所有的上级
            $parents = $order->user->allParents()->get();
            foreach ($parents as $parent) {
                //排除专属返现的用户
                if ($parent->id == $order->user->parent_id) {
                    continue;
                }
                $user_reward = [
                    // 订单信息
                    'order_user_id' => $order->user->id,
                    'order_created_at' => $order->created_at,
                    'order_total' => $order->total,
                    // 奖励信息
                    'reward_user_id' => $parent->id,
                    'rule_id' => $rule->id,
                    'reward_type' => $reward_type,
                    // 发放时间
                    'cashback_at' => $order->created_at->addDays(Arr::get($rule->condition, 'days', 0)),
                    // 备注类型
                    'remark_type' => RewardRemarkTypeEnum::OrderSuccess->value
                ];

                switch ($reward_type) {
                    case RewardTypeEnum::FixedAmount->value:
                        $user_reward['reward_amount'] = $ordinary_reward;
                        break;
                    case RewardTypeEnum::Percent->value:
                        $user_reward['reward_amount'] = $ordinary_reward;
                        break;
                    case RewardTypeEnum::Coupon->value:
                        $user_reward['reward_coupon_id'] = $ordinary_reward;
                        break;
                }
                $order->inviteRewards()->create($user_reward);
            }
        }
    }

    /**
     * 快速支付创建订单
     * @param \App\Models\Order\FastPaypalOrder $fastPaypalOrder
     * @param mixed $orderResponse
     * @return void
     * @throws \App\Exceptions\DataException
     */
    public function paypalFastCreateOrder(FastPaypalOrder $fastPaypalOrder, $orderResponse): Order
    {
        // 生成订单
        $order = new Order();
        // 绑定用户
        $user = Auth::user();
        $email = '';
        if ($user) {
            $order->user()->associate($user);
            $email = $user->email;
        } else {
            $email = Arr::get($orderResponse, 'payer.email_address');
        }
        // 交易号
        $transaction_id = Arr::get($orderResponse, 'purchase_units.0.payments.captures.0.id');
        // 获取购物车列表
        $cart = $fastPaypalOrder->cart;
        $cartItems = $cart->items()->with(['productVariant.product'])->get();
        if ($cartItems->isEmpty()) {
            throw new DataException("There is no product in your shopping cart yet.");
        }
        $orderItems = [];
        $discount = $coupon_discount = $totalPrice = $subtotalPrice = 0;
        foreach ($cartItems as $item) {
            /**
             * @var \App\Models\Product\ProductVariant $product
             * @var \App\Models\CartItem $item
             */
            $product = $item->productVariant;
            $orderItem = new OrderItem([
                'num' => $item->num,
                'name' => $product->getName(),
                'price' => $item->price,  // 购物车中售价
                'coupon_discount_price' => $item->discount, // 购物车中优惠(购物车优惠部分)
                'coupon_id' => $item->coupon_id, // 购物车中优惠(购物车优惠部分)
                'original_price' => $product->getOriginalPrice(), // 商品原价
                'image_url' => $product->getImageUrl(),
                'product_info' => json_encode($product->getProductInfo()) // 商品信息
            ]);
            $subtotalPrice += $orderItem->num * $orderItem->original_price;
            $totalPrice += $orderItem->num * $orderItem->price - $item->discount;
            $discount += $item->original_price - $item->price + $item->discount;
            $coupon_discount += $item->discount;
            $orderItem->product()->associate($product);
            $orderItems[] = $orderItem;
        }
        // 保存订单
        $order->fill([
            'no' => $fastPaypalOrder->no,
            'subtotal_price' => $subtotalPrice,
            'total_price' => $totalPrice,
            'discount_price' => $discount, // 总折扣
            'coupon_discount_price' => $coupon_discount, // 优惠券折扣
            'total' => $totalPrice + $cart->delivery_fee - $cart->delivery_fee_discount, // 订单总金额
            'shipping_id' => $cart->shipping_id,  // 购物车中运费ID
            'shipping_type' => $cart->shipping_type,  // 购物车中运费类型
            'shipping_fee' => $cart->delivery_fee,  // 购物车中运费
            'shipping_fee_discount' => $cart->delivery_fee_discount,
            'shipping_fee_discount_id' => $cart->delivery_fee_discount_id,
            'paid_type' => OrderPaidTypeEnum::PayPal,
            'status' => OrderStatusEnum::Paid,
            'currency_id' => currentCurrency()->id, // 支付货币ID
            'email' => $email,
            'transaction_id' => $transaction_id,
        ])->save();
        // 保存订单内容
        $order->items()->saveMany($orderItems);

        // 同步支付订单ID
        $fastPaypalOrder->paypalOrder()->update([
            'order_id' => $order->id,
        ]);
        $fastPaypalOrder->refresh();
        $order->paypalOrder->update(['detail' => $orderResponse]);
        // 变更购物车标识状态
        $cart->update([
            'order_id' => $order->id,
            'is_checkout' => true
        ]);
        $address = Arr::get($orderResponse, 'purchase_units.0.shipping');
        if($address) {
            $countryId = Country::query()->where('iso_code', Arr::get($address, 'address.country_code'))->value('id');
            $state = State::query()->where('country_id', $countryId)->where('iso_code', Arr::get($address, 'address.admin_area_1'))->value('name');
            $baseAddress = [
                'first_name' => Str::before(Arr::get($address, 'name.full_name'), ' '),
                'last_name' => Str::after(Arr::get($address, 'name.full_name'), ' '),
                'zip' => Arr::get($address, 'address.postal_code'),
                'address' => Arr::get($address, 'address.address_line_1'),
                'country_id' => $countryId ?: 21,
                'state' => $state ?? 'united states',
                'city' => Arr::get($address, 'address.admin_area_2'),
            ];
            $order->billingAddress()->create([
                'type' => OrderAddressTypeEnum::Billing,
                ...$baseAddress
            ]);
            $order->shippingAddress()->create([
                'type' => OrderAddressTypeEnum::Shipping,
                ...$baseAddress
            ]);
        }

        OrderPaid::dispatchSync($order, $order->paypalOrder);
        return $order;
    }

    /**
     * 支付创建订单
     * @param \App\Models\Order\Payment\PaypalOrder $paypalOrder
     * @param mixed $orderResponse
     * @return void
     * @throws \App\Exceptions\DataException
     */
    public function paypalCreateOrder(PaypalOrder $paypalOrder, $orderResponse): Order
    {
        // 生成订单
        $order = new Order();
        // 绑定用户
        $user = Auth::user();
        if ($user) {
            $order->user()->associate($user);
        }
        // 交易号
        $transaction_id = Arr::get($orderResponse, 'purchase_units.0.payments.captures.0.id');
        // 获取购物车列表
        $cart = $paypalOrder->cart;

        $checkout = $cart->checkout_data ?? [];

        $cartItems = $cart->items()->with(['productVariant.product'])->get();
        if ($cartItems->isEmpty()) {
            throw new DataException("There is no product in your shopping cart yet.");
        }
        $orderItems = [];
        $discount = $coupon_discount = $totalPrice = $subtotalPrice = 0;
        foreach ($cartItems as $item) {
            /**
             * @var \App\Models\Product\ProductVariant $product
             * @var \App\Models\CartItem $item
             */
            $product = $item->productVariant;
            $orderItem = new OrderItem([
                'num' => $item->num,
                'name' => $product->getName(),
                'price' => $item->price,  // 购物车中售价
                'coupon_discount_price' => $item->discount, // 购物车中优惠(购物车优惠部分)
                'coupon_id' => $item->coupon_id, // 购物车中优惠(购物车优惠部分)
                'user_coupon_id' => null, // 默认为null，下面会检查并设置
                'original_price' => $product->getOriginalPrice(), // 商品原价
                'image_url' => $product->getImageUrl(),
                'product_info' => json_encode($product->getProductInfo()), // 商品信息
                'is_activity' => $item->is_activity,
                'user_experience_activity_id' => $item->user_experience_activity_id,
            ]);

            // 如果有优惠券ID且用户已登录，检查并关联用户优惠券
            if ($item->coupon_id && $user) {
                $userCoupon = UserCoupon::where('coupon_id', $item->coupon_id)
                    ->where('user_id', $user->id)
                    ->whereNull('used_at')
                    ->first();
                if ($userCoupon) {
                    $orderItem->user_coupon_id = $userCoupon->id;
                }
            }
            $subtotalPrice += $orderItem->num * $orderItem->original_price;
            $totalPrice += $orderItem->num * $orderItem->price - $item->discount;
            $discount += ($item->original_price - $item->price) * $item->num + $item->discount;
            $coupon_discount += $item->discount;
            $orderItem->product()->associate($product);
            $orderItems[] = $orderItem;
        }
        // 保存订单
        $order->fill([
            'subtotal_price' => $subtotalPrice,
            'total_price' => $totalPrice,
            'discount_price' => $discount, // 总折扣
            'coupon_discount_price' => $coupon_discount, // 优惠券折扣
            'total' => $totalPrice + $cart->delivery_fee - $cart->delivery_fee_discount, // 订单总金额
            'shipping_id' => $cart->shipping_id,  // 购物车中运费ID
            'shipping_type' => $cart->shipping_type,  // 购物车中运费类型
            'shipping_fee' => $cart->delivery_fee,  // 购物车中运费
            'shipping_fee_discount' => $cart->delivery_fee_discount,
            'shipping_fee_discount_id' => $cart->delivery_fee_discount_id,
            'paid_type' => OrderPaidTypeEnum::PayPal,
            'status' => OrderStatusEnum::Paid,
            'currency_id' => currentCurrency()->id, // 支付货币ID
            'email' => $checkout['email'] ?? '',
            'transaction_id' => $transaction_id,
        ])->save();
        // 保存订单内容
        $order->items()->saveMany($orderItems);
        // 绑定账单地址
        $billingAddress = $checkout['billing_address'];
        if ($billingAddress) {
            unset($billingAddress['country_code']);
            $billingAddress = (new OrderAddress($billingAddress))->fill(['type' => OrderAddressTypeEnum::Billing]);
            $order->billingAddress()->save($billingAddress);
            // 同步账单地址到用户
            if ($billingAddress->is_save) {
                $order->refresh();
                $order->syncAddressToUser($order->billingAddress, true);
            }
        }
        // 绑定物流地址
        $shippingAddress = $checkout['shipping_address'];
        if ($shippingAddress) {
            unset($shippingAddress['country_code']);
            $shippingAddress = (new OrderAddress($shippingAddress))->fill(['type' => OrderAddressTypeEnum::Shipping]);
            $order->shippingAddress()->save($shippingAddress);
            // 同步物流地址到用户
            if ($shippingAddress->is_save) {
                $order->refresh();
                $order->syncAddressToUser($order->shippingAddress, false);
            }
        }
        $paypalOrder->update(['detail' => $orderResponse, 'order_id' => $order->id]);
        $paypalOrder->refresh();
        // 变更购物车标识状态
        $cart->update([
            'order_id' => $order->id,
            'is_checkout' => true,
            'is_checkout_processing' => false,
            'checkout_data' => null
        ]);
        OrderPaid::dispatchSync($order, $paypalOrder);
        return $order;
    }
}
