<?php

namespace App\Models\Enums\Promotion;

use Illuminate\Support\Arr;

enum PromotionAlignmentMethodEnum: int
{
    case CenterAlignment = 1;
    case LeftAlignment = 2;
    case RightAlignment = 3;

    public function desc(): string
    {
        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::CenterAlignment->value => 'Center', // 居中
            self::LeftAlignment->value => 'Left', // 左对齐
            self::RightAlignment->value => 'Right', // 右对齐
        ];
    }

}
