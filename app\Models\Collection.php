<?php

namespace App\Models;

use App\Constants\CacheKey;
use App\Models\Enums\CollectionJumpTypeEnum;
use App\Models\Product\Product;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\MiddleModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

/**
 * @property CollectionJumpTypeEnum $jump_type
 * @property int id
 */
class Collection extends MiddleModel
{
    use HasFactory;
    use SoftDeletes;

    protected $guarded = [];

    protected $appends = [
        'product_ids'
    ];

    protected $casts = [
        'active' => 'bool',
        'is_related' => 'bool',
        'is_banner_hidden' => 'bool',
        'extra' => 'json',
        'jump_type' => CollectionJumpTypeEnum::class
    ];

    public static function booted(): void
    {
        // 保存的时候执行
        static::saving(function (self $collection) {
            $collection->slug_title = Str::slug($collection->title);
        });
        static::creating(function ($banner) {
            cachePartialDelete(CacheKey::CollectionSearchList->getKey('*'));
        });
        static::saved(function ($banner) {
            cachePartialDelete(CacheKey::CollectionSearchList->getKey('*'));
        });
    }

    /**
     * 商品列表
     * @return BelongsToMany
     */
    public function products(): BelongsToMany
    {
        return $this->belongsToMany(Product::class, CollectionProducts::class);
    }

    public function image(): BelongsTo
    {
        return $this->belongsTo(Attachment::class, 'image_id');
    }

    public function imageBanner(): BelongsTo
    {
        return $this->belongsTo(Attachment::class, 'image_banner_id');
    }

    public function imagePad(): BelongsTo
    {
        return $this->belongsTo(Attachment::class, 'image_banner_pad_id');
    }

    public function imageApp(): BelongsTo
    {
        return $this->belongsTo(Attachment::class, 'image_banner_app_id');
    }

    public function children(): HasMany
    {
        return $this->hasMany(Collection::class, 'parent_id');
    }

    public function productIds(): Attribute
    {
        return Attribute::get(function () {
            if ($this->relationLoaded('products')) {
                return $this->products->pluck('id');
            }
            return null;
        });
    }

    public function parent()
    {
        return $this->belongsTo(Collection::class, 'parent_id');
    }

    /**
     * 推荐集合IDs
     * @return HasMany
     */
    public function associatedCollectionIds(): HasMany
    {
        return $this->hasMany(AssociatedCollection::class);
    }

    /**
     * 推荐集合
     * @return BelongsToMany
     */
    public function associatedCollections(): BelongsToMany
    {
        return $this->belongsToMany(
            Collection::class,
            AssociatedCollection::class,
            'collection_id',
            'associated_id',
            'id',
            'id'
        );
    }

    /**
     * 关联的集合IDs
     * @return HasMany
     */
    public function tabCollectionIds(): HasMany
    {
        return $this->hasMany(CollectionTab::class);
    }


    /**
     * tab 集合
     * @return BelongsToMany
     */
    public function tabCollections(): BelongsToMany
    {
        return $this->belongsToMany(
            Collection::class,
            'collection_tabs',
            'collection_id',
            'tab_id',
            'id',
            'id'
        );
    }

}
