<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Models\Currency;
use App\Models\Country;
class SetCurrencyFromHeader
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $currencyCode = $request->header('X-Currency') ?: 'USD';
        // 根据货币代码查找货币
        $currency = Currency::query()->where('currency_code', $currencyCode)->first();
        if ($currency) {
            // 设置货币符号到应用中，这里假设我们使用一个全局辅助函数或服务容器来存储
            app()->instance('currency', $currency);
        }

        return $next($request);
    }
}
