<?php

namespace App\Models\Membership;

use App\Models\MiddleModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Builder;

/**
 * @property int $id
 * @property string $name
 * @property string $slug
 * @property string $description
 * @property float $min_spend_amount
 * @property float|null $max_spend_amount
 * @property float $point_rate
 * @property array $benefits
 * @property string $color
 * @property string|null $icon
 * @property int $sort_order
 * @property bool $is_active
 */
class MembershipLevel extends MiddleModel
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
        'min_spend_amount' => 'float',
        'point_rate' => 'float',
        'benefits' => 'array',
        'is_active' => 'boolean',
        'sort_order' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 用户会员等级关联
     */
    public function userMembershipLevels(): HasMany
    {
        return $this->hasMany(UserMembershipLevel::class);
    }

    /**
     * 会员积分记录
     */
    public function pointRecords(): HasMany
    {
        return $this->hasMany(MembershipPointRecord::class);
    }

    /**
     * 作用域：仅活跃的等级
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    /**
     * 作用域：按排序顺序
     */
    public function scopeOrdered(Builder $query): Builder
    {
        return $query->orderBy('sort_order')->orderBy('min_spend_amount');
    }

    /**
     * 根据消费金额获取对应的会员等级
     */
    public static function getLevelBySpendAmount(float $amount): ?self
    {
        return self::active()
            ->where('min_spend_amount', '<=', $amount)
            ->orderBy('min_spend_amount', 'desc')
            ->first();
    }

    /**
     * 获取所有等级列表（按顺序）
     */
    public static function getAllLevels(): \Illuminate\Database\Eloquent\Collection
    {
        return self::active()->ordered()->get();
    }

    /**
     * 获取最低等级（用作默认等级）
     */
    public static function getLowestLevel(): ?self
    {
        return self::active()->ordered()->first();
    }

    /**
     * 检查是否可以升级到此等级
     */
    public function canUpgradeFrom(float $currentSpendAmount): bool
    {
        return $currentSpendAmount >= $this->min_spend_amount &&
            ($this->max_spend_amount === null || $currentSpendAmount <= $this->max_spend_amount);
    }

    /**
     * 获取等级权益描述
     */
    public function getBenefitsDescription(): array
    {
        $benefits = $this->benefits ?? [];
        $descriptions = [];

        if (isset($benefits['point_rate'])) {
            $descriptions[] = "积分比例：{$benefits['point_rate']}";
        }

        if (isset($benefits['birthday_bonus'])) {
            $descriptions[] = "生日福利：{$benefits['birthday_bonus']}";
        }

        if (isset($benefits['free_shipping_threshold'])) {
            $threshold = $benefits['free_shipping_threshold'];
            $descriptions[] = $threshold > 0 ? "满{$threshold}元免运费" : "全场免运费";
        }

        if (isset($benefits['exclusive_coupons'])) {
            $descriptions[] = "专属优惠券";
        }

        if (isset($benefits['priority_customer_service'])) {
            $descriptions[] = "优先客服";
        }

        if (isset($benefits['early_access'])) {
            $descriptions[] = "新品抢先购";
        }

        if (isset($benefits['vip_events'])) {
            $descriptions[] = "VIP活动邀请";
        }

        if (isset($benefits['personal_shopper'])) {
            $descriptions[] = "专属购物顾问";
        }

        return $descriptions;
    }

    /**
     * 获取下一个等级
     */
    public function getNextLevel(): ?self
    {
        return self::active()
            ->where('min_spend_amount', '>', $this->min_spend_amount)
            ->orderBy('min_spend_amount', 'asc')
            ->first();
    }

    /**
     * 获取升级所需的额外消费金额
     */
    public function getUpgradeRequiredAmount(float $currentSpendAmount): ?float
    {
        $nextLevel = $this->getNextLevel();
        if (!$nextLevel) {
            return null; // 已经是最高等级
        }

        return max(0, $nextLevel->min_spend_amount - $currentSpendAmount);
    }
}
