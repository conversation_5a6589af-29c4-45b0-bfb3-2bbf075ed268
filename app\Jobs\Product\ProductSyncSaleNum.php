<?php

namespace App\Jobs\Product;

use App\Models\Order\Order;
use App\Models\Product\ProductVariant;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;

class ProductSyncSaleNum implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 同步产品销量
     */
    public function __construct(public Order $order)
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $order = $this->order;
        $items = $order->items()->get();
        foreach ($items as $item) {
            // 如果是变体，则同步销量
            if ($item->product instanceof ProductVariant) {
                // 同步销量，注：这里的 $item->product 是变体，需要获取变体对应的产品
                $item->product->product->increment('sale_num', $item->num);
            }
        }
    }
}
