<?php

namespace App\Models\Email;

use App\Models\AdminUser;
use App\Models\Enums\EmailRuleTypeEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\MiddleModel;

/**
 * @property string $content
 * @property array $data
 */
class EmailTemplate extends MiddleModel
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
        'enabled' => 'boolean',
        'data' => 'json',
        'type' => EmailRuleTypeEnum::class
    ];

    const BASE_DATA = [
        // 优惠券信息
        'couponName' => '{{$couponName}}',
        'couponCode' => '{{$couponCode}}',
        'couponDesc' => '{{$couponDesc}}',
        'couponDiscountDesc' => '{{$couponDiscountDesc}}',
        'couponEffectiveEndAt' => '{{$couponEffectiveEndAt}}',
        // 商品信息
        'productName' => '{{$productName}}',
        'productImageSrc' => '{{$productImageSrc}}',
        'productPrice' => '{{$productPrice}}',
        // 用户信息
        'userName' => '{{$userName}}',
        // 订单信息
        'orderNo' => '{{$orderNo}}',
        'orderSubtotalPrice' => '{{$orderSubtotalPrice}}',
        'orderTotalPrice' => '{{$orderTotalPrice}}',
        'orderCreatedAt' => '{{$orderCreatedAt}}', //下单时间
        'shippingFee' => '{{$shippingFee}}', //运费
        'totalNum' => '{{$totalNum}}', //商品总数量
        'payMethod' => '{{$payMethod}}', //支付方式
        'orderPrice' => '{{$orderPrice}}', //商品总价格(除运费)
        'orderItems' => [
            [
                "name" => '{{$orderItem["name"]}}',
                "imageSrc" => '{{$orderItem["imageSrc"]}}',
                "originalPrice" => '{{$orderItem["originalPrice"]}}',
                "price" => '{{$orderItem["price"]}}',
                "num" => '{{$orderItem["num"]}}',
                "size" => '{{$orderItem["size"]}}',
                "color" => '{{$orderItem["color"]}}',
                "slugTitle" => '{{$orderItem["slugTitle"]}}',
            ]
        ],
        'subscribeList' => [
            [
                'name' => '{{$subscribeList["name"]}}',
                'imageSrc' => '{{$subscribeList["imageSrc"]}}',
                'linkSrc' => '{{$subscribeList["linkSrc"]}}',
            ]
        ],
        'billingAddress' => [
            'name' => '{{$billingAddress["name"]}}',
            'address' => '{{$billingAddress["address"]}}',
            'state' => '{{$billingAddress["state"]}}',
            'city' => '{{$billingAddress["city"]}}',
            'zip' => '{{$billingAddress["zip"]}}',
            'phone' => '{{$billingAddress["phone"]}}',
            'country' => '{{$billingAddress["country"]}}',
        ],
        'shippingAddress' => [
            'name' => '{{$shippingAddress["name"]}}',
            'address' => '{{$shippingAddress["address"]}}',
            'state' => '{{$shippingAddress["state"]}}',
            'city' => '{{$shippingAddress["city"]}}',
            'zip' => '{{$shippingAddress["zip"]}}',
            'phone' => '{{$shippingAddress["phone"]}}',
            'country' => '{{$shippingAddress["country"]}}',
        ],
        // 邀请用户链接
        'userInviteLink' => '{{$userInviteLink}}',
        // 邀请用户code
        'userInviteCode' => '{{$userInviteCode}}',
        // 重置密码链接
        'resetPasswordLink' => '{{$resetPasswordLink}}',
        // 订阅确认token变量
        'subscribeConfirmToken' => '{{$subscribeConfirmToken}}',
        //用户邮箱
        'email' => '{{$email}}',
        //用户分裂政策链接
        'inviteRule' => '{{$inviteRule}}',
        //邀请用户时填的message
        'inviteMessage' => '{{$inviteMessage}}',
        //验证码
        'email_code' => '{{$email_code}}',
        //时间
        'time' => '{{$time}}'

    ];
    //用户信息
    public function adminUser(): BelongsTo
    {
        return $this->belongsTo(AdminUser::class, 'admin_user_id');
    }
}
