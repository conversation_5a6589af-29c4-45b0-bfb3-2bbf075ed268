<?php

namespace App\Exceptions;

use App\Constants\ErrorCode;
use Exception;
use Illuminate\Http\Response;

class DataException extends Exception implements BaseException
{

    public function __construct(string $message = "", public ErrorCode $errorCode = ErrorCode::DataError, $previous = null, int $code = 0)
    {
        parent::__construct($message, $code, $previous);
    }

    public function getErrorCode(): int
    {
        return $this->errorCode->value;
    }
}
