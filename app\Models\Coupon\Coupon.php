<?php

namespace App\Models\Coupon;

use App\Models\Category;
use App\Models\Enums\Discount\DiscountAmountTypeEnum;
use App\Models\Enums\Discount\DiscountEffectiveDateTypeEnum;
use App\Models\Enums\Discount\DiscountPriceTypeEnum;
use App\Models\Enums\Discount\DiscountTypeEnum;
use App\Models\Product\Product;
use App\Models\User\UserCoupon;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\MiddleModel;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Arr;

/**
 * @property DiscountPriceTypeEnum $price_type
 * @property DiscountTypeEnum $type
 * @property array $rules
 * @property int $effective_days
 * @property int $id
 * @property int $total_count
 * @property string $name
 * @property Carbon $effective_start_at
 * @property Carbon $effective_end_at
 * @property bool $enabled
 */
class Coupon extends MiddleModel
{
    use HasFactory;
    use SoftDeletes;

    protected $guarded = [];
    protected $appends = [
        'product_ids',
        'expire_at'
    ];

    public $casts = [
        'enabled' => 'bool',
        'effective_days' => 'int',
        'total_count' => 'int',
        'user_count' => 'int',
        'price_type' => DiscountPriceTypeEnum::class,
        'type' => DiscountTypeEnum::class,
        'rules' => 'json',
        'is_global' => 'bool',
        'is_auto' => 'bool',
        'effective_start_at' => 'datetime',
        'effective_end_at' => 'datetime',
    ];

    // 适用商品
    public function products(): MorphToMany
    {
        return $this->morphedByMany(Product::class, 'target', CouponTarget::class);
    }

    // 适用属性
    public function categories(): MorphToMany
    {
        return $this->morphedByMany(Category::class, 'target', CouponTarget::class);
    }

    public function userCoupons(): HasMany
    {
        return $this->hasMany(UserCoupon::class);
    }


    public function productIds(): Attribute
    {
        return Attribute::make(
            get: function () {
                if ($this->relationLoaded('products')) {
                    return $this->products->pluck('id')->toArray();
                }
                return [];
            },
            set: function ($values) {
                $this->products()->sync($values);
            }
        );

    }

    public function expireAt(): Attribute
    {
        return Attribute::get(function () {
            if ($this->effective_date_type == DiscountEffectiveDateTypeEnum::Cycle->value) {
                return $this->effective_end_at;
            } else {
                return Carbon::now()->addDays($this->effective_days);
            }
        });
    }

    public function discountDesc(): Attribute
    {
        return Attribute::get(function () {
            $rule = array_pop($this->rules);
            $type = Arr::get($rule, 'discount_amount_type');
            // 满减运费
            if (is_null($type)) {
                $shippingFee = Arr::get($rule, 'shipping_fee');
                return "{$shippingFee} Shipping discount";
            }
            if ($type == DiscountAmountTypeEnum::FixedAmount) {
                $price = Arr::get($rule, 'discount_price');
                return "{$price} FREE";
            }
            if ($type == DiscountAmountTypeEnum::PercentAmount) {
                $rate = Arr::get($rule, 'discount_rate');
                return "{$rate}% OFF";
            }
        });
    }
}
