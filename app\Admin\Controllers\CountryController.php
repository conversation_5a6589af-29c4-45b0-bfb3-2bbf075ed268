<?php

namespace App\Admin\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Country;
use App\Models\CountryCurrency;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\JsonResponse;
use App\Constants\Permissions;
use App\Exceptions\DataException;
use Spatie\QueryBuilder\QueryBuilder;
use Spatie\QueryBuilder\AllowedFilter;
use Illuminate\Support\Arr;
use Illuminate\Validation\Rules\Exists;


class CountryController extends Controller
{
    public function __construct()
    {
        $this->hasPermissionOr(Permissions::CmsSettingsUpdate)->except(['index']);
        $this->hasPermissionOr(Permissions::CmsSettingsIndex, Permissions::CmsSettingsUpdate)->only(['index']);
    }

    // 列表
    public function index(Request $request): JsonResource
    {
        $query = QueryBuilder::for(Country::class)
            ->allowedFilters([
                AllowedFilter::exact('active'),
                AllowedFilter::partial('name'),
                AllowedFilter::partial('iso_code'),
            ]);

        if ($request->boolean('all', false)) {
            return JsonResource::collection($query->get());
        }

        return JsonResource::collection($query->paginate($this->getPerPage()));
    }

    // 获取国家货币列表
    public function currencies(Request $request): JsonResource
    {
        $query = QueryBuilder::for(CountryCurrency::class)
            ->with(['image:id,path,disk,module', 'country:id,name,iso_code', 'currency:id,currency_code,symbol'])
            ->allowedFilters([
                AllowedFilter::exact('active'),
                AllowedFilter::partial('currency_code'),
                AllowedFilter::partial('country_code'),
            ])
            ->allowedSorts(['country_code', 'currency_code'])
            ->defaultSort('country_code');

        if ($request->boolean('all', false)) {
            return JsonResource::collection($query->get());
        }

        return JsonResource::collection($query->paginate($this->getPerPage()));
    }

    // 设置国家货币
    public function setCurrency(Request $request): JsonResource
    {
        $validated = $request->validate([
            'country_code' => ['required', 'string', 'max:10', 'exists:countries,iso_code'],
            'currency_code' => ['required', 'string', 'max:10', 'exists:currencies,currency_code'],
            'image_id' => ['nullable', 'integer'],
            'active' => ['required', 'boolean'],
        ]);

        // 检查是否存在未软删除的记录
        $exists = CountryCurrency::where('country_code', $validated['country_code'])->exists();
        if ($exists) {
            throw new DataException('该国家已设置货币');
        }

        // 创建新记录
        $countryCurrency = CountryCurrency::create(
            array_merge($validated, ['country_code' => $validated['country_code']])
        );

        return JsonResource::make($countryCurrency);
    }

    // 删除国家货币
    public function destroy(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ids' => ['array', 'required'],
            'ids.*' => [(new Exists(CountryCurrency::class, 'id'))],
        ]);
        $ids = Arr::get($validated, 'ids');
        try {
            CountryCurrency::query()->whereIn('id', $ids)->update([
                'deleted_at' => now()
            ]);

        } catch (\Throwable $throwable) {
            throw new DataException($throwable->getMessage());
        }
        return response()->json(['message' => '删除成功']);
    }

    // 修改国家货币状态
    public function currencyStatus(Request $request, Country $country): JsonResponse
    {
        $validated = $request->validate([
            'active' => ['required', 'boolean'],
        ]);

        CountryCurrency::where('country_code', $country->iso_code)
            ->update($validated);

        return response()->json();
    }

} 