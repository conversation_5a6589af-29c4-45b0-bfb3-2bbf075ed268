<?php

namespace App\Services;

use App\Apis\Airwallex\AirwallexService;
use App\Apis\Paypal\PaypalService;
use App\Constants\QueueKey;
use App\Exceptions\DataException;
use App\Jobs\EmailRuleNotice;
use App\Jobs\Order\OrderPaid;
use App\Models\Cart;
use App\Models\Enums\EmailRuleEventEnum;
use App\Models\Enums\Order\OrderStatusEnum;
use App\Models\Order\Order;
use App\Models\Order\Payment\AirwallexOrder;
use App\Models\Order\FastPaypalOrder;
use App\Models\Order\Payment\PaypalOrder;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class PaymentService
{
    /**
     * 空中云汇卡支付
     * @param \App\Models\Order\Order $order
     * @param \Illuminate\Http\Request $request
     * @throws \App\Exceptions\DataException
     * @return array|mixed
     */
    public function payByAirwallexBank(Order $order, Request $request)
    {
        $cardInfo = $request->validate([
            'card' => 'array|required',
            'card.cvc' => 'required',
            'card.expiry_month' => 'required',
            'card.expiry_year' => 'required',
            'card.number' => 'required',
        ]);
        // 创建订单
        $createResponseData = AirwallexService::paymentIntentsCreate($order);
        if (!$id = Arr::get($createResponseData, 'id')) {
            throw new DataException('Failed to create payment order.');
        }
        // 创建信用卡付款
        $cardPaymentData = AirwallexService::formatCardPaymentData($order, $cardInfo['card'], $id);
        $paymentData = AirwallexService::paymentIntentsConfirm($id, $cardPaymentData);
        //3DS
        if (Arr::get($paymentData, 'status') == 'REQUIRES_CUSTOMER_ACTION') {
            return Arr::only($paymentData, ['next_action', 'status']);
        }

        // 调用成功
        if (Arr::get($paymentData, 'status') == 'SUCCEEDED') {
            // 付款订单
            $paymentOrder = new AirwallexOrder([
                'detail' => $paymentData,
                'intent_id' => $id,
            ]);
            $paymentOrder->save();
            // 更新交易号
            $order->update([
                'transaction_id' => $id,
            ]);
            // 支付绑定
            OrderPaid::dispatchSync($order, $paymentOrder);
        } else {
            // 付款订单
            $paymentOrder = new AirwallexOrder([
                'detail' => $paymentData,
                'intent_id' => $id,
            ]);
            $paymentOrder->save();
            $order->payment()->associate($paymentOrder)->save();
            //获取错误信息
            $error_msg = Arr::get($paymentData, 'message');
            // 检查是否无法正常解析（不是数组或没有message字段）
            if (!is_array($paymentData) || !Arr::has($paymentData, 'message')) {
                // 如果是字符串，直接使用；如果是其他类型，转换为字符串
                $error_msg = is_string($paymentData) ? $paymentData : json_encode($paymentData);
            }
            $order->update([
                'status' => OrderStatusEnum::PaidFail,
                'remark' => $error_msg,
            ]);
            $user = auth()->user();
            EmailRuleNotice::dispatch(EmailRuleEventEnum::OrderPaidFail, user: $user, order: $order)->onQueue(QueueKey::Default->value);
        }

        return $paymentData;
    }

    /**
     * 快闪支付生成PayPal订单
     * @param \App\Models\Order\FastPaypalOrder $fastPaypalOrder
     */
    public function fastPayByPaypal(FastPaypalOrder $fastPaypalOrder)
    {
        // 没有才生成新的, 这边的价格用的是cart的货币代理价格
        if (!$fastPaypalOrder->paypalOrder) {
            $data = PaypalService::paymentIntentsCreateForFastPaypalOrder($fastPaypalOrder);
            // 新增
            $paypalOrder = new PaypalOrder();
            $paypalOrder->fill(['paypal_order_id' => $data['id']])->save();
            // 关联
            $fastPaypalOrder->paypalOrder()->associate($paypalOrder)->save();
            $fastPaypalOrder->load('paypalOrder');
        }
        return $fastPaypalOrder->paypalOrder;
    }

    /**
     * paypal支付
     * @param Order $order
     * @return PaypalOrder|null
     * @throws \Throwable
     */
    public function payByPaypal(Order $order): ?PaypalOrder
    {
        // 没有才生成新的, 使用的价格使用的是order的货币代理价格
        if (!$order->paypalOrder) {
            $data = PaypalService::paymentIntentsCreate($order);
            $order->paypalOrder()->updateOrCreate(['paypal_order_id' => $data['id']]);
            $order->load('paypalOrder');
        }
        return $order->paypalOrder;
    }
    /**
     * 创建PayPal订单
     * @param Cart $cart
     * @return array PayPal订单数据
     */
    public function createPayPalOrder(Cart $cart): array
    {
        $data = PaypalService::paymentIntentsCreateForCart($cart);
        // 检查是否已存在PaypalOrder记录
        $existingOrder = PaypalOrder::where([
            'cart_id' => $cart->id,
            'order_id' => null
        ])->first();
        if ($existingOrder) {
            // 如果存在，则更新记录
            $existingOrder->update(['paypal_order_id' => $data['id']]);
            $paypalOrder = $existingOrder;
        } else {
            // 如果不存在，则创建新记录
            $paypalOrder = new PaypalOrder();
            $paypalOrder->fill(['paypal_order_id' => $data['id'], 'cart_id' => $cart->id])->save();
        }
        return [
            'id' => $paypalOrder->id,
            'paypal_order_id' => $paypalOrder->paypal_order_id,
            'status' => $data['status']
        ];
    }
}
