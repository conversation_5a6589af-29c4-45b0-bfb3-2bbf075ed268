<?php

namespace App\Admin\Controllers;

use App\Constants\Permissions;
use App\Exceptions\DataException;
use App\Http\Controllers\Controller;
use App\Models\Coupon\Coupon;
use App\Models\CouponGrantRule;
use App\Models\Email\EmailTemplate;
use App\Models\Enums\CouponGrantType;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules\Enum;
use Illuminate\Validation\Rules\Exists;
use Illuminate\Validation\ValidationException;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

class CouponGrantRuleController extends Controller
{
    public function __construct()
    {
        $this->hasPermissionOr(Permissions::CouponsUpdate)->only(['store', 'patchUpdate', 'destroy', 'update',]);
        $this->hasPermissionOr(Permissions::CouponsUpdate, Permissions::CouponsIndex)->only(['index', 'show']);
    }

    /**
     * 新增优惠券发放规则
     * @param Request $request
     * @param CouponGrantRule $rule
     * @return JsonResource
     * @throws ValidationException
     */
    public function store(Request $request, CouponGrantRule $rule): JsonResource
    {
        $validated = $request->validate([
            'name' => 'required|string',
            'enabled' => 'required|boolean',
            'desc' => 'required|string',
            'type' => ['required', new Enum(CouponGrantType::class)],
            'config' => ['nullable', 'array'],
            'effective_start_at' => 'required|date',
            'effective_end_at' => 'required|date',
            'send_email' => ['required', 'bool'],
            'email_template_id' => ['nullable', new Exists(EmailTemplate::class, 'id')],
            'total_count' => ['nullable', 'integer'],
            'url' => ['nullable', 'url'],
        ]);
        // 如果 type 不是 6，则 coupon_id 必填
        if ($validated['type'] != CouponGrantType::RandomGrant->value) {
            $request->validate([
                'coupon_id' => ['required', new Exists(Coupon::class, 'id')],
            ]);
        }
        $type = CouponGrantType::tryFrom(Arr::get($validated, 'type'));
        $validator = Validator::make($validated['config'], $type->validateRules());
        if ($validator->fails()) {
            throw new ValidationException($validator);
        }
        // 如果限定了总数量, 需要控制发放
        if (isset($validated['coupon_id']) && $validated['coupon_id']) {
            $coupon = Coupon::query()->find($validated['coupon_id']);
            if ($coupon->total_count) {
                $totalCount = $coupon->total_count - CouponGrantRule::query()->where('coupon_id', $validated['coupon_id'])->sum('total_count');
                if ($totalCount < $validated['total_count']) {
                    throw new DataException("当前优惠券可用发放额度最高{$totalCount}张");
                }
            }
        }
        $rule->fill($validated)->save();
        return JsonResource::make($rule);
    }

    /**
     * 优惠券发放规则列表
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        /**
         * @var $user User|null
         */
        $user = Auth::user();
        $builder = QueryBuilder::for(CouponGrantRule::class)
            ->allowedFilters([
                AllowedFilter::exact('type'),
                AllowedFilter::exact('enabled'),
            ]);
        return JsonResource::collection($builder->paginate($this->getPerPage()));
    }

    public function show(CouponGrantRule $rule): JsonResource
    {
        $rule->loadMissing([
            'coupon'
        ]);
        return JsonResource::make($rule);
    }

    public function destroy(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ids' => ['array', 'required'],
            'ids.*' => [(new Exists(CouponGrantRule::class, 'id'))],
        ]);
        $ids = Arr::get($validated, 'ids');
        // 删除
        CouponGrantRule::query()->whereIn('id', $ids)
            ->delete();
        return response()->json();
    }

    /**
     * 修改优惠券发放规则
     * @param Request $request
     * @param CouponGrantRule $rule
     * @return JsonResource
     */
    public function update(Request $request, CouponGrantRule $rule): JsonResource
    {
        $update = $request->validate([
            'name' => 'required|string',
            'enabled' => 'required|boolean',
            'desc' => 'required|string',
            'config' => ['nullable', 'array'],
            'effective_start_at' => 'required|date',
            'effective_end_at' => 'required|date',
            'send_email' => ['required', 'bool'],
            'email_template_id' => ['nullable', new Exists(EmailTemplate::class, 'id')],
            'url' => ['nullable', 'url'],
            'type' => ['required', new Enum(CouponGrantType::class)],
            'total_count' => ['nullable', 'integer'],
        ]);

        //启用中的规则不允许修改
        if ($rule->enabled) {
            throw new DataException('The rules in use do not allow modification');
        }
        // 如果 type 不是 6，则 coupon_id 必填
        if ($update['type'] != CouponGrantType::RandomGrant->value) {
            $request->validate([
                'coupon_id' => ['required', new Exists(Coupon::class, 'id')],
            ]);
        }

        if (empty($update['total_count'])) {
            $update['total_count'] = null;
        }
        $type = CouponGrantType::tryFrom(Arr::get($update, 'type'));
        $validator = Validator::make($update['config'], $type->validateRules());
        if ($validator->fails()) {
            throw new ValidationException($validator);
        }
        $update && $rule->update($update);
        return JsonResource::make($rule);
    }

    /**
     * 修改部分
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\CouponGrantRule $rule
     * @return JsonResource
     */
    public function patchUpdate(Request $request, CouponGrantRule $rule): JsonResource
    {
        $update = $request->validate([
            'enabled' => 'required|boolean',
        ]);
        $update && $rule->update($update);
        return JsonResource::make($rule);
    }
}
