<?php

namespace App\Models\Enums\Broadcast;

use Illuminate\Support\Arr;

enum BroadcastColumnContentTypeEnum: int
{
    case  ImageType = 1;
    case  ContentType = 2;
    case  HyperLink = 3;
    public function desc(): string
    {
        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::ImageType->value => 'image type',
            self::ContentType->value => 'content type',
            self::HyperLink->value => 'hyperlink',
        ];
    }

}
