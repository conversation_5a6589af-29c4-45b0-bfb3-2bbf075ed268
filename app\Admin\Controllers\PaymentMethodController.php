<?php

namespace App\Admin\Controllers;

use App\Constants\Permissions;
use App\Http\Controllers\Controller;
use App\Models\Enums\Order\Payment\PaymentMethodTypeEnum;
use App\Models\Order\Order;
use App\Models\Order\Payment\PaymentMethod;
use Arr;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules\Enum;
use Illuminate\Validation\Rules\Exists;
use Illuminate\Validation\ValidationException;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

class PaymentMethodController extends Controller
{

    public function __construct()
    {
        $this->hasPermissionOr(Permissions::PaymentMethodsUpdate)->only(['update']);
        $this->hasPermissionOr(Permissions::PaymentMethodsUpdate, Permissions::PaymentMethodsIndex)->only(['index', 'show', 'stat']);
    }

    /**
     * 订单列表
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $builder = QueryBuilder::for(PaymentMethod::class)
            ->allowedFilters([
            AllowedFilter::partial('name')
            ])
            ->allowedSorts(['id', 'created_at'])
            ->defaultSort('-id');

        return JsonResource::collection($builder->paginate($this->getPerPage()));
    }

    public function show(PaymentMethod $method): JsonResource
    {
        return JsonResource::make($method);
    }

    /**
     * 修改
     * @param Request $request
     * @param Order $order
     * @return JsonResource
     */
    public function update(Request $request, PaymentMethod $method): JsonResource
    {
        $validated = $request->validate([
            'name' => ['required', 'string'],
            'status' => ['required', 'bool'],
            'type' => ['required', new Enum(PaymentMethodTypeEnum::class)],
            'config' => ['array', 'required'],
        ]);
        $typeValue = Arr::get($validated, 'type');
        $type = PaymentMethodTypeEnum::tryFrom($typeValue);

        $rules = $type->validateRules();
        $validator = Validator::make($validated['config'], $rules);
        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        $method->update($validated);
        return JsonResource::make($method);
    }

    /**
     * 新增
     * @param Request $request
     * @param Order $order
     * @return JsonResource
     */
    public function store(Request $request, PaymentMethod $method): JsonResource
    {
        $validated = $request->validate([
            'name' => ['required'],
            'type' => ['required', new Enum(PaymentMethodTypeEnum::class)],
            'status' => ['required', 'bool'],
            'config' => ['array', 'required'],
        ]);
        $typeValue = Arr::get($validated, 'type');
        $type = PaymentMethodTypeEnum::tryFrom($typeValue);

        $rules = $type->validateRules();
        $validator = Validator::make($validated['config'], $rules);
        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        $method->fill($validated)->save();
        return JsonResource::make($method);
    }

    public function putchUpdate(Request $request, PaymentMethod $method): JsonResource
    {
        $validated = $request->validate([
            'status' => ['bool'],
        ]);
        $method->update($validated);
        return JsonResource::make($method);
    }

    public function destroy(Request $request)
    {
        $validated = $request->validate([
            'ids' => ['array', 'required'],
            'ids.*' => [(new Exists(PaymentMethod::class, 'id'))],
        ]);
        $ids = Arr::get($validated, 'ids');
        DB::beginTransaction();
        try {
            PaymentMethod::query()->whereIn('id', $ids)->delete();
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
        return response()->json();
    }
}
