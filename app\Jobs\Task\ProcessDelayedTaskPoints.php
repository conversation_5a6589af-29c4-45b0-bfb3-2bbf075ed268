<?php

namespace App\Jobs\Task;

use App\Constants\QueueKey;
use App\Models\Task\UserTaskRecord;
use App\Models\Enums\User\WalletChangeTypeEnum;
use App\Services\UserWalletService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class ProcessDelayedTaskPoints implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 处理延迟发放的任务积分
     */
    public function __construct(public UserTaskRecord $taskRecord)
    {
        $this->onQueue(QueueKey::Order->value);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            // 检查任务记录是否已经发放积分
            if ($this->taskRecord->points_distributed) {
                Log::info('Task points already distributed', [
                    'task_record_id' => $this->taskRecord->id,
                    'user_id' => $this->taskRecord->user_id,
                ]);
                return;
            }

            // 检查用户是否存在
            $user = $this->taskRecord->user;
            if (!$user) {
                Log::error('User not found for task record', [
                    'task_record_id' => $this->taskRecord->id,
                    'user_id' => $this->taskRecord->user_id,
                ]);
                return;
            }

            DB::beginTransaction();
            try {
                // 发放积分到用户钱包
                userWalletService()->walletChangePoint(
                    $user->wallet,
                    WalletChangeTypeEnum::TaskReward,
                    $this->taskRecord->earned_points,
                    "任务积分奖励 - {$this->taskRecord->taskConfig->task_name}",
                    $this->taskRecord
                );

                // 标记积分已发放
                $this->taskRecord->update([
                    'points_distributed' => true,
                    'points_distributed_at' => now(),
                ]);

                DB::commit();

                Log::info('Delayed task points distributed successfully', [
                    'task_record_id' => $this->taskRecord->id,
                    'user_id' => $this->taskRecord->user_id,
                    'earned_points' => $this->taskRecord->earned_points,
                    'task_type' => $this->taskRecord->task_type,
                    'channel' => $this->taskRecord->channel,
                ]);

            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }

        } catch (\Exception $e) {
            Log::error('Failed to process delayed task points', [
                'task_record_id' => $this->taskRecord->id,
                'user_id' => $this->taskRecord->user_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // 重新抛出异常以便队列系统处理重试
            throw $e;
        }
    }

    /**
     * 处理失败的任务
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('ProcessDelayedTaskPoints job failed permanently', [
            'task_record_id' => $this->taskRecord->id,
            'user_id' => $this->taskRecord->user_id,
            'error' => $exception->getMessage(),
        ]);
    }
}
