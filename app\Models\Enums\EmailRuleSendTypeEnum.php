<?php

namespace App\Models\Enums;

use Illuminate\Support\Arr;

enum EmailRuleSendTypeEnum: int

{
    case Operation = 1;
    case AutoEvent = 2;


    public function desc(): string
    {
        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::Operation->value => '手动',
            self::AutoEvent->value => '自动订阅',
        ];
    }

}
