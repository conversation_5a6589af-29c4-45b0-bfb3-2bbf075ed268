<?php

namespace App\Models\Enums\User;

use Illuminate\Support\Arr;

enum WalletChangeTypeEnum: string
{
    case  RechargeBackend = 'recharge_backend';
    case  OrderCompleted = 'order_completed';
    case  Consume = 'consume';
    case  Withdraw = 'withdraw';

    public function desc(): string
    {
        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::RechargeBackend->value => 'Backend recharge',
            self::OrderCompleted->value => 'Order completion rewards',
            self::Consume->value => 'Points consumption',
            self::Withdraw->value => 'Withdraw',
        ];
    }
}
