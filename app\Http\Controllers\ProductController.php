<?php

namespace App\Http\Controllers;


use App\Constants\CacheKey;
use App\Constants\ErrorCode;
use App\Exceptions\DataException;
use App\Http\Resources\ProductListResource;
use App\Models\Attachment;
use App\Models\Attribute;
use App\Models\AttributeValue;
use App\Models\Collection;
use App\Models\Comment;
use App\Models\Enums\Comment\CommentStatusEnum;
use App\Models\Enums\SysConfigKeyEnum;
use App\Models\Enums\User\UserRegisterTypesEnum;
use App\Models\Material;
use App\Models\Product\Product;
use App\Models\Product\ProductVariant;
use App\Models\Style;
use App\Models\User\User;
use App\Models\User\UserProductCollect;
use App\Models\UserExperienceActivity;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rules\Exists;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\AllowedInclude;
use Spatie\QueryBuilder\QueryBuilder;
use App\Models\ActivityProduct;
use App\Models\CmsArticle;

class ProductController extends Controller
{
    //

    /**
     * 商品列表
     * @param Request $request
     * @return mixed
     */
    public function index(Request $request): mixed
    {
        /**
         * @var $user User|null
         */
        $user = Auth::user();
        $currency = currentCurrency()->currency_code; // 获取当前货币代码
        $uniqueKey = md5(json_encode([$currency, $request->all()]));
        $content = Cache::remember(CacheKey::ProductSearchList->getKey($uniqueKey), now()->addWeek(), function () use ($request) {
            $query = productService()->getIndexQueryBuilder($request);
            // 计算出有多少颜色值
            $allProductId = $query->clone()->select(['id'])->pluck('id');
            // 最大价格
            $maxPrice = ProductVariant::query()
                ->whereHas('product', function ($query) use ($allProductId) {
                    $query->whereIn('id', $allProductId);
                })
                ->max('price');
            return ProductListResource::collection($query->paginate($this->getPerPage()))
                ->additional([
                    'max_price' => round($maxPrice, 2),
                    'max_price_agent' => convertPrice($maxPrice, currentCurrency()),
                ])
                ->toResponse($request)
                ->getContent();
        });
        // 优化记录↓↓↓↓
        // 1.如果缓存 response非content字符串 慢100ms， 所以直接缓存content;
        // 2.如果对content直接return $content; 能达到最高速度，但是结果非jsonResponse 且没有收藏信息 150ms
        // 3.使用 JsonResponseCode 继续追加 code和data // 200ms ， 如需再优化，去掉 JsonResponseCode中间件 快十几ms，感觉无
        // 由用户存在需要加载收藏关系
        if ($user) {
            $list = json_decode($content, true);
            $productIds = array_column($list['data'], 'id');
            $collectIds = UserProductCollect::query()->whereIn('product_id', $productIds)
                ->where('user_id', $user->id)
                ->pluck('product_id')
                ->toArray();
            foreach ($list['data'] as &$row) {
                $row['collected'] = in_array($row['id'], $collectIds);
            }
            return response()->json($list);
        } else {
            return response($content, 200, ['content-type' => 'application/json']);
        }
    }

    //用户体验计划产品列表
    public function activity(Request $request): AnonymousResourceCollection
    {
        $validated = $request->validate([
            'session_uuid' => 'nullable|uuid'
        ]);
        $user = Auth::user();
        $session_uuid = Arr::get($validated, 'session_uuid');
        //查询当前是否有在线活动
        $activity = UserExperienceActivity::where('is_listed', true)
            ->where('start_at', '<=', now())
            ->where('end_at', '>=', now())
            ->first();
        $productList = [];
        $maxPrice = 0;
        if (!empty($activity)) {
            $productIds = $activity->activityProducts()->take(12)->pluck('product_id')->toArray();
            $query = productService()->getIndexQueryBuilder(
                $request->merge(['filter' => array_merge([], ['productIds' => implode(',', $productIds)])]),
                $user
            );
            // 计算出有多少颜色值
            $allProductId = $query->clone()->select(['id'])->pluck('id');
            // 最大价格
            $maxPrice = ProductVariant::query()
                ->whereHas('product', function ($query) use ($allProductId) {
                    $query->whereIn('id', $allProductId);
                })
                ->max('price');
            $productList = $query->paginate($this->getPerPage());
            //将变体的价格改成活动价格  (查看用户购物车或者订单是否有该产品)
            $productList = $productList->map(function ($product) use ($user, $session_uuid) {
                if ($user) {
                    $price = activityService()->isProductOnSale($product->id, $user);
                } else {
                    $price = activityService()->getSessionUuidProductPrice($product->id, $session_uuid);
                }

                $product->is_activity = false;
                if ($price) {
                    $product->min_price = $price['price'];
                    $product->is_activity = true;
                    //修改变体价格
                    foreach ($product->variants as $variant) {
                        $variant->price = $price['price'];
                    }
                }
                return $product;
            });
        }
        return ProductListResource::collection($productList)
            ->additional([
                'max_price' => round($maxPrice, 2),
                'start_at' => $activity->start_at ?? NOW(),
                'end_at' => $activity->end_at ?? NOW(),
                'is_register' => $activity->is_register ?? false,
            ]);
    }

    /**
     * 关键词提取
     * @param Request $request
     * @return JsonResource
     */
    public function suggestions(Request $request): JsonResource
    {
        $keyword = trim($request->get('keyword'));
        $keywordList = Cache::remember(CacheKey::ProductSearchSuggestions->getKey($keyword), now()->addDay(), function () use ($keyword) {
            $total = 10;
            // 先获取集合
            $keywordList = Collection::query()->where(function (Builder $builder) use ($keyword) {
                $builder->where('title', 'like', '%' . $keyword . '%');
            })->limit(10)->pluck('title')->toArray();
            $total -= count($keywordList);
            if ($total > 0) {
                // 获取款式
                $styles = Style::query()
                    ->where('name', 'like', '%' . $keyword . '%')
                    ->limit($total)
                    ->pluck('name');
                if ($styles->count()) {
                    $total -= count($styles);
                    $keywordList = [...$keywordList, ...$styles->toArray()];
                }
            }
            if ($total > 0) {
                // 获取材质
                $materials = Material::query()
                    ->where('name', 'like', '%' . $keyword . '%')
                    ->limit($total)
                    ->pluck('name');
                if ($materials->count()) {
                    $total -= count($materials);
                    $keywordList = [...$keywordList, ...$materials->toArray()];
                }
            }
            if ($total > 0) {
                // 颜色尺寸
                $attributes = AttributeValue::query()
                    ->where('value', 'like', '%' . $keyword . '%')
                    ->limit($total)
                    ->pluck('value');
                if ($attributes->count()) {
                    $keywordList = [...$keywordList, ...$attributes->toArray()];
                }
            }
            return $keywordList;
        });

        return JsonResource::make($keywordList);
    }


    /**
     * 收藏的商品列表
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function collectProducts(Request $request): AnonymousResourceCollection
    {
        /**
         * @var $user User|null
         */
        $user = Auth::user();
        $productTable = (new Product())->getTable();
        $builder = QueryBuilder::for($user->collectProducts(), $request)
            ->select([
                "{$productTable}.id",
                "{$productTable}.title",
                "{$productTable}.spu",
                "{$productTable}.slug_title",
                "{$productTable}.image_id",
                "{$productTable}.is_new",
                "{$productTable}.origin_price",
                "{$productTable}.max_price",
                "{$productTable}.min_price",
                DB::raw('1 as collected')
            ])
            ->where('is_publish', true)
            ->with([
                'image:id,path,disk,module',
                'variants' => function ($query) {
                    $query->select(['id', 'product_id', 'price', 'image_id', 'original_price', 'color_attribute_value_id', 'size_attribute_value_id', 'stock'])
                        ->where('is_publish', true)
                        ->orderBy('sort', 'desc');
                },
                'variants.colorAttribute',
                'variants.sizeAttribute',
                'colors.images',
                'colors.color',
                'variants.image:id,path,disk,module',
                'variants.images'
            ])->when($user, function (Builder $query, User $user) {
                // 是否收藏
                $query->withExists([
                    'collectUserIds' => function ($builder) use ($user) {
                        $builder->where('user_id', $user->id);
                    }
                ]);
            })
            ->whereHas('collectUserIds', function (Builder $builder) use ($user) {
                $builder->where('user_id', $user->id);
            })
            ->defaultSort('-pivot_collect_at');
        return ProductListResource::collection($builder->paginate($this->getPerPage()));
    }

    /**
     * 删除所有收藏
     * @param Request $request
     * @return JsonResource
     */
    public function collectProductClear(Request $request): JsonResource
    {
        /**
         * @var $user User|null
         */
        $user = Auth::user();
        $user->collectProducts()->detach();
        return JsonResource::make(['clear' => true]);
    }

    /**
     * 收藏商品
     * @param Request $request
     * @param Product $product
     * @return JsonResource
     */
    public function collectProduct(Request $request, Product $product): JsonResource
    {
        /**
         * @var $user User|null
         */
        $user = Auth::user();
        $validated = $request->validate([
            'detach' => 'bool',
            'ids' => 'array|required',
        ]);
        $detach = Arr::get($validated, 'detach', false);
        if ($detach) {
            $res = $user->collectProducts()->detach($validated['ids']);
        } else {
            $res = $user->collectProducts()->syncWithPivotValues($validated['ids'], ['collect_at' => now()], false);
        }
        return JsonResource::make(['collect' => !!$res]);
    }

    /**
     * 收藏商品列表(未登录)
     * @param \Illuminate\Http\Request $request
     * @return AnonymousResourceCollection
     */
    public function collectProductByIds(Request $request): JsonResource
    {
        $validated = $request->validate([
            'ids' => 'array|required',
        ]);
        $ids = Arr::get($validated, 'ids');
        $builder = QueryBuilder::for(Product::class)
            ->whereIn('id', $ids)
            ->with([
                'image:id,path,disk,module',
                'variants' => function ($query) {
                    $query->select(['id', 'product_id', 'price', 'image_id', 'original_price', 'color_attribute_value_id', 'size_attribute_value_id', 'stock'])
                        ->where('is_publish', true)
                        ->orderBy('sort', 'desc');
                },
                'variants.colorAttribute',
                'variants.sizeAttribute',
                'variants.image:id,path,disk,module',
                'variants.images:id,path,disk,module',
            ]);
        return ProductListResource::collection($builder->paginate($this->getPerPage()));
    }

    /**
     * 相似产品
     * @param Request $request
     * @param Product $product
     * @return JsonResource
     * @throws DataException
     */
    public function match(Request $request, Product $product): JsonResource
    {
        // 三选一
        $validated = $request->validate([
            'spu' => 'string',
            'id' => 'numeric',
            'limit' => 'numeric',
            'slug_title' => 'string',
        ]);
        if (empty($validated)) {
            throw new DataException("params not exist.", ErrorCode::HttpNotFound);
        }
        $limit = Arr::get($validated, 'limit', 2);
        // 获取商品
        foreach (Arr::except($validated, ['limit']) as $key => $value) {
            $product = Product::query()
                ->where($key, $value)
                ->first();
            break;
        }
        // 缓存相似列表
        $productList = Cache::remember(CacheKey::ProductInfoMatchList->getKey($product->id), now()->addMinutes(30), function () use ($request, $product, $limit) {
            $param = [
                'filter' => [
                    'style_id' => $product->style_id,
                    'category_id' => $product->category_id,
                    'collection_ids' => $product->collectionIds()->pluck('collection_id')->toArray(),
                ]
            ];
            function getLikeProducts($request, $param, $limit, $productId): \Illuminate\Database\Eloquent\Collection|array
            {
                return productService()->getIndexQueryBuilder(clone $request->merge($param))
                    ->where('id', '!=', $productId)
                    ->limit($limit)
                    ->get();
            }

            // 获取相似产品
            $productList = getLikeProducts($request, $param, $limit, $product->id);
            // 去条件继续查
            if ($productList->count() < 2) {
                unset($param['filter']['style_id']);
                $productList = getLikeProducts($request, $param, $limit, $product->id);
            }
            // 去条件继续查
            if ($productList->count() < 2) {
                unset($param['filter']['category_id']);
                $productList = getLikeProducts($request, $param, $limit, $product->id);
            }
            // 去条件继续查
            if ($productList->count() < 2) {
                $param['filter']['collection_ids'] = array_filter([...$product->collections()->get()->pluck('parent_id')->toArray(), ...$param['filter']['collection_ids']]);
                $productList = getLikeProducts($request, $param, $limit, $product->id);
            }
            // 去条件继续查
            if ($productList->count() < 2) {
                unset($param['filter']['collection_ids']);
                $productList = getLikeProducts($request, $param, $limit, $product->id);
            }
            return $productList;
        });

        return ProductListResource::collection($productList);
    }

    public function show(Request $request, Product $product): JsonResource
    {
        // 三选一
        $validated = $request->validate([
            'spu' => 'string',
            'id' => 'numeric',
            'slug_title' => 'string',
            'session_uuid' => 'nullable|uuid',
            'from_activity' => 'nullable|bool',
        ]);

        //是否活动页
        $from_activity = Arr::get($validated, 'from_activity', false);
        Arr::forget($validated, 'from_activity');

        if (empty($validated)) {
            throw new DataException("params not exist.", ErrorCode::HttpNotFound);
        }
        $session_uuid = Arr::get($validated, 'session_uuid');
        $currencyCode = currentCurrency()->currency_code;
        $validated['currencyCode'] = $currencyCode;
        // 生成缓存键
        $uniqueKey = md5(json_encode(Arr::except($validated, ['session_uuid', 'from_activity'])));
        Arr::forget($validated, 'currencyCode');
        $product = Cache::remember(CacheKey::Productnfo->getKey($uniqueKey), now()->addHour(), function () use ($validated) {
            // 获取商品
            foreach ($validated as $key => $value) {
                $product = Product::query()
                    ->where($key, $value)
                    ->first();
                break;
            }
            if (!$product) {
                throw new DataException("The requested data does not exist.", ErrorCode::HttpNotFound);
            }

            if (!$product->is_publish && (!$product->publish_at || $product->publish_at > now())) {
                throw new DataException("The product has been taken down.");
            }

            $product->loadMissing([
                'image:id,path,disk,module',
                'variants' => function ($query) {
                    $query->where('is_publish', true)
                        ->orderBy('sort', 'desc'); // 根据 sort 字段降序排序
                },
                'variants.images:id,path,disk,module',
                'variants.image:id,path,disk,module',
                'variants.colorAttribute',
                'variants.sizeAttribute',
                'commentStatic',
                'collections',
                'colors.color:id,value,extra',
                'colors.images:id,path,disk,module'
            ]);
            return $product;
        });
        $user = Auth::user();
        // 用户收藏状态（不缓存，每次查询）
        if ($user) {
            $collected = $user->collectProducts()->where('product_id', $product->id)->exists();
            $product->collected = $collected;
        } else {
            $product->collected = false;
        }

        //查询是否有体验活动价
        $activityPrice = false;
        if ($from_activity) { //活动页访问才显示活动价
            if ($user) {
                $activityPrice = activityService()->isProductOnSale($product->id, $user);
            } else {
                $activityPrice = activityService()->getSessionUuidProductPrice($product->id, $session_uuid);
            }
        }

        $product->end_at = $product->start_at = false;

        if ($activityPrice) {
            $UserExperienceActivity = UserExperienceActivity::with(['activityProducts' => function ($query) use ($product) {
                $query->where('product_id', '=', $product->id);
            }])
                ->where('start_at', '<=', now())
                ->where('end_at', '>=', now())
                ->where('is_listed', true)
                ->first();

            if ($UserExperienceActivity) {
                $product->end_at = $UserExperienceActivity->end_at;
                $product->start_at = $UserExperienceActivity->start_at;
            }
            // 使用克隆避免修改缓存中的对象
            $variants = $product->variants->map(function ($variant) use ($activityPrice) {
                $clone = clone $variant;
                $clone->price = $activityPrice['price'];
                return $clone;
            });
            $product->setRelation('variants', $variants);
        }
        //添加特价标识
        $product->is_activity = $activityPrice && $from_activity ? true : false;
        // 拿出ids
        $product->collection_ids_arr = $product->collections->pluck('id')->toArray();

        return ProductListResource::make($product);
    }

    /**
     * 属性列表(分类页左侧筛选)
     * @return AnonymousResourceCollection
     */
    public function attributes(): AnonymousResourceCollection
    {
        $list = Cache::remember(CacheKey::ProductAttributes->getKey(), now()->addMinutes(30), function () {
            return QueryBuilder::for(Attribute::class)
                ->select(['id', 'name'])
                ->with(['values' => function ($query) {
                    $query->select('id', 'value', 'extra', 'sort', 'attribute_id', 'attachment_id')
                        ->orderBy('sort', 'asc');
                }, 'values.attachment:id,path,disk,module'])
                ->get();
        });

        return JsonResource::collection($list);
    }

    /**
     * 商品评论列表
     * @param \Illuminate\Http\Request $request
     * @return AnonymousResourceCollection
     */
    public function commentList(Request $request): AnonymousResourceCollection
    {
        /**
         * @var $user User|null
         */
        $config = sysConfigService()->get(SysConfigKeyEnum::CommentSortOrderConfig);

        // 构建基础查询
        $baseQuery = function () use ($request) {
            return QueryBuilder::for(Comment::class, $request)
                ->where('enabled', true)
                ->with(['images:id,file_type,path,module'])
                ->allowedIncludes([
                    AllowedInclude::relationship('images', 'images:id,file_type,path,module'),
                ])
                ->allowedFilters([
                    AllowedFilter::exact('product_id'),
                    AllowedFilter::exact('product_vagrant_id'),
                    AllowedFilter::callback('has_image', function (Builder $builder, $value) {
                        $value && $builder->whereHas('images', fn($q) => $q->select(['attachments.id']));
                    }),
                    AllowedFilter::callback('is_top', function (Builder $builder, $value) {}),
                ]);
        };
        $builder = $baseQuery();

        // 判断是否请求置顶评论
        $isTopRequest = $request->input('filter.is_top');
        if ($isTopRequest) {
            // 先查询置顶评论(top_at不为空的数据)
            $builder = $baseQuery()
                ->whereNotNull('top_at');

            // 如果置顶评论为空，则查询所有已审核的评论并按grade和created_at排序
            if ($builder->paginate($this->getPerPage())->isEmpty()) {
                $query = Comment::query()
                    ->where('enabled', true)
                    ->where('product_id', $request->input('filter.product_id'))
                    ->where('status', CommentStatusEnum::Approved)
                    ->with(['images:id,file_type,path,module'])
                    ->orderBy('grade', 'desc')
                    ->orderBy('created_at', 'desc');

                return JsonResource::collection($query->paginate($this->getPerPage()));
            }
        }

        // 解析默认排序规则
        $sorts = [];

        if (!empty($config['image']) && $config['image'] === true) {
            $sorts[] = '-has_images'; // 有图片的评论排前
        } else {
            $sorts[] = 'has_images'; // 无图片的评论排前
        }

        if (!empty($config['top']) && $config['top'] === true) {
            $sorts[] = '-top_at'; // 置顶评论排前
        }

        if (!empty($config['grade'])) {
            $sorts[] = ($config['grade'] === 'asc' ? 'grade' : '-grade');
        }

        if (!empty($config['created_at'])) {
            $sorts[] = ($config['created_at'] === 'asc' ? 'created_at' : '-created_at');
        }

        // 应用默认排序
        if (!empty($sorts)) {
            $builder->defaultSort($sorts);
        }
        // ->defaultSort(['-created_at', '-id']);

        return JsonResource::collection($builder->paginate($this->getPerPage()));
    }


    /**
     * 评价
     * @param Request $request
     * @param Product $product
     * @return JsonResource
     * @throws DataException
     * @throws \Throwable
     */
    public function storeComment(Request $request, Product $product): JsonResource
    {
        /**
         * @var $user User|null
         */
        $user = Auth::user();
        $validated = $request->validate([
            'first_name' => ['required', 'string', 'max:255'],
            'last_name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255'],
            'content' => ['required', 'string'],
            'grade' => ['required', 'numeric', 'between:0,100'],
            'product_variant_id' => ['nullable', new Exists(ProductVariant::class, 'id')],
            'images' => ['nullable', 'array'],
            'images.*' => ['required', new Exists(Attachment::class, 'id')],
        ]);
        try {
            DB::beginTransaction();
            // 这里处理的不够优雅,先这样随便烂写一下
            $isTop = Arr::pull($validated, 'is_top');
            if ($isTop) {
                $validated['top_at'] = now();
            }
            // 评论信息
            $comment = (new Comment());
            // 存在用户
            if ($user) {
                $comment->user()->associate($user);
            } else {
                $user = User::query()->where('email', $validated['email'])->first();
                if (!$user) {
                    // 订阅
                    $user = userService()->fastCreateUserByEmail($validated['email'], UserRegisterTypesEnum::Comment);
                }
                $comment->user()->associate($user);
            }
            $comment->fill(Arr::except($validated, 'images'))
                ->product()->associate($product)
                ->save();
            // 评论图片
            $images = Arr::get($validated, 'images', []);
            if (is_array($images)) {
                if (count($images) > 5) {
                    throw new DataException('The number of comment images cannot exceed 5');
                }
                $comment->images()->sync($images);
            }
            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }
        return JsonResource::make($comment);
    }


    /**
     * erp sync product status
     * @param Request $request
     * @return JsonResource
     */
    public function syncProductStatus(Request $request): JsonResource
    {
        $validated = $request->validate([
            'sku' => ['required', 'exists:products,spu'],
        ]);
        $sku = Arr::get($validated, 'sku');
        $product = Product::query()->where('spu', $sku)->first();
        $product->update([
            'is_publish' => false
        ]);
        return JsonResource::make([]);
    }


    public function sitemapData(Request $request): JsonResource
    {
        //获取所有在线商品的slug_title
        $products = QueryBuilder::for(Product::class)
            ->select(['id', 'slug_title', 'desc', 'image_id'])
            ->where('is_publish', 1)
            ->with(['image:id,path'])
            ->get();

        //获取所有的分类导航
        $categories = Collection::query()->where('active', true)->pluck('slug_title');
        //获取所有cms和blogs
        $pages = CmsArticle::query()->whereIn('type', [1, 3])->select('slug_title', 'type')->get();

        $data = [
            'products' => $products,
            'categories' => $categories,
            'pages' => $pages
        ];
        return JsonResource::make($data);
    }

    public function recommend(Product $product): JsonResource
    {
        $product->load([
            'productRecommends.recommendProduct.variants' => function ($query) {
                $query->where('is_publish', true)
                    ->orderBy('sort', 'desc');
            },
            'productRecommends.recommendProduct.variants.images:id,path,disk,module',
            'productRecommends.recommendProduct.variants.image:id,path,disk,module',
            'productRecommends.recommendProduct.variants.colorAttribute',
            'productRecommends.recommendProduct.variants.sizeAttribute',
        ]);
        //只返回推荐商品的信息
        $recommends = $product->productRecommends->map(function ($item) {
            return $item->recommendProduct;
        })->filter(); // 过滤掉不存在的推荐商品
        return JsonResource::collection($recommends);
    }
}
