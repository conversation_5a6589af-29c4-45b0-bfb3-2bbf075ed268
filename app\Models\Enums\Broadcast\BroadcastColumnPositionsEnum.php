<?php

namespace App\Models\Enums\Broadcast;

use Illuminate\Support\Arr;

enum BroadcastColumnPositionsEnum: int
{
    case  ToTheLeft = 1;
    case  Centered = 2;
    case  ToTheRight = 3;
    public function desc(): string
    {
        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::ToTheLeft->value => 'To the left',
            self::Centered->value => 'Centered',
            self::ToTheRight->value => 'To the right',
        ];
    }

}
