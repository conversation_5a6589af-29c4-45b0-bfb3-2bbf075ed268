<?php
/**
 * Author: raku <<EMAIL>>
 * Date: 2023/6/25
 */

namespace App\Apis\Google;

use App\Traits\SingletonTrait;
use Illuminate\Http\Client\Factory;
use Illuminate\Http\Client\PendingRequest;


class GoogleClient extends PendingRequest
{
    use SingletonTrait;

    protected $baseUrl = 'https://www.google.com';

    public function __construct(Factory $factory = null)
    {
        parent::__construct($factory);
        $this->timeout(30);
        $this->withoutVerifying();
        if ($proxy = config('services.socialite_proxy')) {
            if ($proxy['enabled']) {
                $this->withOptions(['proxy' => [
                    'http' => config('services.socialite_proxy.http'),
                    'https' => config('services.socialite_proxy.https'),
                ]]);
            }
        }
//        // 添加secret中间件
//        $this->withMiddleware(Middleware::mapRequest(function (RequestInterface $request) {
//
//            $param['secret'] = env('GOOGLE_CAPTCHA_BACKEND_KEY'); //
//            return $request->withUri($request->getUri()->withQuery(http_build_query($param)));
//        }));
    }

}
