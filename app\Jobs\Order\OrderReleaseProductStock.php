<?php

namespace App\Jobs\Order;

use App\Models\Enums\Order\OrderStatusEnum;
use App\Models\Order\Order;
use App\Models\Product\ProductVariant;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class OrderReleaseProductStock implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 释放库存
     */
    public function __construct(public Order $order)
    {
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // 恢复库存
        // if (in_array($this->order->status, [OrderStatusEnum::Refund, OrderStatusEnum::Cancel])) {
        //     $this->order->loadMissing(['items.product']);
        //     foreach ($this->order->items as $item) {
        //         if ($item->product instanceof ProductVariant) {
        //             $item->product->update([
        //                 'stock' => DB::raw('stock + ' . $item->num)
        //             ]);
        //         }
        //     }
        // }
    }


}
