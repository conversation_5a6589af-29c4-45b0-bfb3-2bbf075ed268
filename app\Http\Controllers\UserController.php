<?php

namespace App\Http\Controllers;

use App\Constants\QueueKey;
use App\Exceptions\DataException;
use App\Http\Resources\CouponFrameListResource;
use App\Http\Resources\UserInviteListResource;
use App\Jobs\EmailRuleNotice;
use App\Models\Attachment;
use App\Models\User\UserInviteReward;
use App\Models\Comment;
use App\Models\CouponGrantRule;
use App\Models\Enums\AttachmentModuleEnum;
use App\Models\Enums\CouponGrantType;
use App\Models\Enums\EmailRuleEventEnum;
use App\Models\Enums\Order\OrderValidEnum;
use App\Models\Enums\Reward\RewardTypeEnum;
use App\Models\Enums\SysConfigKeyEnum;
use App\Models\Enums\User\InviteAmountWithdrawStatusEnum;
use App\Models\Enums\User\UserEmailPreferencesEnum;
use App\Models\Enums\User\UserRegisterTypesEnum;
use App\Models\SharingRule;
use App\Models\SubscribeConfirm;
use App\Models\User\User;
use App\Services\CouponGrant\CouponGrantService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Illuminate\Validation\Rules\Enum;
use Illuminate\Validation\ValidationException;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;
use DB;

class UserController extends Controller
{
    /**
     * 订阅确认
     * @param Request $request
     * @return JsonResource
     */
    public function subscribeConfirm(Request $request): JsonResource
    {
        $validated = $request->validate(['token' => ['string', 'required']]);
        $subscribeConfirm = SubscribeConfirm::query()
            ->with(['user'])
            ->where('token', $validated['token'])
            ->first();
        if (!$subscribeConfirm instanceof SubscribeConfirm) {
            throw new DataException("Token error");
        }
        if (!$subscribeConfirm->confirmed) {
            $subscribeConfirm->user->update([
                'email_preferences' => UserEmailPreferencesEnum::FewTimesMonth
            ]);
            $subscribeConfirm->update(['confirmed' => true]);
        }
        return JsonResource::make([]);
    }

    /**
     * 订阅
     * @param Request $request
     * @return JsonResource
     */
    public function subscribe(Request $request): JsonResource
    {
        $validated = $request->validate(['email' => ['email', 'required']]);
        /**
         * @var $user User
         */
        $user = User::query()->where('email', $validated['email'])->first();
        if (!$user) {
            // 订阅
            $user = userService()->fastCreateUserByEmail($validated['email'], UserRegisterTypesEnum::Subscription);
        }
        $user->refresh();
        if ($user->email_preferences != UserEmailPreferencesEnum::None) {
            throw new DataException("This email is already subscribed.");
        }

        $user->update([
            'email_preferences' => UserEmailPreferencesEnum::FewTimesWeek
        ]);

        // 规则：用户订阅，发放优惠卷, 不订阅不发放优惠卷
        list($coupon_grant_rule, $coupon) = CouponGrantRule::grantCouponByRuleType(CouponGrantType::Subscribed, $user, true);
        return JsonResource::make([])->additional([
            'coupon_frames' => [[
                'coupon_grant_rule' => $coupon_grant_rule,
                'coupon' => $coupon
            ]]
        ]);
    }

    /**
     * 个人中心页的优惠券列表
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function userCoupons(Request $request): AnonymousResourceCollection
    {
        /**
         * @var $user User
         */
        $user = Auth::user();
        $builder = QueryBuilder::for($user->userCoupons()
            ->whereHas('coupon', function (Builder $query) {
                // 只获取未被删除的优惠券
                $query->whereNull('used_at');
            }))
            ->with([
                'coupon'
            ])
            ->allowedFilters([
                AllowedFilter::exact('code'),
                AllowedFilter::exact('type', 'coupon.type'),
                AllowedFilter::callback('can_used', function (Builder $builder, $value) {
                    if ($value) {
                        // 未过期并且未使用
                        $builder->where('effective_end_at', '>', now())
                            ->whereNull('used_at');
                    }
                })
            ]);
        // 设置已读
        if ($request->has('read')) {
            $user->userCoupons()->update([
                'is_read' => true,
                'is_framed' => true
            ]);
        }
        return JsonResource::collection($builder->paginate($this->getPerPage()));
    }

    /**
     * 用户非实时动效列表
     * @param \Illuminate\Http\Request $request
     * @return AnonymousResourceCollection
     */
    public function userCouponFrames(Request $request): AnonymousResourceCollection
    {
        $user = Auth::user();
        // 查询需要动效展示的优惠劵
        $res = QueryBuilder::for($user->userCoupons()
            ->whereHas('coupon', function (Builder $query) {
                // 只获取未被删除的优惠券
                $query->whereNull('deleted_at');
            }))
            ->with([
                'coupon',
                'couponGrantRule'
            ])
            ->where('is_framed', false)
            ->get();
        // 批量修改
        $res->each(function ($userCoupon) {
            $userCoupon->update([
                'is_framed' => true
            ]);
        });
        return CouponFrameListResource::collection($res);
    }

    /**
     * 用户评论
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function userComments(Request $request): AnonymousResourceCollection
    {
        /**
         * @var $user User|null
         */
        $user = Auth::user();
        $builder = QueryBuilder::for(Comment::class, $request)
            ->select([
                'id',
                'product_id',
                'order_id',
                'product_variant_id',
                'grade',
                'first_name',
                'last_name',
                'email',
                'content',
                'status',
                'created_at',
            ])
            ->where('user_id', $user->id)
            ->with([
                'productVariant.colorAttribute',
                'productVariant.sizeAttribute',
                'product.image:id,path,disk,module',
                'images:id,file_type,path,module'
            ])
            ->allowedSorts(['created_at', 'id'])
            ->defaultSort(['-created_at', '-id']);
        return JsonResource::collection($builder->paginate($this->getPerPage()));
    }

    /**
     * 用户信息
     * @return JsonResource
     */
    public function me(): JsonResource
    {
        /**
         * @var $user User
         */
        $user = Auth::user();
        $user->loadMissing([
            'wallet:user_id,point,invite_amount',
            'addresses' => function (HasMany $builder) {
                $builder->where('is_billing_default', true)
                    ->orWhere('is_shipping_default', true);
            },
        ]);
        // 生成邀请码
        $user->invite_code = hashids()->encode($user->id);
        // 可提现金额
        $user->invite_amount = $user->wallet->invite_amount;
        $user->invite_amount_agent = $user->wallet->invite_amount_agent;
        // 已提现金额
        $user->withdraw_amount = $user->inviteAmountWithdraws()
            ->where('status', InviteAmountWithdrawStatusEnum::Passed)
            ->sum('amount');
        $user->withdraw_amount_agent = convertPrice($user->withdraw_amount, currentCurrency());
        // 粉丝数
        $user->fans_count = $user->allInvitedUsers->count();
        // 预估佣金
        $user->estimated_commission = $user->inviteRewards()
            ->where('is_effective', true)
            ->where('is_cashback', false)
            ->sum('reward_amount');
        $user->estimated_commission_agent = convertPrice($user->estimated_commission, currentCurrency());
        //提现开关
        $user->withdraw_switch = sysConfigService()->get(SysConfigKeyEnum::InviteWithdrawConfig);
        return JsonResource::make($user);
    }


    /**
     * 修改基础信息
     * @param Request $request
     * @return JsonResource
     */
    public function update(Request $request): JsonResource
    {
        /**
         * @var $user User
         */
        $user = Auth::user();
        $validated = $request->validate([
            'email' => 'email',
            'first_name' => ['string', 'max:32'],
            'last_name' => ['string', 'max:32'],
            'birth_date' => ['nullable', 'date'],
            'email_preferences' => [new Enum(UserEmailPreferencesEnum::class)],
        ]);
        $user->update($validated);
        $user->loadMissing([
            'wallet:user_id,point',
        ]);
        // 发放的优惠卷信息
        $coupon_grant_rule = null;
        $coupon = null;
        // 订阅发放优惠卷
        if (
            Arr::exists($validated, 'email_preferences') &&
            Arr::get($validated, 'email_preferences')
        ) {
            // 订阅发放规则
            list($coupon_grant_rule, $coupon) = CouponGrantRule::grantCouponByRuleType(CouponGrantType::Subscribed, $user, true);
        }
        return JsonResource::make($user)->additional([
            'coupon_frames' => [[
                'coupon_grant_rule' => $coupon_grant_rule,
                'coupon' => $coupon
            ]]
        ]);
    }

    /**
     * 修改密码
     * @param Request $request
     * @return JsonResource
     * @throws ValidationException
     */
    public function changePassword(Request $request): JsonResource
    {
        /**
         * @var $user User
         */
        $user = Auth::user();
        $validated = $request->validate([
            'current_password' => ['required', 'string', 'max:32'],
            'password' => ['required', 'string', 'max:32', 'min:6'],
        ]);
        $currentPassword = Arr::pull($validated, 'current_password');
        if (!Hash::check($currentPassword, $user->password)) {
            throw ValidationException::withMessages(['password' => ['Current password error.'],]);
        }
        // 修改密码
        $user->update($validated);
        return JsonResource::make($user);
    }

    // 验证旧密码
    public function checkPassword(Request $request): JsonResource
    {
        /**
         * @var $user User
         */
        $user = Auth::user();
        $validated = $request->validate([
            'password' => ['required', 'string', 'max:32', 'min:6'],
        ]);
        $password = Arr::pull($validated, 'password');
        if (!Hash::check($password, $user->password)) {
            return JsonResource::make(['status' => false]);
        }
        return JsonResource::make(['status' => true]);
    }

    /**
     * 上传
     * @param Request $request
     * @return JsonResource
     */
    public function upload(Request $request): JsonResource
    {
        $data = $request->validate([
            'file' => ['required', 'file', 'max:40960000', 'mimes:jpeg,png,jpg,mp4'],
            'module' => ['string', new Enum(AttachmentModuleEnum::class)],
            'disk' => ['string', 'max:255'],
            'file_name' => 'nullable|string|max:255',
        ], [
            'file.max' => '文件最大支持4G'
        ]);

        // 图片
        $file = Arr::get($data, 'file');
        $model = Arr::get($data, 'module', AttachmentModuleEnum::Other->value);
        /**
         * @var UploadedFile $file
         */
        $imagePath = AttachmentModuleEnum::tryFrom($model)->getPath();
        $datePath = Carbon::today()->format("Ym");
        $fileHash = md5_file($file->getRealPath());  // 获取文件的哈希值
        $disk = config('filesystems.default');
        if (Arr::get($data, 'disk')) {
            $disk = Arr::get($data, 'disk');
        }
        $existingFile = Attachment::query()
            ->where('file_hash', $fileHash)
            ->where('module', $model)
            ->where('disk', $disk)
            ->where('path', '!=', "0")
            ->first();
        if ($existingFile) {
            return JsonResource::make($existingFile);
        }
        try {
            $src = $file->store($imagePath . $datePath, $disk);
            if (!$src) {
                throw new DataException('Upload failed.');
            }
            $attachment = new Attachment();
            $attachment->fill([
                'file_name' => Arr::get($data, 'file_name', $file->getClientOriginalName()),
                'file_type' => $file->getMimeType(),
                'file_size' => $file->getSize(),
                'upload_time' => now(),
                'file_hash' => $fileHash,
                'module' => $model,
                'path' => Str::replace($imagePath, '', $src),
                'disk' => $disk,
                'user_id' => Auth::id(),
            ])->save();
        } catch (\Exception $e) {
            throw new DataException($e->getMessage());
        }
        return JsonResource::make($attachment);
    }


    /**
     * 邀请好友注册
     * @param Request $request
     * @return JsonResource
     * @throws DataException
     */
    public function inviteSendEmail(Request $request): JsonResource
    {
        $validated = $request->validate([
            'email' => 'required|email',
            'optional' => 'string|max:255',
            'invite_link' => 'required|string|max:255',
        ]);
        /**
         * @var $user User
         */
        $user = Auth::user();
        // 生成邀请码
        $validated['invite_code'] = hashids()->encode($user->id);
        // 发邮件
        EmailRuleNotice::dispatch(EmailRuleEventEnum::UserInvite, user: $user, data: $validated)->onQueue(QueueKey::Default->value);
        return JsonResource::make([]);
    }

    /**
     * 邀请好友列表
     * @return JsonResource
     */
    public function inviteIndex(): JsonResource
    {
        $user = Auth::user();
        $query = QueryBuilder::for(UserInviteReward::class)
            ->select([
                'order_user_id',
                DB::raw('SUM(reward_amount) as reward_amount'),
                DB::raw('MIN(invite_users.created_at) as invited_at')
            ])
            ->where('reward_user_id', $user->id)
            ->where('is_cashback', false)
            ->whereIn('reward_type', [RewardTypeEnum::FixedAmount, RewardTypeEnum::Percent])
            ->join('invite_users', function ($join) use ($user) {
                $join->on('user_invite_rewards.order_user_id', '=', 'invite_users.invited_user_id')
                    ->where('invite_users.user_id', '=', $user->id);
            })
            ->groupBy('order_user_id')
            ->with([
                'orderUser:id,email',
            ]);

        return JsonResource::collection($query->paginate($this->getPerPage()));
    }

    /**
     * 当前激活的分享规则，用于邀请好友页面展示宣传
     * @return JsonResource
     */
    public function inviteRule(): JsonResource
    {
        $res = QueryBuilder::for(SharingRule::class)
            ->where(function ($query) {
                $query->where('effective_start_at', '<=', now())
                    ->orWhereNull('effective_start_at');
            })
            ->where(function ($query) {
                $query->where('effective_end_at', '>=', now())
                    ->orWhereNull('effective_end_at');
            })
            ->where('is_global', true)
            ->where('is_publish', true)
            ->select(['title', 'description'])
            ->first();
        return JsonResource::make($res);
    }


    /**
     * 获取是否有未读消息
     * @return JsonResource
     */
    public function newMessage(): JsonResource
    {
        $user = Auth::user();
        $builder = QueryBuilder::for($user->userCoupons()
            ->whereHas('coupon', function (Builder $query) {
                // 只获取未被删除的优惠券
                $query->whereNull('used_at');
                $query->where('is_read', 0);
            }))
            ->with([
                'coupon'
            ])
            ->allowedFilters([
                AllowedFilter::callback('can_used', function (Builder $builder, $value) {
                    if ($value) {
                        // 未过期并且未使用
                        $builder->where('effective_end_at', '>', now())
                            ->whereNull('used_at');
                    }
                })
            ])->count();

        return JsonResource::make(['new_message' => $builder]);
    }
}
