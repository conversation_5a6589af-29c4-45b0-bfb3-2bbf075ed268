<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('membership_point_records', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_id')->index()->comment('用户ID');
            $table->bigInteger('membership_level_id')->comment('会员等级ID');
            $table->bigInteger('order_id')->nullable()->comment('订单ID');
            $table->decimal('order_amount', 12, 2)->comment('订单金额');
            $table->decimal('point_rate', 5, 4)->comment('积分比例');
            $table->decimal('earned_points', 10, 2)->comment('获得积分');
            $table->string('source_type', 32)->default('order')->comment('积分来源类型');
            $table->bigInteger('source_id')->nullable()->comment('来源ID');
            $table->text('description')->nullable()->comment('描述');
            $table->timestamps();
            
            $table->index(['user_id', 'created_at']);
            $table->index('order_id');
            $table->index(['source_type', 'source_id']);
            $table->engine('InnoDB');
            $table->comment('会员积分获得记录表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('membership_point_records');
    }
};
