<?php

namespace App\Admin\Controllers;

use App\Constants\Permissions;
use App\Exceptions\DataException;
use App\Http\Controllers\Controller;
use App\Models\Group;
use App\Models\User\User;
use App\Models\User\UserGroup;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rules\Exists;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;
use Illuminate\Validation\Rule;

class GroupController extends Controller
{

    public function __construct()
    {
        $this->hasPermissionOr(Permissions::GroupUpdate)->only(['store', 'update', 'destroy',]);
        $this->hasPermissionOr(Permissions::GroupUpdate, Permissions::GroupIndex)->only(['index', 'show']);
    }

    /**
     * 列表
     * @param \Illuminate\Http\Request $request
     * @return AnonymousResourceCollection
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $builder = QueryBuilder::for(Group::class, $request)
            ->allowedFilters([
                AllowedFilter::partial('name'),
            ])
            ->withCount('users')
            ->allowedSorts(['id'])
            ->defaultSort('-id');
        $res = $builder->paginate($this->getPerPage());
        return JsonResource::collection($res);
    }

    /**
     * 新增
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Group $group
     * @throws \App\Exceptions\DataException
     * @return JsonResource
     */
    public function store(Request $request, Group $group): JsonResource
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:32', Rule::unique('groups')->whereNull('deleted_at')],
            'description' => ['required', 'nullable', 'string', 'max:255'],
            'user_ids' => ['required', 'nullable', 'array'],
            'user_ids.*' => ['required', new Exists(User::class, 'id')],
        ]);

        try {
            DB::beginTransaction();
            $user_ids = Arr::pull($validated, 'user_ids');
            // 新增
            $group->fill($validated)->save();
            // 关联用户
            $group->users()->sync($user_ids);
            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }

        return JsonResource::make($group->load('users'));
    }

    /**
     * 修改
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Group $group
     * @throws \App\Exceptions\DataException
     * @return JsonResource
     */
    public function update(Request $request, Group $group)
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:32'],
            'description' => ['required', 'nullable', 'string', 'max:255'],
            'user_ids' => ['required', 'nullable', 'array'],
            'user_ids.*' => ['required', new Exists(User::class, 'id')],
        ]);

        try {
            DB::beginTransaction();
            $user_ids = Arr::pull($validated, 'user_ids');

            // 更新
            $group->update($validated);

            // 关联用户
            $group->users()->sync($user_ids);

            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }

        return JsonResource::make($group->load('users'));
    }

    /**
     * 批量删除
     * @param \Illuminate\Http\Request $request
     * @throws \App\Exceptions\DataException
     * @return JsonResponse|mixed
     */
    public function destroy(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'ids' => ['array', 'required'],
            'ids.*' => ['integer', new Exists(Group::class, 'id')],
        ]);
        $ids = Arr::get($validated, 'ids');

        try {
            DB::beginTransaction();

            // 删除关联用户
            UserGroup::query()->whereIn('group_id', $ids)->delete();

            // 删除用户组
            Group::query()->whereIn('id', $ids)->delete();

            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
            throw new DataException($throwable->getMessage());
        }

        return response()->json(['message' => '删除成功']);
    }

    /**
     * 详情
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Group $group
     * @return JsonResource
     */
    public function show(Request $request, Group $group): JsonResource
    {
        return JsonResource::make($group->load('users'));
    }

    /**
     * 选择
     * @param \Illuminate\Http\Request $request
     * @return JsonResource
     */
    public function options(Request $request)
    {
        $res = QueryBuilder::for(Group::class, $request)
            ->allowedFilters([
                AllowedFilter::partial('name'),
            ])
            ->select([DB::raw('name as label'), DB::raw('id as value')])
            ->get();

        return JsonResource::make($res);
    }
    /**
     * 检查上传文件中的邮箱并返回存在的用户ID数组
     * @param \Illuminate\Http\Request $request
     * @return JsonResponse
     */
    public function checkEmails(Request $request): JsonResponse
    {
        $request->validate([
            'file' => 'required|file|mimes:csv,txt,xlsx,xls|max:2048' // 限制文件类型和大小
        ]);

        // 获取上传文件
        $file = $request->file('file');

        // 解析文件内容为邮箱数组
        $emails = $this->parseEmailsFromFile($file);

        // 查询存在的用户
        $existingUsers = User::whereIn('email', $emails)
            ->pluck('id')
            ->toArray();

        return response()->json([
            'success' => true,
            'data' => [
                'existing_user_ids' => $existingUsers,
                'count' => count($existingUsers),
                'total_emails' => count($emails) // 可选：返回总邮箱数
            ]
        ]);
    }

    /**
     * 从文件中解析邮箱
     */
    private function parseEmailsFromFile($file): array
    {
        $extension = $file->getClientOriginalExtension();
        $emails = [];

        try {
            if ($extension === 'csv' || $extension === 'txt') {
                // 处理CSV/TXT
                $fileHandle = fopen($file->getPathname(), 'r');
                while (($line = fgetcsv($fileHandle)) !== false) {
                    foreach ($line as $cell) {
                        if (filter_var($cell, FILTER_VALIDATE_EMAIL)) {
                            $emails[] = $cell;
                        }
                    }
                }
                fclose($fileHandle);
            } elseif (in_array($extension, ['xlsx', 'xls'])) {
                // 处理Excel（需要安装 maatwebsite/excel 包）
                $rows = \Excel::toArray([], $file);
                foreach ($rows[0] as $row) {
                    foreach ($row as $cell) {
                        if (filter_var($cell, FILTER_VALIDATE_EMAIL)) {
                            $emails[] = $cell;
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            throw new \RuntimeException("文件解析失败: " . $e->getMessage());
        }

        return array_unique($emails); // 去重
    }
    
}
