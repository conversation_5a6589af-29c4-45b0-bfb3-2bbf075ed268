<?php

namespace App\Constants;

use Illuminate\Support\Arr;

enum Permissions: string
{
    case RolesUpdate = 'roles.update';
    case AdminUsersUpdate = 'adminUsers.update';
    case AttributesUpdate = 'attributes.update';
    case AttributesIndex = 'attributes.index';
    case BannersUpdate = 'banners.update';
    case BannersIndex = 'banners.index';
    case BrandsUpdate = 'brands.update';
    case BrandsIndex = 'brands.index';
    case BroadcastsUpdate = 'broadcasts.update';
    case BroadcastsIndex = 'broadcasts.index';
    case CategoriesUpdate = 'categories.update';
    case CategoriesIndex = 'categories.index';
    case CollectionsUpdate = 'collections.update';
    case CollectionsIndex = 'collections.index';
    case CommentsUpdate = 'comments.update';
    case CommentsIndex = 'comments.index';
    case CouponsUpdate = 'coupons.update';
    case CouponsIndex = 'coupons.index';
    case CurrenciesUpdate = 'currencies.update';
    case CurrenciesIndex = 'currencies.index';
    case FootersUpdate = 'footers.update';
    case FootersIndex = 'footers.index';
    case HelpFaqsUpdate = 'helpFaqs.update';
    case HelpFaqsIndex = 'helpFaqs.index';
    case MaterialsUpdate = 'materials.update';
    case MaterialsIndex = 'materials.index';
    case OrdersUpdate = 'orders.update';
    case OrdersIndex = 'orders.index';
    case ProductsUpdate = 'products.update';
    case ProductsIndex = 'products.index';
    case PromotionUpdate = 'promotions.update';
    case PromotionIndex = 'promotions.index';
    case StylesUpdate = 'styles.update';
    case StylesIndex = 'styles.index';
    case SysConfigsUpdate = 'sysConfigs.update';
    case SysConfigsIndex = 'sysConfigs.index';
    case UsersUpdate = 'users.update';
    case UsersIndex = 'users.index';
    case EmailsUpdate = 'emails.update';
    case EmailsIndex = 'emails.index';
    case InviteConfigUpdate = 'inviteConfig.update';
    case InviteConfigIndex = 'inviteConfig.index';
    case SharingRulesUpdate = 'sharingRule.update';
    case SharingRulesIndex = 'sharingRule.index';
    case InvitesStatIndex = 'inviteStat.index';
    case ShippingsUpdate = 'shipping.update';
    case ShippingsIndex = 'shipping.index';
    case PaymentMethodsUpdate = 'paymentMethods.update';
    case PaymentMethodsIndex = 'paymentMethods.index';
    case BrandMasterProgramsUpdate = 'brandMasterPrograms.update';
    case BrandMasterProgramsIndex = 'brandMasterPrograms.index';
    case ContactUsMessagesUpdate = 'contactUsMessages.update';
    case ContactUsMessagesIndex = 'contactUsMessages.index';
    case CartsUpdate = 'cart.update';
    case CartsIndex = 'cart.index';
    case CartsStat = 'cart.stat';
    case UserExperienceActivitiesUpdate = 'UserExperienceActivities.update';
    case UserExperienceActivitiesIndex = 'UserExperienceActivities.index';
    case ActivityNavUpdate = 'ActivityNav.update';
    case ActivityNavIndex = 'ActivityNav.index';
    case ModesUpdate = 'Modes.update';
    case ModesIndex = 'Modes.index';
    case CmsSettingsUpdate = 'CmsSettings.update';
    case CmsSettingsIndex = 'CmsSettings.index';
    case GroupUpdate = 'Group.update';
    case GroupIndex = 'Group.index';
    case SubscribeUpdate = 'subscribe.update';
    case SubscribeIndex = 'subscribe.index';
    case WithdrawsIndex = 'withdraws.index';

    public static function values(): array
    {
        return array_column(self::list(), 'value');
    }

    public function desc(): string
    {
        return Arr::get(self::list(), $this->value);
    }

    public static function list(): array
    {
        return [
            self::ProductsIndex->value => '商品列表',
            self::ProductsUpdate->value => '商品修改',
            self::AdminUsersUpdate->value => '后台用户管理',
            self::RolesUpdate->value => '角色管理',

            self::BannersUpdate->value => 'Banner-修改',
            self::BannersIndex->value => 'Banner-列表',

            self::BrandsUpdate->value => '品牌-修改',
            self::BrandsIndex->value => '品牌-列表',

            self::BroadcastsUpdate->value => '广播栏-修改',
            self::BroadcastsIndex->value => '广播栏-列表',

            self::CategoriesUpdate->value => '分类管理-修改',
            self::CategoriesIndex->value => '分类管理-列表',

            self::CollectionsUpdate->value => '商品导航管理-修改',
            self::CollectionsIndex->value => '商品导航管理-列表',

            self::CommentsUpdate->value => '评论管理-修改',
            self::CommentsIndex->value => '评论管理-列表',

            self::CouponsUpdate->value => '折扣管理-修改',
            self::CouponsIndex->value => '折扣管理-列表',

            self::CurrenciesUpdate->value => '语言配置-修改',
            self::CurrenciesIndex->value => '语言配置-列表',

            self::FootersUpdate->value => '底部导航-修改',
            self::FootersIndex->value => '底部导航-列表',

            self::HelpFaqsUpdate->value => '帮助解答-修改',
            self::HelpFaqsIndex->value => '帮助解答-列表',

            self::MaterialsUpdate->value => '材质管理-修改',
            self::MaterialsIndex->value => '材质管理-列表',

            self::OrdersUpdate->value => '订单管理-修改',
            self::OrdersIndex->value => '订单管理-列表',

            self::PromotionUpdate->value => '广告推广-修改',
            self::PromotionIndex->value => '广告推广-列表',

            self::StylesUpdate->value => '款式管理-修改',
            self::StylesIndex->value => '款式管理-列表',

            self::SysConfigsUpdate->value => '系统配置管理-修改',
            self::SysConfigsIndex->value => '系统配置管理-列表',

            self::UsersUpdate->value => '用户管理-修改',
            self::UsersIndex->value => '用户管理-列表',

            self::EmailsUpdate->value => '邮件管理-修改',
            self::EmailsIndex->value => '邮件管理-列表',

            self::InviteConfigUpdate->value => '邀请活动-修改',
            self::InviteConfigIndex->value => '邀请活动-列表',

            self::AttributesUpdate->value => '属性管理-修改',
            self::AttributesIndex->value => '属性管理-列表',

            self::SharingRulesUpdate->value => '用户分享规则-修改',
            self::SharingRulesIndex->value => '用户分享规则-列表',

            self::InvitesStatIndex->value => '用户邀请统计-列表',

            self::ShippingsUpdate->value => '物流管理-修改',
            self::ShippingsIndex->value => '物流管理-列表',

            self::PaymentMethodsUpdate->value => '支付方式管理-修改',
            self::PaymentMethodsIndex->value => '支付方式管理-列表',

            self::BrandMasterProgramsUpdate->value => '品牌大使计划-修改',
            self::BrandMasterProgramsIndex->value => '品牌大使计划-列表',

            self::ContactUsMessagesUpdate->value => '联系我们-修改',
            self::ContactUsMessagesIndex->value => '联系我们-列表',

            self::CartsUpdate->value => '购物车管理-修改',
            self::CartsIndex->value => '购物车管理-列表',
            self::CartsStat->value => '购物车统计',

            self::UserExperienceActivitiesUpdate->value => '用户体验计划管理-修改',
            self::UserExperienceActivitiesIndex->value => '用户体验计划管理-列表',

            self::ActivityNavUpdate->value => '活动导航管理-修改',
            self::ActivityNavIndex->value => '活动导航管理-列表',

            self::ModesUpdate->value => '风格管理-修改',
            self::ModesIndex->value => '风格管理-列表',

            self::CmsSettingsUpdate->value => 'CMS设置-修改',
            self::CmsSettingsIndex->value => 'CMS设置-列表',

            self::GroupUpdate->value => '用户组管理-修改',
            self::GroupIndex->value => '用户组管理-列表',

            self::SubscribeUpdate->value => '订阅推送管理-修改',
            self::SubscribeIndex->value => '订阅推送管理-列表',

            self::WithdrawsIndex->value => '提现记录-列表',
        ];
    }
}
