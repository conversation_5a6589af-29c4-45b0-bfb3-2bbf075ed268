<?php

namespace App\Services;

use App\Constants\QueueKey;
use App\Jobs\EmailRuleNotice;
use App\Models\Cart;
use App\Models\Coupon\Coupon;
use App\Models\CouponGrantRule;
use App\Models\Enums\Discount\DiscountEffectiveDateTypeEnum;
use App\Models\Enums\EmailRuleEventEnum;
use App\Models\Enums\Reward\RewardTypeEnum;
use App\Models\Enums\User\InviteUserTypeEnum;
use App\Models\Enums\User\UserRegisterTypesEnum;
use App\Models\Enums\User\UserStatusEnum;
use App\Models\Enums\User\WalletChangeTypeEnum;
use App\Models\FacebookUser;
use App\Models\GoogleUser;
use App\Models\TwitterUser;
use App\Models\User\User;
use App\Models\User\UserCoupon;
use App\Models\User\UserInviteReward;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Laravel\Socialite\Two\User as SocialiteUser;
use SocialiteProviders\Manager\OAuth1\User as Socialite2User;

class UserService
{
    /**
     * 给用户发放优惠券
     * @desc 发放优惠券无条件发, 最高权限
     * @param User $user
     * @param Coupon $coupon
     * @param  CouponGrantRule $rule
     * @param string|null $effectiveEndAt 固定结束时间（不传用优惠券的结束时间）
     * @param bool $is_framed 是否已动效展示
     * @return bool|UserCoupon
     */
    public function giveUserCoupon(User $user, Coupon $coupon, CouponGrantRule $rule = null, $effectiveEndAt = null, $is_framed = false): bool|UserCoupon
    {
        $userCoupon = new UserCoupon();

        // 根据优惠卷有效期类型
        if ($coupon->effective_date_type == DiscountEffectiveDateTypeEnum::Cycle->value) {
            // 开始时间
            $effectiveStartAt = $coupon->effective_start_at;
            // 结束时间
            $effectiveEndAt = $coupon->effective_end_at;
        } else {
            // 开始时间
            $effectiveStartAt = now();
            // 结束时间
            $effectiveEndAt = now()->addDays($coupon->effective_days);
        }
        // 如果优惠券已失效，则不能发放
        if ($effectiveEndAt->isPast()) {
            return false;
        }
        // 发放优惠券
        $userCoupon->fill([
            'code' => $coupon->code,
            'effective_start_at' => $effectiveStartAt,
            'effective_end_at' => $effectiveEndAt,
            'is_framed' => $is_framed,
        ]);

        $userCoupon->coupon()->associate($coupon);
        $userCoupon->couponGrantRule()->associate($rule);

        return $user->userCoupons()->save($userCoupon);
    }

    /**
     * 用户主动领取优惠券
     * @param User $user
     * @param Coupon $coupon
     * @return Coupon
     */
    public function userReceiveCoupon(User $user, Coupon $coupon): Coupon {}

    /**
     * @param $email
     * @param $password
     * @param $firstName
     * @param $lastName
     * @param $brithDate
     * @return User|null
     */
    public function createUser($email, $password, $firstName = null, $lastName = null, $brithDate = null, UserRegisterTypesEnum $registerType = null): ?User
    {
        $user = new User([
            'email' => $email,
            'password' => $password,
            'status' => UserStatusEnum::StatusEnable,
            'first_name' => $firstName,
            'last_name' => $lastName,
            'birth_date' => $brithDate,
            'register_type' => $registerType
        ]);
        return $user->save() ? $user : null;
    }

    /**
     * 快速创建账号
     * @param string $email
     * @return User|null
     */
    public function fastCreateUserByEmail(string $email, $registerType = UserRegisterTypesEnum::FastPay): ?User
    {
        return $this->createUser($email, null, registerType: $registerType);
    }

    /**
     * @param $email
     * @return Builder|Model|object
     */
    public function findByEmail($email): Builder|User|null
    {
        return User::query()
            ->where('email', $email)
            ->whereNull('deleted_at')
            ->first();
    }


    /**
     * 获取购物车
     * @param User|null $user
     * @param string|null $sessionUuid
     * @return Builder|Model
     */
    public function getOrCreateCart(?User $user = null, string $sessionUuid = null): Cart|Builder
    {
        if ($user) {
            $cart = $user->cart()->firstOrCreate();
        } else {
            $sessionUuid = $sessionUuid ?: Str::uuid();
            $cart = Cart::query()
                ->where('session_uuid', $sessionUuid)
                ->where(function ($query) {
                    $query->whereNull('order_id')
                        ->where('is_checkout', false);
                })
                ->first();

            if (!$cart) {
                $cart = Cart::create([
                    'session_uuid' => $sessionUuid,
                    'user_id' => null,
                    'order_id' => null,
                    'is_checkout' => false
                ]);
            }
        }
        return $cart;
    }

    /**
     * 从谷歌创建用户
     * @param GoogleUser $user
     * @return User|null
     */
    public function createUserByGoogle(GoogleUser $user): ?User
    {
        return $this->createUser(
            $user->email,
            null,
            $user->family_name,
            $user->given_name,
            registerType: UserRegisterTypesEnum::Google
        );
    }


    /**
     * 从facebook创建用户
     * @param FacebookUser $user
     * @return User|null
     */
    public function createUserByFacebook(FacebookUser $user): ?User
    {
        return $this->createUser(
            $user->email,
            null,
            Str::before($user->name, ' '),
            Str::after($user->name, ' '),
            registerType: UserRegisterTypesEnum::Facebook
        );
    }

    /**
     * 从推特创建用户
     * @param TwitterUser $user
     * @return User|null
     */
    public function createUserByTwitter(TwitterUser $user): ?User
    {
        return $this->createUser(
            $user->email,
            null,
            Str::before($user->name, ' '),
            Str::after($user->name, ' '),
            registerType: UserRegisterTypesEnum::Facebook
        );
    }


    /**
     * 创建或者获取 google 用户
     * @return GoogleUser|Builder
     * @var array $data
     */
    public function firstOrCreateGoogleUser($user): GoogleUser|Builder
    {
        return GoogleUser::query()
            ->updateOrCreate(['email' => Arr::get($user, 'email')], [
                'google_id' => Arr::get($user, 'sub'),
                'avatar' => Arr::get($user, 'picture'),
                ...Arr::only($user, [
                    'given_name',
                    'name',
                    'family_name',
                    'email_verified',
                ]),
            ]);
    }


    /**
     * 创建或者获取 google 用户
     * @return FacebookUser|Builder
     * @var SocialiteUser $data
     */
    public function firstOrCreateFacebookUser(SocialiteUser $user): FacebookUser|Builder
    {
        return FacebookUser::query()
            ->updateOrCreate(['email' => $user->email], [
                'email' => $user->email,
                'facebook_id' => $user->id,
                'name' => $user->name,
                'avatar' => $user->avatar,
            ]);
    }

    /**
     * 创建或者获取 推特 用户
     * @return TwitterUser|Builder
     * @var Socialite2User $data
     */
    public function firstOrCreateTwitterUser(Socialite2User $user): TwitterUser|Builder
    {
        return TwitterUser::query()
            ->updateOrCreate(['email' => $user->email], [
                'email' => $user->email,
                'twitter_id' => $user->id,
                'name' => $user->name,
                'nickname' => $user->nickname,
                'avatar' => $user->avatar,
            ]);
    }

    /**
     * 发放邀请奖励
     */
    public function cashbackReward(UserInviteReward $reward)
    {
        $reward_type = $reward->reward_type;
        $user = $reward->rewardUser;

        // 发放奖励
        switch ($reward_type) {
            case RewardTypeEnum::FixedAmount->value:
            case RewardTypeEnum::Percent->value:
                userWalletService()->walletChangeInviteAmount($user->wallet, WalletChangeTypeEnum::OrderCompleted, $reward->reward_amount, "", $reward);
                break;
            case RewardTypeEnum::Coupon->value:
                $coupon = Coupon::find($reward->reward_coupon_id);
                if (!$coupon) {
                    break;
                }
                UserService()->giveUserCoupon($user, $coupon);
                break;
            default:
                break;
        }

        // 发送邮件
        $email_setting = $reward->rule->emailSetting;
        if (Arr::get($email_setting, 'enabled')) {
            // $emailTemplateId = Arr::get($email_setting, 'email_template_id');
            // 邮件队列
            EmailRuleNotice::dispatch(EmailRuleEventEnum::UserInviteRewardGrant, user: $user)->onQueue(QueueKey::Default->value);
        }
        $reward->update(['is_cashback' => true]);
    }
    /**
     * 通过日期筛选邀请统计
     * @param mixed $start_at
     * @param mixed $end_at
     * @return array
     */
    public function inviteStatByDate($start_at, $end_at, $email=null)
    {
        // 查询昨日的数据
        $users = User::query()->whereNotNull('first_share_date')
            ->with([
                'allInvitedUsers' => function ($query) use ($start_at, $end_at) {
                    $query->where('invite_users.created_at', '>=', $start_at)
                        ->where('invite_users.created_at', '<=', $end_at);
                },
                'inviteRewards' => function ($query) use ($start_at, $end_at) {
                    $query->where('order_created_at', '>=', $start_at)
                        ->where('order_created_at', '<=', $end_at);
                    $query->where('is_effective', true);
                },
                'inviteAmountWithdraws' => function ($query) use ($start_at, $end_at) {
                    $query->where('created_at', '>=', $start_at)
                        ->where('created_at', '<=', $end_at);
                }
            ]);
        if($email){
            $users->where('email', 'like', '%' . $email . '%');
        }
        $users = $users->get();
        // 统计
        $result = [];
        foreach ($users as $user) {
            $exclusive_fans_count = 0;
            $ordinary_fans_count = 0;
            // 循环计算专属粉丝和普通粉丝人数
            $user->allInvitedUsers->each(function ($item) use (&$exclusive_fans_count, &$ordinary_fans_count) {
                // 专属粉丝人数
                if ($item->pivot->type == InviteUserTypeEnum::ExclusiveFans->value) {
                    $exclusive_fans_count++;
                }
                // 普通粉丝人数
                if ($item->pivot->type == InviteUserTypeEnum::OrdinaryFans->value) {
                    $ordinary_fans_count++;
                }
            });

            $result[] = [
                'user_id' => $user->id,
                'email' => $user->email,
                'total_fans_count' => $user->allInvitedUsers->count(),
                'exclusive_fans_count' => $exclusive_fans_count,
                'ordinary_fans_count' => $ordinary_fans_count,
                'estimated_count' => $user->inviteRewards->count(),
                'estimated_amount' => $user->inviteRewards->sum('reward_amount'),
                'withdraw_count' => $user->inviteAmountWithdraws->count(),
                'withdraw_amount' => $user->inviteAmountWithdraws->sum('amount'),
            ];
        }

        return $result;
    }

    /**
     * 今日邀请统计
     * @return array{daily: array, total: array{estimated_amount: int, estimated_count: int, exclusive_fans_count: int, ordinary_fans_count: int, share_count: int, withdraw_amount: int, withdraw_count: int|float[]}|array{daily: array, total: array{estimated_amount: int, estimated_count: int, exclusive_fans_count: int, ordinary_fans_count: int, share_count: int, withdraw_amount: int, withdraw_count: int}}}
     */
    public function todayInviteStat($email=null)
    {
        $start_at = now()->startOfDay();
        $end_at = now()->endOfDay();
        // 统计
        $stats = $this->inviteStatByDate($start_at, $end_at, $email);
        $result = [
            'share_count' => 0,
            'total_fans_count' => 0,
            'exclusive_fans_count' => 0,
            'ordinary_fans_count' => 0,
            'estimated_count' => 0,
            'estimated_amount' => 0,
            'withdraw_count' => 0,
            'withdraw_amount' => 0
        ];
        // 统计
        foreach ($stats as $stat) {
            $result['share_count'] += Arr::get($stat, 'total_fans_count', 0) ? 1 : 0;
            $result['total_fans_count'] += Arr::get($stat, 'total_fans_count', 0);
            $result['exclusive_fans_count'] += Arr::get($stat, 'exclusive_fans_count', 0);
            $result['ordinary_fans_count'] += Arr::get($stat, 'ordinary_fans_count', 0);
            $result['estimated_count'] += Arr::get($stat, 'estimated_count', 0);
            $result['estimated_amount'] += Arr::get($stat, 'estimated_amount', 0);
            $result['withdraw_count'] += Arr::get($stat, 'withdraw_count', 0);
            $result['withdraw_amount'] += Arr::get($stat, 'withdraw_amount', 0);
        }

        return $result;
    }
}
