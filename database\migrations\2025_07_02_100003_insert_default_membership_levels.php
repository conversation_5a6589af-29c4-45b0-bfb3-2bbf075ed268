<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 插入默认会员等级数据
        DB::table('membership_levels')->insert([
            [
                'name' => '普通会员',
                'slug' => 'bronze',
                'description' => '新注册用户默认等级，享受基础服务',
                'min_spend_amount' => 0.00,
                'max_spend_amount' => 499.99,
                'point_rate' => 0.01, // 消费1元获得0.01积分
                'benefits' => json_encode([
                    'point_rate' => '1%',
                    'birthday_bonus' => '生日当月额外5%积分',
                    'free_shipping_threshold' => 99
                ]),
                'color' => '#CD7F32',
                'sort_order' => 1,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => '银卡会员',
                'slug' => 'silver',
                'description' => '累计消费满500元可升级，享受更多优惠',
                'min_spend_amount' => 500.00,
                'max_spend_amount' => 1999.99,
                'point_rate' => 0.015, // 消费1元获得0.015积分
                'benefits' => json_encode([
                    'point_rate' => '1.5%',
                    'birthday_bonus' => '生日当月额外10%积分',
                    'free_shipping_threshold' => 79,
                    'exclusive_coupons' => '专属优惠券'
                ]),
                'color' => '#C0C0C0',
                'sort_order' => 2,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => '金卡会员',
                'slug' => 'gold',
                'description' => '累计消费满2000元可升级，享受高级服务',
                'min_spend_amount' => 2000.00,
                'max_spend_amount' => 4999.99,
                'point_rate' => 0.02, // 消费1元获得0.02积分
                'benefits' => json_encode([
                    'point_rate' => '2%',
                    'birthday_bonus' => '生日当月额外15%积分',
                    'free_shipping_threshold' => 59,
                    'exclusive_coupons' => '专属优惠券',
                    'priority_customer_service' => '优先客服'
                ]),
                'color' => '#FFD700',
                'sort_order' => 3,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => '白金会员',
                'slug' => 'platinum',
                'description' => '累计消费满5000元可升级，享受顶级服务',
                'min_spend_amount' => 5000.00,
                'max_spend_amount' => 9999.99,
                'point_rate' => 0.025, // 消费1元获得0.025积分
                'benefits' => json_encode([
                    'point_rate' => '2.5%',
                    'birthday_bonus' => '生日当月额外20%积分',
                    'free_shipping_threshold' => 0,
                    'exclusive_coupons' => '专属优惠券',
                    'priority_customer_service' => '优先客服',
                    'early_access' => '新品抢先购'
                ]),
                'color' => '#E5E4E2',
                'sort_order' => 4,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => '钻石会员',
                'slug' => 'diamond',
                'description' => '累计消费满10000元可升级，享受至尊服务',
                'min_spend_amount' => 10000.00,
                'max_spend_amount' => null, // 无上限
                'point_rate' => 0.03, // 消费1元获得0.03积分
                'benefits' => json_encode([
                    'point_rate' => '3%',
                    'birthday_bonus' => '生日当月额外25%积分',
                    'free_shipping_threshold' => 0,
                    'exclusive_coupons' => '专属优惠券',
                    'priority_customer_service' => '专属客服',
                    'early_access' => '新品抢先购',
                    'vip_events' => 'VIP活动邀请',
                    'personal_shopper' => '专属购物顾问'
                ]),
                'color' => '#B9F2FF',
                'sort_order' => 5,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::table('membership_levels')->truncate();
    }
};
