<?php

namespace App\Apis\Airwallex;

use App\Models\Order\Order;
use App\Models\Order\OrderItem;
use Illuminate\Support\Str;
use App\Models\Cart;
use App\Models\CartItem;
use App\Models\Country;

class AirwallexService
{

    /**
     * 构建支付意向数据 - 订单
     * @param Order $order
     * @return array
     */
    public static function formatIntentsData(Order $order): array
    {
        $price = $discount = 0;
        $currencyCode = 'USD';
        //产品信息
        $products = $order->items()->with(['product.product'])->chunkMap(function (OrderItem $item) use ($order, &$price, &$discount, $currencyCode) {
            $price += $item->{'price'} * $item->{'num'};
            $discount += $item->coupon_discount_price;
            $spuProduct = $item->product->product;
            return [
                'code' => $item->product->sku,
                'desc' => "{$item->product->getName()}({$item->product->sku})",
                'name' => $item->product->getName(),
                'quantity' => $item->{'num'},
                'sku' => $spuProduct?->sku,
                'type' => 'physical',
                'unit_price' => round($item->{'price'}, 2),
                'url' => 'http://allurense.test/products/' . $spuProduct?->slug_title,
            ];
        })->toArray();
        $email = $order->email ?: $order->user?->email;
        return [
            'request_id' => Str::uuid(),
            'amount' => round(($price + $order->shipping_fee - $order->shipping_fee_discount - $discount), 2),
            'currency' => $currencyCode,
            'merchant_order_id' => $order->no,
            'customer' => [
                'first_name' => $order->billingAddress->first_name,
                'last_name' => $order->billingAddress->last_name,
                'email' => $email,
                'phone_number' => $order->billingAddress->phone,
            ],
            'order' => [
                'products' => $products,
                'shipping' => [
                    "address" =>
                    [
                        "city" => $order->shippingAddress->city,
                        'state' => $order->shippingAddress->state,
                        'country_code' => $order->shippingAddress->country->iso_code,
                        'postcode' => $order->shippingAddress->zip,
                        'street' => $order->shippingAddress->address,
                    ],
                    "fee_amount" => $order->shipping_fee,    //运费
                    "email" => $email,
                    "first_name" => $order->shippingAddress->first_name,
                    "last_name" => $order->shippingAddress->last_name,
                    "phone_number" => $order->shippingAddress->phone,
                ]
            ],
            "return_url" => config('app.web_url') .'/auth/3ds-check'
            // "action" => 'ACCEPT',
            // "type" => 'CUSTOMER_EMAIL',
            // "payment_method_options" => [
            //     "card" => [
            //         "risk_control" => [
            //             "three_ds_action" => "FORCE_3DS"
            //         ]
            //     ]
            // ]
        ];
    }

    /**
     * 构建支付意向数据 - 购物车
     * @param Cart $cart
     * @return array
     */
    public static function formatIntentsDataFromCart(Cart $cart): array
    {
        $price = $discount = 0;
        $currencyCode = currentCurrency()->currency_code;

        // 获取结账数据
        $checkoutData = $cart->checkout_data ?? [];
        $shippingAddress = $checkoutData['shipping_address'] ?? [];
        $billingAddress = $checkoutData['billing_address'] ?? [];
        $email = $checkoutData['email'] ?? null;

        // 产品信息
        $products = $cart->items()->with(['productVariant.product'])->chunkMap(function (CartItem $item) use (&$price, &$discount, $currencyCode) {
            $price += $item->price_agent * $item->num;
            $discount += $item->discount_agent;
            $product = $item->productVariant;
            $spuProduct = $product->product;

            return [
                'code' => $product->sku,
                'desc' => "{$product->getName()}({$product->sku})",
                'name' => $product->getName(),
                'quantity' => $item->num,
                'sku' => $spuProduct?->sku,
                'type' => 'physical',
                'unit_price' => round($item->price_agent, 2),
                'url' => 'https://www.ardoreva.com/products/' . $spuProduct?->slug_title,
            ];
        })->toArray();

        // 构建基本数据
        $data = [
            'request_id' => Str::uuid(),
            'amount' => round(($price + $cart->delivery_fee - $cart->delivery_fee_discount - $discount), 2),
            'currency' => $currencyCode,
            'merchant_order_id' => 'cart_' . $cart->id,
            'return_url' => 'https://www.ardoreva.com/payment/success',
        ];

        // 添加客户信息
        if (!empty($billingAddress)) {
            $data['customer'] = [
                'first_name' => $billingAddress['first_name'] ?? '',
                'last_name' => $billingAddress['last_name'] ?? '',
                'email' => $email,
                'phone_number' => $billingAddress['phone'] ?? '',
            ];
        }

        // 添加订单信息
        $data['order'] = [
            'products' => $products,
        ];

        // 添加配送信息
        if (!empty($shippingAddress)) {
            $countryCode = '';
            if (!empty($shippingAddress['country_id'])) {
                $country = Country::find($shippingAddress['country_id']);
                $countryCode = $country ? $country->iso_code : '';
            }

            $data['order']['shipping'] = [
                "address" => [
                    "city" => $shippingAddress['city'] ?? '',
                    'state' => $shippingAddress['state'] ?? '',
                    'country_code' => $countryCode,
                    'postcode' => $shippingAddress['zip'] ?? '',
                    'street' => $shippingAddress['address'] ?? '',
                ],
                "fee_amount" => $cart->delivery_fee,    // 运费
                "email" => $email,
                "first_name" => $shippingAddress['first_name'] ?? '',
                "last_name" => $shippingAddress['last_name'] ?? '',
                "phone_number" => $shippingAddress['phone'] ?? '',
            ];
        }

        return $data;
    }

    /**
     * 构建银行卡确认支付请求数据 - 订单
     * @param Order $order
     * @return array
     */
    public static function formatCardPaymentData(Order $order, $cardInfo, $intent_id=null): array
    {
        $email = $order->email ?: $order->user?->email;
        return [
            'request_id' => Str::uuid(),
            'return_url' => config('app.web_url') .'/auth/3ds-check?intent_id='.$intent_id,
            'payment_method' => [
                "type" => "card",
                "card" => [
                    "billing" => [
                        "address" => [
                            "state" => $order->billingAddress->state,
                            "city" => $order->billingAddress->city,
                            "country_code" => $order->billingAddress->country->iso_code,
                            "postcode" => $order->billingAddress->zip,
                            "street" => $order->billingAddress->address,
                        ],
                        "phone_number" => $order->billingAddress->phone,
                        "email" => $email,
                        "first_name" => $order->billingAddress->first_name,
                        "last_name" => $order->billingAddress->last_name,
                    ],
                    ...$cardInfo,
                    'number_type' => 'PAN'
                ],
                'three_ds' => [
                    'return_url' => config('app.web_url') .'/auth/3ds-check?intent_id='.$intent_id,
                ],
            ],
            'payment_method_options' => [
                "card" => [
                    "auto_capture" => true    //自动确认
                ]
            ],
            'device_data' => [
                'device_id' => request()->header('device_id', '00000000-000000000000000'),
                'ip_address' => request()->ip(),
            ], ///payment/airwallex/3ds-check
            ''
        ];
    }

    /**
     * 构建银行卡确认支付请求数据 - 购物车
     * @param Cart $cart
     * @param array $cardInfo
     * @return array
     */
    public static function formatCardPaymentDataForCart(Cart $cart, array $cardInfo): array
    {
        // 获取结账数据
        $checkoutData = $cart->checkout_data ?? [];
        $billingAddressData = $checkoutData['billing_address'] ?? [];
        $email = $checkoutData['email'] ?? ($cart->user?->email ?? '');

        // 获取国家代码
        $countryCode = '';
        if (!empty($billingAddressData['country_id'])) {
            $country = Country::find($billingAddressData['country_id']);
            $countryCode = $country ? $country->iso_code : '';
        }

        return [
            'request_id' => Str::uuid(),
            'return_url' => config('app.web_url') . '/payment/airwallex/3ds-check',
            'payment_method' => [
                "type" => "card",
                "card" => [
                    "billing" => [
                        "address" => [
                            "state" => $billingAddressData['state'] ?? '',
                            "city" => $billingAddressData['city'] ?? '',
                            "country_code" => $countryCode,
                            "postcode" => $billingAddressData['zip'] ?? '',
                            "street" => $billingAddressData['address'] ?? '',
                        ],
                        "phone_number" => $billingAddressData['phone'] ?? '',
                        "email" => $email,
                        "first_name" => $billingAddressData['first_name'] ?? '',
                        "last_name" => $billingAddressData['last_name'] ?? '',
                    ],
                    ...$cardInfo,
                    'number_type' => 'PAN'
                ],
                'three_ds' => [
                    'return_url' => config('app.web_url') . '/payment/airwallex/3ds-check',
                ],
            ],
            'payment_method_options' => [
                "card" => [
                    "auto_capture" => true    // 自动确认
                ]
            ],
            'device_data' => [
                'device_id' => request()->header('device_id', '00000000-000000000000000'),
                'ip_address' => request()->ip(),
            ]
        ];
    }

    /**
     * 创建支付意向 - 订单
     * @param Order $order
     * @return array|mixed
     */
    public static function paymentIntentsCreate(Order $order): mixed
    {
        $path = '/api/v1/pa/payment_intents/create';
        $data = self::formatIntentsData($order);
        logger()->channel('payment')->warning("payment:airwallex:createIntent no:{$order->no} request:", $data);
        $response = AirwallexClient::getInstance()->post($path, $data);
        logger()->channel('payment')->warning("payment:airwallex:createIntent no:{$order->no} response:" . $response->body());
        return $response->json();
    }

    /**
     * 创建支付意向 - 购物车
     * @param Cart $cart
     * @return mixed
     */
    public static function paymentIntentsCreateForCart(Cart $cart): mixed
    {
        $path = '/api/v1/pa/payment_intents/create';
        $data = self::formatIntentsDataFromCart($cart);
        logger()->channel('payment')->warning("payment:airwallex:createIntent cartId:{$cart->id} request:", $data);
        $response = AirwallexClient::getInstance()->post($path, $data);
        logger()->channel('payment')->warning("payment:airwallex:createIntent cartId:{$cart->id} response:" . $response->body());
        return $response->json();
    }

    /**
     * 确认支付意向
     * @param $intentsId
     * @param $data
     * @return array|mixed
     */
    public static function paymentIntentsConfirm($intentsId, $data): mixed
    {
        $path = "/api/v1/pa/payment_intents/{$intentsId}/confirm";
        logger()->channel('payment')->warning("payment:airwallex:confirmIntent intentsId:{$intentsId} request:", $data);
        $response = AirwallexClient::getInstance()->post($path, $data);
        logger()->channel('payment')->warning("payment:airwallex:confirmIntent intentsId:{$intentsId} response:" . $response->body());
        return $response->json();
    }

    /**
     * 3ds继续支付
     * @param string $intentsId
     * @param string $threeDSMethodData
     * @return mixed|array
     */
    public static function paymentIntentsConfirmContinue(string $intentsId): mixed
    {
        $path = "/api/v1/pa/payment_intents/{$intentsId}/confirm_continue";
        $request_data = [
            'request_id' => Str::uuid(),
            'type' => '3ds_continue',
            'three_ds' => [
                "return_url" => config('app.web_url') .'/auth/3ds-check?intent_id='.$intentsId,
            ]
        ];
        logger()->channel('payment')->warning("payment:airwallex:3dsContinue intentsId:{$intentsId} request:", $request_data);
        $response = AirwallexClient::getInstance()->post($path, $request_data);
        logger()->channel('payment')->warning("payment:airwallex:3dsContinue intentsId:{$intentsId} response:" . $response->body());
        return $response->json();
    }
}
