<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\CmsArticle;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;
use App\Models\Enums\CmsTypeEnum;
use Illuminate\Validation\Rules\Enum;
use App\Models\CmsTag;

class CmsArticleController extends Controller
{

    public function index(Request $request): AnonymousResourceCollection
    {
        $validated = $request->validate([
            'filter.type' => ['required', 'integer', new Enum(CmsTypeEnum::class)],
            'filter.tag_id' => ['sometimes', 'integer'],
        ]);
        $CmsArticle = QueryBuilder::for(CmsArticle::class, $request)
            ->where('status', 1)
            ->select('id', 'title', 'sort', 'published_at', 'slug_title', 'cate_type', 'image_id', 'desc', 'meta_description', 'tag_ids')
            ->allowedFilters([
                AllowedFilter::exact('type'),
                AllowedFilter::callback('tag_id', function ($query, $value) {
                    $query->whereJsonContains('tag_ids', $value);
                }),
            ])
            ->with(['image:id,path,disk,module'])
            ->allowedSorts('sort', 'published_at')
            ->defaultSort('sort')
            ->orderBy('published_at', 'desc');
        if ($request->boolean('all', false)) {
            return JsonResource::collection($CmsArticle->get());
        }
        $res = $CmsArticle->paginate($this->getPerPage());

        return JsonResource::collection($res);
    }

    public function show(CmsArticle $CmsArticle): JsonResource
    {
        $CmsArticle->loadMissing([
            'image:id,path,disk,module',
            'adminUser:id,name'
        ]);
        //上一篇(sort 正序 published_at 降序)
        $prev = CmsArticle::where('sort', '<', $CmsArticle->sort)
            ->where(['status' => 1, 'type' => $CmsArticle->type])
            ->where(function ($query) use ($CmsArticle) {
                $query->where('sort', '<', $CmsArticle->sort)
                    ->orWhere(function ($q) use ($CmsArticle) {
                        $q->where('sort', $CmsArticle->sort)
                          ->where('published_at', '>', $CmsArticle->published_at)
                          ->where('id', '!=', $CmsArticle->id);
                    });
            })
            ->select('id', 'title', 'slug_title')
            ->orderBy('sort', 'desc')
            ->orderBy('published_at', 'asc')
            ->first();
        //下一篇(sort 正序 published_at 降序)
        $next = CmsArticle::where('sort', '>', $CmsArticle->sort)
            ->where(['status' => 1, 'type' => $CmsArticle->type])
            ->where(function ($query) use ($CmsArticle) {
                $query->where('sort', '>', $CmsArticle->sort)
                    ->orWhere(function ($q) use ($CmsArticle) {
                        $q->where('sort', $CmsArticle->sort)
                          ->where('published_at', '<', $CmsArticle->published_at)
                          ->where('id', '!=', $CmsArticle->id);
                    });
            })
            ->select('id', 'title', 'slug_title')
            ->orderBy('sort', 'asc')
            ->orderBy('published_at', 'desc')
            ->first();
        $CmsArticle->prev = $prev;
        $CmsArticle->next = $next;

        return JsonResource::make($CmsArticle);
    }

    /**
     * 获取所有标签
     */
    public function tags(Request $request): JsonResource
    {
        $query = QueryBuilder::for(CmsTag::class)->where('status', 1);
        return JsonResource::collection($query->get());
    }
}
