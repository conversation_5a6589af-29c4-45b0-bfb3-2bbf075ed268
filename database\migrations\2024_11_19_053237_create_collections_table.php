<?php

use App\Models\Enums\CollectionJumpTypeEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('collections', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('parent_id')->nullable()->comment('父级');
            $table->string('title', 128);
            $table->string('slug_title', 128)->index()->comment('slug标题');
            $table->integer('sort')->default(50);
            $table->bigInteger('image_id')->nullable()->comment('分类图');
            $table->bigInteger('image_banner_id')->nullable()->comment('banner位大图');
            $table->boolean('is_banner_hidden')->default(false)->comment('banner隐藏');
            $table->boolean('is_related')->default(0)->comment('展示再相关集合');
            $table->tinyInteger('jump_type')->default(CollectionJumpTypeEnum::Collection)->comment('跳转类型');
            $table->string('url')->nullable()->comment('链接');
            $table->boolean('active')->default(true)->comment('隐藏');
            $table->json('extra')->nullable()->comment('附加值: 例如配置样式 {"style":{xxx}}');
            $table->string('meta_title', 255)->nullable()->comment('meta标题');
            $table->string('meta_description', 512)->nullable()->comment('meta描述');
            $table->engine('InnoDB');
            $table->softDeletes();
            $table->comment('商品集合配置(导航栏) 对应ERP分类');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('collections');
    }
};
