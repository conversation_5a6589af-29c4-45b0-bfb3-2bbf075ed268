<?php

namespace App\Jobs;

use App\Models\SharingRule;
use App\Models\User\User;
use Arr;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class UserSharingRule implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 更新用户订单返现总计
     */
    public function __construct(public User $user)
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $user = $this->user;
 
        $rule = null;
        $rule = SharingRule::query()
            ->where('is_global', true)
            ->where('is_publish', true)
            ->where(function ($query) use ($user) {
                $query->where(function ($subQuery) use ($user) {
                    $subQuery->where(function ($subSubQuery) use ($user) {
                        if ($user->created_at) {
                            $subSubQuery->whereDate('effective_start_at', '<=', $user->created_at);
                        }
                        $subSubQuery->orWhereNull('effective_start_at');
                    });
                })
                    ->where(function ($subQuery) use ($user) {
                        $subQuery->where(function ($subSubQuery) use ($user) {
                            if ($user->created_at) {
                                $subSubQuery->whereDate('effective_end_at', '>=', $user->created_at);
                            }
                            $subSubQuery->orWhereNull('effective_end_at');
                        });
                    });
            })
            ->first();
        // 当前用户的粉丝默认使用全局规则
        $user->invitersRule()->associate($rule)->save();

        // 先查询父级用户是否有 inviters_rule_id
        if ($user->parent_id) {
            $parentUser = User::find($user->parent_id);
            $isInEffectivePeriod = false;
            if ($parentUser && $parentUser->inviters_rule_id) {
                // 获取父级规则
                $parentRule = SharingRule::find($parentUser->inviters_rule_id);
                // 判断父级规则是否存在且在有效期内
                if ($parentRule && $parentRule->is_publish) {
                    $isInEffectivePeriod = true;
                    // 检查开始时间
                    if ($parentRule->effective_start_at && $user->created_at) {
                        if ($parentRule->effective_start_at->greaterThan($user->created_at)) {
                            $isInEffectivePeriod = false;
                        }
                    }
                    // 检查结束时间
                    if ($isInEffectivePeriod && $parentRule->effective_end_at && $user->created_at) {
                        if ($parentRule->effective_end_at->lessThan($user->created_at)) {
                            $isInEffectivePeriod = false;
                        }
                    }
                    // 如果在有效期内，使用父级规则
                    if ($isInEffectivePeriod) {
                        $rule = $parentRule;
                    }
                }
            }
        }
        $user->sharingRule()->associate($rule)->save();
    }
}
