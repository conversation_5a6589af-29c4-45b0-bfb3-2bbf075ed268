<?php

namespace App\Services;

use App\Exceptions\DataException;
use App\Models\CartItem;
use App\Models\Coupon\Coupon;
use App\Models\Enums\Discount\DiscountAmountTypeEnum;
use App\Models\Enums\Discount\DiscountPriceTypeEnum;
use App\Models\Enums\Discount\DiscountTypeEnum;
use App\Models\Order\Order;
use App\Models\Order\OrderItem;
use App\Models\Product\ProductVariant;
use Illuminate\Database\Eloquent\Collection as ModelsCollection;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Throwable;

class OrderDiscountProcessor
{
    protected Order $order;
    protected Collection $rules;
    protected ModelsCollection $orderItems;
    protected Collection $newOrderItems;

    /**
     * @desc 此类用于计算(预处理)购物车优惠价以及保存方法
     * @param Coupon $coupon
     * @param Order $order
     */
    public function __construct(public Coupon $coupon, Order $order)
    {
        $this->order = clone $order;
        $this->init();
    }

    protected function init(): void
    {
        // 加载默认规则和命中商品
        $this->order->loadMissing(['items']);
        $this->coupon->loadMissing(['products:id']);
        $this->rules = collect($this->coupon->rules);
        $this->newOrderItems = collect();
        // 存在条件需要过滤商品
        if ($canProductIds = $this->coupon->products->pluck('id')->toArray()) {
            $this->orderItems = $this->order->items->filter(function ($item) use ($canProductIds) {
                if ($item->product instanceof ProductVariant) {
                    return in_array($item->product->product_id, $canProductIds);
                }
                return false;
            });
        } else {
            $this->orderItems = $this->order->items;
        }
    }

    /**
     * 计算结果更新到数据库
     * @return bool
     */
    public function updateOrder(): bool
    {
        try {
            DB::beginTransaction();
            // 运费需要保存这里
            if ($this->coupon->type == DiscountTypeEnum::FreeShippingPromotion) {
                $this->order->save();
            }
            $this->order->items()->saveMany($this->newOrderItems);
            $this->orderItems->map(function (OrderItem $item) {
                $item->num > 0 ? $item->save() : $item->delete();
            });
            DB::commit();
        } catch (Throwable $exception) {
            DB::rollBack();
            return false;
        }
        return true;
    }


    /**
     * 获取差价
     * @return float
     */
    public function getDiffOriginDiscount(): float
    {
        // 原价没有折扣
        $originPrice = $this->orderItems->sum(function (OrderItem $item) {
            return $item->getOriginal('num') * $item->getOriginal('price');
        });
        // 现价需要减掉折扣
        $nowPrice = $this->orderItems->sum(function (OrderItem $item) {
            return $item->num * $item->price - $item->coupon_discount_price;
        });
        // 有拆分的需要加上
        $nowPrice += $this->orderItems->sum(function (OrderItem $item) {
            return $item->num * $item->price - $item->coupon_discount_price;
        });
        return $originPrice - $nowPrice;
    }

    /**
     * 处理数据
     * @return $this
     */
    public function handle(): static
    {
        switch ($this->coupon->type) {
            case DiscountTypeEnum::QuantityBasedDiscount:
                // 基于数量享折扣 计算是否满足数量
                $priceKey = match ($this->coupon->price_type) {
                    DiscountPriceTypeEnum::Price => 'price',
                    DiscountPriceTypeEnum::OriginalPrice => 'original_price'
                };
                // 当前商品数量满足几个折扣
                $filterRules = $this->rules->where('quantity', '<=', $this->orderItems->sum('num'));
                // 一共多少个商品参与折扣
                $discountGoodsCount = $filterRules->max('quantity');
                // 按照价格降序
                $sortItems = $this->orderItems->sortByDesc($priceKey);
                // 开始处理商品，倒序
                do {
                    /**
                     * @var CartItem $item
                     */
                    $item = $sortItems->pop();
                    // 看似循环$item->num , 实则循环 $discountGoodsCount
                    for ($item->num; $item->num > 0;) {
                        // 复制一个新的商品 并绑定优惠券
                        $newItem = $item->replicate()->fill([
                            'price' => $item->{$priceKey},
                            'num' => 1,
                            'coupon_id' => $this->coupon->id,
                            'discount' => 0,
                        ]);
                        // 从最后拿奖励
                        if ($discount = $filterRules->pop()) {
                            $discountAmountType = DiscountAmountTypeEnum::tryFrom(Arr::get($discount, 'discount_amount_type'));
                            $newItem->coupon_discount_price = match ($discountAmountType) {
                                DiscountAmountTypeEnum::RandomAmount => 0,
                                DiscountAmountTypeEnum::PercentAmount => min(round($item->{$priceKey} * (100 - (float) Arr::get($discount, 'discount_rate', 0)) / 100, 4), Arr::get($discount, 'discount_max_price', 0)),
                                DiscountAmountTypeEnum::FixedAmount => Arr::get($discount, 'discount_price', 0),
                            };
                        }
                        // 保存
                        $this->newOrderItems->push($newItem);
                        $item->num--;
                        // 扣完商品结束
                        if (--$discountGoodsCount <= 0) {
                            break;
                        }
                    }
                } while ($discountGoodsCount);
                break;
            case DiscountTypeEnum::SpendBasedDiscount:
                // 基于数量享折扣 计算是否满足数量
                $priceKey = match ($this->coupon->price_type) {
                    DiscountPriceTypeEnum::Price => 'price',
                    DiscountPriceTypeEnum::OriginalPrice => 'original_price'
                };
                // 总价
                $totalPrice = $this->orderItems->sum(function (CartItem $item) use ($priceKey) {
                    return $item->{$priceKey} * $item->num;
                });
                // 命中的最后一条规则
                $maxRule = $this->rules->where('price', '<=', $totalPrice)->pop();
                $discountAmountType = DiscountAmountTypeEnum::tryFrom(Arr::get($maxRule, 'discount_amount_type'));
                // 处理2种情况, 第一种
                switch ($discountAmountType) {
                    // 固定金额 平均分配给每个商品
                    case  DiscountAmountTypeEnum::FixedAmount:
                        // 总优惠金额
                        $discountPrice = Arr::get($maxRule, 'discount_price');
                        // 当前剩余未用优惠金额
                        $notUsedDiscountPrice = Arr::get($maxRule, 'discount_price');
                        $totalCount = $this->orderItems->count();
                        // 折扣
                        $this->orderItems->each(function (CartItem $item, $index) use (&$notUsedDiscountPrice, $discountPrice, $totalPrice, $priceKey, $totalCount) {
                            if ($notUsedDiscountPrice) {
                                // 等比例分布优惠
                                $discount = bcmul(bcdiv($item->{$priceKey} * $item->num, $totalPrice, 8), $discountPrice, 2);
                                $discount = min($discount, $notUsedDiscountPrice);
                                if ($index + 1 == $totalCount) {
                                    $discount = $notUsedDiscountPrice;
                                }
                                $item->coupon_discount_price = $discount;
                                // 计算剩余
                                $notUsedDiscountPrice -= $discount;
                            }
                            // 绑定优惠券
                            $item->price = $item->{$priceKey};
                            $item->coupon_id = $this->coupon->id;
                        });
                        break;
                    // 固定折扣,每个商品折扣
                    case  DiscountAmountTypeEnum::PercentAmount:
                        $discountRate = Arr::get($maxRule, 'discount_rate');
                        // 折扣
                        $this->orderItems->each(function (CartItem $item, $index) use ($discountRate, $priceKey) {
                            // 绑定优惠券
                            $discount = bcdiv(bcmul(bcmul($item->{$priceKey}, $item->num, 8), $discountRate, 8), 100, 2);
                            $item->coupon_discount_price = $discount;
                            $item->price = $item->{$priceKey};
                            $item->coupon_id = $this->coupon->id;
                        });
                        break;
                }
                break;
            case DiscountTypeEnum::FreeShippingPromotion:
                // 基于价格享折扣 计算是否满足价格 基于运费也一样
                $priceKey = match ($this->coupon->price_type) {
                    DiscountPriceTypeEnum::Price => 'price',
                    DiscountPriceTypeEnum::OriginalPrice => 'original_price'
                };
                // 总价
                $totalPrice = $this->orderItems->sum(function (CartItem $item) use ($priceKey) {
                    return $item->{$priceKey} * $item->num;
                });
                // 命中的最后一条规则
                $maxRule = $this->rules->where('price', '<=', $totalPrice)
                    ->where('shipping_id', $this->order->shipping_id)->pop();
                // 直接满减运费
                if ($maxRule) {
                    $this->order->fill([
                        'shipping_fee_discount' => $this->order->shipping_fee,
                        'shipping_fee_discount_id' => $this->coupon->id,
                    ]);
                }
                break;
        }
        return $this;
    }


    /**
     * 是否可用验证
     * @return DataException|null
     */
    public function canUseValidate(): ?DataException
    {
        if (!$this->coupon->enabled) {
            return new DataException('Coupon not enabled.');
        }
        // 如果存在固定截止时间 并且有时间小于当前时间
        if ($this->coupon->effective_end_at && $this->coupon->effective_end_at->lte(now())) {
            return new DataException('This coupon has expired.');
        }

        switch ($this->coupon->type) {
            case DiscountTypeEnum::QuantityBasedDiscount:
                // 数量不满足使用条件
                if ($this->rules->min('quantity') > $this->orderItems->sum('num')) {
                    return new DataException('Coupon usage failed, did not reach the specified quantity.');
                }
                break;
            case DiscountTypeEnum::SpendBasedDiscount:
                $totalPrice = $this->orderItems->sum(function ($item) {
                    // 无条件约束
                    return match ($this->coupon->price_type) {
                        DiscountPriceTypeEnum::Price => $item->price * $item->num,
                        DiscountPriceTypeEnum::OriginalPrice => $item->original_price * $item->num,
                    };
                });
                // 价格不满足使用条件
                if ($this->rules->min('price') > $totalPrice) {
                    return new DataException('Coupon usage failed, did not reach the specified price.');
                }
            case DiscountTypeEnum::FreeShippingPromotion:
                $totalPrice = $this->orderItems->sum(function ($item) {
                    // 无条件约束
                    return match ($this->coupon->price_type) {
                        DiscountPriceTypeEnum::Price => $item->price * $item->num,
                        DiscountPriceTypeEnum::OriginalPrice => $item->original_price * $item->num,
                    };
                });
                // 价格以及运输方式不满足使用条件
                if ($this->rules->min('price') > $totalPrice ||
                    $this->rules->where('shipping_id', $this->order->shipping_id)->isEmpty()) {
                    return new DataException('Coupon usage failed, did not reach the specified price.');
                }

                break;
        }
        return null;
    }

}
