<?php

namespace App\Models\User;

use App\Models\Enums\User\WalletChangeTypeEnum;
use App\Models\MiddleModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class UserWalletInviteAmountRecord extends MiddleModel
{

    protected $guarded = [];

    protected $casts = [
        'current_amount' => 'float',
        'change_type' => WalletChangeTypeEnum::class,
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 变更所属模型来源
     * @return MorphTo
     */
    public function source(): MorphTo
    {
        return $this->morphTo();
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function wallet(): BelongsTo
    {
        return $this->belongsTo(UserWallet::class);
    }

}
