<?php

namespace App\Imports;

use App\Models\Attachment;
use App\Models\Comment;
use App\Models\Enums\Comment\CommentStatusEnum;
use App\Models\Product\Product;
use App\Models\Product\ProductVariant;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Arr;
use Illuminate\Validation\Rules\Enum;
use Illuminate\Validation\Rules\Exists;
use Maatwebsite\Excel\Concerns\RegistersEventListeners;
use Maatwebsite\Excel\Concerns\SkipsEmptyRows;
use Maatwebsite\Excel\Concerns\SkipsOnFailure;
use Maatwebsite\Excel\Concerns\ToArray;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Events\AfterImport;
use Maatwebsite\Excel\Events\BeforeImport;
use Maatwebsite\Excel\Imports\HeadingRowFormatter;
use Maatwebsite\Excel\Validators\Failure;

class CommentsImport implements ToArray, WithHeadingRow, WithEvents, WithValidation, SkipsEmptyRows, SkipsOnFailure
{
    use RegistersEventListeners;

    /**
     * @var \Illuminate\Database\Eloquent\Collection
     */
    protected Collection $projects;

    public static function beforeImport(BeforeImport $event): void
    {
        HeadingRowFormatter::extend('slug', function ($value, $key) {
            return Arr::get(static::$header, $value);
        });
        HeadingRowFormatter::default('slug');
    }

    public static function afterImport(AfterImport $event): void
    {
        HeadingRowFormatter::reset();
    }

    // 注意这里产品的sku和变体的sku是相反使用的
    public static array $header = [
        'sku' => 'spu',
        // 'spu' => 'sku',
        'first_name' => 'first_name',
        'last_name' => 'last_name',
        'email' => 'email',
        'content' => 'content',
        'grade' => 'grade',
        'status' => 'status',
        'enabled' => 'enabled',
        // 'top_at' => 'top_at',
        'create_time' => 'created_at',
        // 'images' => 'images',
    ];

    public function rules(): array
    {
        return [
            'spu' => ['required', new Exists(Product::class, 'spu')],
            // 'spu' => 'sku',
            'first_name' => ['required'],
            'last_name' => ['nullable'],
            'email' => ['nullable'],
            'content' => ['required'],
            'grade' => ['required'],
            'status' => ['required', 'int', new Enum(CommentStatusEnum::class)],
            'enabled' => ['required', 'bool'],
            // 'top_at' => 'top_at',
            'created_at' => ['required', 'date'],
            // 'images' => 'images',
        ];
    }

    public function array(array $array): array
    {
        // todo:: 优化（后期需要优化修改）
        $products = Product::query()->select(['id', 'spu'])->get();
        // $productVariants = ProductVariant::query()->select(['id', 'product_id', 'sku'])->get();
        $attachments = Attachment::query()->select('id')->get();

        $errors = [];
        foreach ($array as $key => $item) {
            $line = $key + 2;
            $comment = new Comment();
            if (!Arr::exists($item, 'spu') && !Arr::exists($item, 'sku')) {
               $errors[] ="{$line}行数据错误，sku或spu不能为空";
               continue;
            }

            try {
                $product_id = null;
                $product_variant_id = null;
                // 注意这里产品的sku和变体的sku是相反使用的
                if (Arr::exists($item, 'spu')) {
                    $product_id = $products->where('spu', Arr::get($item, 'spu'))->value('id');
                }
                // if (Arr::exists($item, 'sku')) {
                //     $product_variant_id = $productVariants->where('sku', Arr::get($item, 'sku'))->value('id');
                //     $product_id = $productVariants->where('sku', Arr::get($item, 'sku'))->value('product_id');
                // }
                // 判断产品数据是否存在
                if (!$product_id && !$product_variant_id) {
                    $errors[] ="{$line}行数据错误，sku或spu找不到对应的产品";
                    continue;
                }

                $data['product_id'] = (int) $product_id;
                // $data['product_variant_id'] = $product_variant_id;
                $data['first_name'] = Arr::get($item, 'first_name');
                $data['last_name'] = Arr::get($item, 'last_name');
                $data['email'] = Arr::get($item, 'email');
                $data['content'] = Arr::get($item, 'content');
                $data['grade'] = (float) Arr::get($item, 'grade');
                $data['status'] = (int) Arr::get($item, 'status');
                $data['enabled'] = (int) Arr::get($item, 'enabled');
                // $data['top_at'] = Arr::get($item, 'top_at');
                $data['created_at'] = Arr::get($item, 'created_at');
                $comment->fill($data)->save();
                // 图片
                if (Arr::exists($item, 'images')) {
                    $images = explode(',', Arr::get($item, 'images'));
                    $comment->images()->sync($attachments->whereIn('id', $images)->pluck('id')->toArray());
                }
            } catch (\Throwable $e) {
                $errors[] ="{$line}行数据错误，{$e->getMessage()}";
                continue;
            }
        }

        return $errors;
    }

    public function onFailure(Failure ...$failures)
    {
    }
}
