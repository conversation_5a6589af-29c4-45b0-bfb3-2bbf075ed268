<?php

namespace App\Admin\Controllers;

use App\Constants\Permissions;
use App\Http\Controllers\Controller;
use App\Models\Cart;
use App\Models\Enums\Order\OrderStatusEnum;
use App\Models\Order\Order;
use App\Models\Product\ProductVariant;
use DB;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rules\Enum;


class OrderController extends Controller
{

    public function __construct()
    {
        $this->hasPermissionOr(Permissions::OrdersUpdate)->only(['update']);
        $this->hasPermissionOr(Permissions::OrdersUpdate, Permissions::OrdersIndex)->only(['index', 'show', 'stat']);
    }

    /**
     * 订单列表
     * @param Request $request
     * @return AnonymousResourceCollection
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        /**
         * @var $user User|null
         */
        $builder = orderService()->getIndexQueryBuilder($request);

        $builder->with([
            'user:id,email,first_name,last_name',
            'billingAddress.country',
            'shippingAddress.country',
            'items.product' => function (MorphTo $query) {
                $query->morphWith([
                    ProductVariant::class => ['colorAttribute', 'sizeAttribute'],
                ]);
            },
            'items.userCoupon.coupon:id,name',
            'shipping'
        ]);

        return JsonResource::collection($builder->paginate($this->getPerPage()));
    }

    public function show(Order $order): JsonResource
    {
        $order->loadMissing([
            'items.product' => function (MorphTo $query) {
                $query->morphWith([
                    ProductVariant::class => ['colorAttribute', 'sizeAttribute'],
                ]);
            },
            'payment',
            'user',
            'billingAddress.country',
            'shippingAddress.country',
            'shipping',
            'histories'
        ]);
        // 客户订单总数
        if ($order->user) {
            $order->user->loadCount(['orders' => function ($q) {
                $q->where('status', OrderStatusEnum::Paid->value);
            }]);
            // 客户订单总金额
            $order->user->loadSum(['orders' => function ($q) {
                $q->where('status', OrderStatusEnum::Paid->value);
            }], 'total');
            // 四舍五入保留2位小数
            if (isset($order->user->orders_sum_total)) {
                $order->user->orders_sum_total = round($order->user->orders_sum_total, 2);
            }
        }

        return JsonResource::make($order);
    }

    /**
     * 修改订单
     * @param Request $request
     * @param Order $order
     * @return JsonResource
     */
    public function update(Request $request, Order $order): JsonResource
    {
        $update = $request->validate([
            'status' => [new Enum(OrderStatusEnum::class)],
            'remark' => ['string'],
        ]);

        $update && $order->update($update);
        return JsonResource::make($order);
    }

    // 统计
    public function stat(Request $request)
    {
        // 30天内的订单量
        $orderNum = Order::query()->whereDate('created_at', '>=', now()->subDays(30))->count();
        // 30天的订单总金额
        $orderTotalPrice = Order::query()
            ->whereDate('created_at', '>=', now()->subDays(30))
            ->sum('total');
        // 30天的订单平均金额
        $orderAvgPrice = (float) $orderNum ? $orderTotalPrice / $orderNum : 0;
        // 今天的订单量
        $todayOrderNum = Order::query()->whereDate('created_at', today())->count();
        // 30天内的购物车数量
        $cartNum = Cart::query()->whereDate('created_at', '>=', now()->subDays(30))->count();
        // 今天的购物车数量
        $todayCartNum = Cart::query()->whereDate('created_at', today())->count();
        // 今天购物车转化率
        $cartConversionRate = (float) $todayCartNum ? $todayOrderNum / $todayCartNum : 0;
        return JsonResource::make([
            'cartNum' => $cartNum,
            'cartConversionRate' => $cartConversionRate,
            'orderNum' => $orderNum,
            'orderTotalPrice' => $orderTotalPrice,
            'orderAvgPrice' => $orderAvgPrice,
        ]);
    }
}
