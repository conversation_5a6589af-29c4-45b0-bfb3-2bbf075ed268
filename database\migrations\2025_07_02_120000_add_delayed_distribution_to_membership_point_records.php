<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('membership_point_records', function (Blueprint $table) {
            $table->boolean('points_distributed')->default(false)->comment('积分是否已发放')->after('description');
            $table->timestamp('points_distributed_at')->nullable()->comment('积分发放时间')->after('points_distributed');
            $table->timestamp('scheduled_distribution_at')->nullable()->comment('计划发放时间')->after('points_distributed_at');
            
            $table->index(['points_distributed', 'scheduled_distribution_at'], 'idx_pending_distribution');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('membership_point_records', function (Blueprint $table) {
            $table->dropIndex('idx_pending_distribution');
            $table->dropColumn(['points_distributed', 'points_distributed_at', 'scheduled_distribution_at']);
        });
    }
};
