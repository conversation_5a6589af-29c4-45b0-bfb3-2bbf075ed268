<?php

namespace App\Jobs\Order;

use App\Models\Enums\Order\OrderStatusEnum;
use App\Models\Order\Order;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class OrderHistoryRecord implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 同步产品销量
     */
    public function __construct(public OrderStatusEnum $status, public Order $order)
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // 如果直接支付成功的订单，并且没有历史记录，则创建一条历史记录
        // if ($this->status == OrderStatusEnum::Paid && !$this->order->histories()->count()) {
        //     $this->order->histories()->create([
        //         'status' => OrderStatusEnum::Unpaid->value,
        //     ]);
        // }
        $this->order->histories()->create([
            'status' => $this->status->value,
        ]);
    }
}
